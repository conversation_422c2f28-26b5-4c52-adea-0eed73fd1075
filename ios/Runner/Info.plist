<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Cliente Minha Unimed</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>cliente_minha_unimed</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>vmcard</string>
		<string>tel</string>
		<string>mailto</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>pacsviewer.unimedfortaleza.com.br</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Permissão para Teleconsulta.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Uso do fone de ouvido bluetooth para teleconsultas</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Uso do fone de ouvido bluetooth para teleconsultas</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Queremos sua permissão para marcarmos, na sua agenda, suas consultas agendadas.</string>
	<key>NSCameraUsageDescription</key>
	<string>Abrir sua galeria para anexar fotos a sua solicitação de autorização. Abrirmos uma video chamada, para teleconsulta.</string>
	<key>NSContactsUsageDescription</key>
	<string>Permissão de contatos para a Teleconsulta.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Validar seu usuário pela biometria</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Precisamos da sua localizaçao para mostrarmos os médicos próximos a você</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Precisamos da sua localizaçao para mostrarmos os médicos próximos a você</string>
	<key>NSLocationUsageDescription</key>
	<string>Precisamos da sua localizaçao para mostrarmos os médicos próximos a você</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Precisamos da sua localizaçao para mostrarmos os médicos próximos a você</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Precisamos utilizar o seu microfone durante a teleconsulta.</string>
	<key>NSMotionUsageDescription</key>
	<string>Permissão para biometria facial</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Abrir sua galeria para anexar fotos a sua solicitação de autorização e teleconsulta.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Abrir sua galeria para anexar fotos a sua solicitação de autorização e teleconsulta.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Buscas no guia médico por voz</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
