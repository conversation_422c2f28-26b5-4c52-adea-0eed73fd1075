import UIKit
import Flutter
import GoogleMaps
import ScreenProtector<PERSON>it

@main
@objc class AppDelegate: FlutterAppDelegate {

  private lazy var screenProtectorKit = { return ScreenProtectorKit(window: window) }()

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    self.screenProtectorKit.configurePreventionScreenshot()
    self.screenProtectorKit.disablePreventScreenshot()

    let controller : FlutterViewController = self.window?.rootViewController as! FlutterViewController
    let generalChannel = FlutterMethodChannel(name: "cliente-unimed/general", binaryMessenger: controller.binaryMessenger)
        
    generalChannel.setMethodCallHandler({
        (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
        if call.method == "disableScreenshot" {
            self.screenProtectorKit.enabledPreventScreenshot()
        } else if call.method == "enableScreenshot" {
            self.screenProtectorKit.disablePreventScreenshot()
        }
    })

    GMSServices.provideAPIKey("AIzaSyAqbeA8rXJ43WoSIW_iro5JV6s_agk2EkA")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

}
