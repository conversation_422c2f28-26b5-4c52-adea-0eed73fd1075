# 🛠️ Integration Test App Cliente Unimed
  Para fazer começar  ira ser preciso ter o Ambiente de FLUTTER configurado na maquina.
  - [Configurando ambiente de desenvolvimento Flutter;](https://docs.flutter.dev/get-started/install)
  - [Guia com outro site para fazer configuração de ambiente FLUTTER + ANDROID STUDIO;](https://www.treinaweb.com.br/blog/configurando-ambiente-de-desenvolvimento-flutter)

## 📝 Começando

  - [Introdução a testes de integração no flutter;](https://docs.flutter.dev/cookbook/testing/integration/introduction)
  - [An<PERSON><PERSON><PERSON> de desempenho;](https://docs.flutter.dev/cookbook/testing/integration/profiling)

## Widgets de teste
  - [Uma introdução ao teste de widget;](https://docs.flutter.dev/cookbook/testing/widget/introduction)
  - [Find widgets;](https://docs.flutter.dev/cookbook/testing/widget/finders)
  - [Handle scrolling;](https://docs.flutter.dev/cookbook/testing/widget/scrolling)
  - [Tap, drag, and enter text;](https://docs.flutter.dev/cookbook/testing/widget/tap-drag)

#### 📁 **Screens**
  - screen: Onde se localiza todas as páginas com seus respectivos casos de teste.
  - test_cases: Os casos de teste das respectivas páginas

#### 📁 **Shared**
  - utils: Ideal para a criação de funções que serão compartilhadas em vários casos de teste, por exemplo a função de 'login_sucess' usado para realizar o login da aplicação.
  - messages: Ideal para centralizar mensagens que estão sendo usadas pela a validação dos testes, como por exemplo mensagens de informações, error ou validações.

#### 📁 **Redenrização de telas**
- **pumpAndSettle()** é um metodo para forçar o Flutter a renderizar todas as animações até o fim e, em seguida, verificando se o controlador de animação está concluído.
- Você também pode usar o método **pump()** do pacote integration_test para testar uma animação continua. No entanto, isso pode não ser suficiente se a animação for longa ou complexa. O método pumpAndSettle() garante que todas as animações sejam renderizadas até o fim, mesmo que sejam longas ou complexas.
- [Fonte;](https://blog.codeminer42.com/first-steps-through-the-flutter-framework/)

#### 📁 **FLUTTER DRIVER - VARIAVEIS DE AMBIENTE** 
- [Documentação](https://docs.flutter.dev/cookbook/testing/integration/introduction)
- Fazer a adição das variaveis de ambiente do **ANDROID_HOME** | **Plataform_tools** | **Tools**

Link video Windows: https://www.youtube.com/watch?v=yuKlc-a5z5k 

Link Video Linux: https://www.youtube.com/watch?v=_ZUIUaVBIao

## 📱 Emulador usado (Celular)

Nome | Resolução| Imagem
:--------- | :------ | :-------
-Pixel XL API 33 | 5.5 1440x2560  560dpi  | S Android 12
- Emulador de sua prefencia ou device fisico

## Versão da SDK usada no projeto 
- FLUTTER: 3.13.5
  
## 💻 Comandos
```dart
  //Executar todos os testes 
  flutter test integration_test/screens
```
```dart
  //Executar todos os casos teste de uma unica página (Login)
  flutter test integration_test/screens/login/
```

```dart
  //Executar somente um teste específico de uma página (Login)
  flutter test integration_test/screens/login/test_cases/login-sucess_test.dart
```

```dart
  //Comando para executar casos que precise de permissões(CAMERA, MICROFONE, ARQUIVO, ETC..)
  //Poderá ser executado no emulador, mas caso use camera o mais indicado é usar em device fisico o teste
  flutter driver --driver=test_driver/test_driver.dart --target=integration_test/screens/consultas-virtuais/teleconsulta-ais_sucess_test.dart
```

# 🛠️ Test Integrantion
  #### 📁 Lista de funcionalidades
  **Guia**
  - Feito ✅
  - Não Feito ❌

   **Cadastro** ✅ 
   -
     CPF já cadastrado  ✅
     Cancelar cadastro ✅
   
   **Login** ✅
   -
    Login Valido ✅
    Login Invalido (Usuario Invalido) ✅
    Login Invalido (Senha Invalida) ✅
    Login Nulo ✅
  
   **Financeiro** ✅
   -
    - Demonstrativo de Imposto de Renda ✅
    - Minhas Faturas ✅
    - Gerar Boleto ✅

   **Exames** 
   -
     Exame de imagem  Laudo ✅
     Exame Laboratorio laudo ✅
     Agendar Exame de imagem (Necessario ter guia do exame + agenda do exame aberta) ❌
  
   **Consultas** ✅
   -
    Verificação dos STATUS ✅
    Verificação de consulta sem dados ✅
    Validar termo de aceite de teleterapias ✅

  **Cartão Virtual** 
  -
    Frente e verso/ QR Code ✅
    Verificação de QR Code (TEXTO) ❌

**Autorizações** ✅
-
    Listagem de Solicitações e falar com atendente ✅
    Nova Solicitação sem anexo e fotos (ERROR) ✅
    Nova Solicitação com foto da camera  ✅ -> Executar em dispositivos fisicos -> Flutter Driver ✅

**Busca Avançada**
-
    Pesquisar por Médico - ✅
    Pesquisar por Clinica ❌

**Check-in Unidade**
-
    Check-in Emergência - ✅
    Check-in Laboratorio - ✅
    Check-in Cirurgia  - ❌
      - Sem agendamento pre-cirurgico ✅ -> Executar em dispositivos fisicos -> Flutter Driver
      - Com agendamento pre- cirurgico ❌ -> Executar em dispositivos fisicos -> Flutter Driver

  **Consultas Virtuais**
  -
    Pa Virtual Adulto - Faltando finalizar
    Teleconsulta Agendada(AIS) - ✅ -> Executar em dispositivos fisicos -> Flutter Driver
    Historico de Consultas ✅

  **Home App** ✅
  -
     Avaliação - ✅

  **Guia Médico** ✅
  -
     Buscar Médico ✅
     Prestadores Favoritos ✅
     Buscar Clinica ✅

**Agendamento Home App** ✅ - Executar com FLUTTER DRIVER
  -  
     Agendamento de consulta ✅
     Verificar especialidade já agendada ✅ 
```dart
 flutter driver --driver=test_driver/test_driver.dart --target=integration_test/screens/agendamento/agendamento-consulta-sucess_test.dart
```
     
**Perfil**
  -
     Trocar de Perfil ❌

**Meus Dados**
  -
     Trocar/Alterar Endereço ❌
     Adicionar Contato(Email,Telefone fixo, celular, etc.. ) ❌

**Central de Permissões**
  -
     Validar Permissoes - Permitindo com Clique ❌

  **Termo de Uso/Policita de privacidade**
  -
     Clicar no termo para validar se carrega ❌
     Clicar na policita de privacidade para validar se carrega ❌
  
  **Logout** ✅
  -
     Sair da conta ✅
  

     




 
