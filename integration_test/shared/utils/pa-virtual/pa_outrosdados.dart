import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../text/messages_validation.dart';
import 'app_tutorial.dart';

Future<Null> paOutrosDados(WidgetTester tester) async {
  final Finder btnOutrosDados = find.text('OUTROS DADOS');
  await tester.tap(btnOutrosDados);
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);
  await tester.pumpAndSettle(const Duration(seconds: 2));

  final Finder textFieldPeso = find.byKey(const Key('keyPeso'));
  await tester.enterText(textFieldPeso, '100');
  await tester.pumpAndSettle();

  final Finder textFieldAltura = find.byKey(const Key('keyAltura'));
  await tester.enterText(textFieldAltura, '1,75');
  await tester.pumpAndSettle();

  final Finder checkboxImc = find.text(checkboxIMC);
  await tester.tap(checkboxImc);
  await tester.pumpAndSettle();
}
