import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'app_tutorial.dart';

Future<Null> pavirtualFluxo(WidgetTester tester) async {
  final Finder btnConsultasVirtuais =
      find.byKey(const Key('consultasVirtuais'));
  await tester.tap(btnConsultasVirtuais);
  await tester.pumpAndSettle();

  final Finder btnPaVirtual = find.text('PRONTO ATENDIMENTO');
  await tester.tap(btnPaVirtual);
  await tester.pumpAndSettle();

  final Finder btnPa = find.text('Pronto Atendimento');
  await tester.tap(btnPa);
  await tester.pumpAndSettle();

  final Finder btnPopup = find.text('Sim');
  await tester.tap(btnPopup);
  await tester.pumpAndSettle();

  final Finder btnPrivacidade = find.text('Ok');
  await tester.tap(btnPrivacidade);
  await tester.pumpAndSettle();

  final Finder checkbox1 = find.byType(Checkbox).first;
  await tester.tap(checkbox1);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnContinuar = find.text('CONTINUAR');
  await tester.tap(btnContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await tester.tap(btnContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder checkbox2 = find.byType(Checkbox).last;
  await tester.tap(checkbox1);
  await tester.pumpAndSettle();
  await tester.tap(checkbox2);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnMeusDados = find.text('MEUS DADOS');
  await tester.tap(btnMeusDados);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnOutrosDados = find.text('OUTROS DADOS');
  await tester.tap(btnOutrosDados);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder textFieldPeso = find.byKey(const Key('keyPeso'));
  await tester.enterText(textFieldPeso, '100');
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  //TODO: Verificar no package do PA Virtual para add keys na parte do IMC e CHECKBOX
  final Finder textFieldAltura = find.byKey(const Key('keyAltura'));
  await tester.enterText(textFieldAltura, '1,75');
  await tester.testTextInput.receiveAction(TextInputAction.done);
  await tester.pumpAndSettle();

  final Finder checkboxImc1 = find.byKey(const Key('checkboxMedicamentos_nao'));
  await tester.tap(checkboxImc1);
  await tester.pumpAndSettle();

  final Finder checkboxImc2 =
      find.byKey(const Key('checkboxMedicamentoContinuo_nao'));
  await tester.tap(checkboxImc2);
  await tester.pumpAndSettle();

  await tester.tap(btnContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);

  await tester.tap(btnContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  await clienteAppTutorial(tester);

  final Finder btnSintomas = find.text('Dificuldade de Urinar');
  await tester.tap(btnSintomas);
  await tester.pumpAndSettle();

  final Finder btnSelectDores = find.text('SELECIONAR DORES');
  await tester.tap(btnSelectDores);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnDores = find.text('Garganta');
  await tester.tap(btnDores);
  await tester.pumpAndSettle();

  await tester.tap(btnContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnNao = find.text('Não');
  await tester.tap(btnNao);
  await tester.pumpAndSettle();

  final Finder btnBiometiaContinuar = find.text('Continuar');
  await tester.tap(btnBiometiaContinuar);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();

  final Finder btnTextBiometria =
      find.text('Biometria Facial - Unimed Fortaleza');
  await tester.longPress(btnTextBiometria);
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
  await tester.pumpAndSettle();
}
