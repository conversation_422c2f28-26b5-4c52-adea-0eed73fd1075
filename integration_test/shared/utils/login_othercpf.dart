import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'initial_team_for_animation.dart';

Future<Null> loginCPF(WidgetTester tester, String cpf, String senha) async {
  await initialTeamForAnimation(tester);

  //Digitar usuário com cpf valido
  final Finder textFieldUsername = find.byKey(const Key('tfLogin'));
  await tester.enterText(textFieldUsername, cpf);
  await tester.pumpAndSettle();

  //Digiar senha de acesso
  final Finder textFieldPassword = find.byKey(const Key('tfPassword'));
  await tester.enterText(textFieldPassword, senha);
  await tester.pumpAndSettle();

  //Clicar no botão de login
  final Finder buttonLogin = find.byKey(Key('btnLogin'));
  await tester.tap(buttonLogin);
  await tester.pumpAndSettle();

  await Future.delayed(Duration(milliseconds: 2000));
  await tester.pumpAndSettle();
}
