import 'package:flutter_test/flutter_test.dart';

const duration = Duration(seconds: 3);

Future<Null> appPermissions(WidgetTester tester) async {
  final Finder btnCentralPermissoes = find.text('Central de Permissões');
  await tester.tap(btnCentralPermissoes);
  await tester.pumpAndSettle();

  final Finder btnPermissionMicrofone =
      find.text('PERMITIR O USO DO MICROFONE');
  await tester.tap(btnPermissionMicrofone);
  await tester.pumpAndSettle(duration);

  final Finder btnPermissionCamera = find.text('PERMITIR O USO DA CÂMERA');
  await tester.tap(btnPermissionCamera);
  await tester.pumpAndSettle(duration);

  final Finder btnPermissonBluetooth = find.text('PERMITIR O USO DO BLUETOOTH');
  await tester.tap(btnPermissonBluetooth);
  await tester.pumpAndSettle(duration);
}
