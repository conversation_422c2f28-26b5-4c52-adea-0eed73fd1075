import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:cliente_minha_unimed/main.dart' as app;

import '../../shared/utils/login_othercpf.dart';

void main() {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  testWidgets('GUIA MÉDICO - Pesquisar Prestador', (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '88229513368', '123456');
    await tester.pumpAndSettle();

    final Finder textFieldGuiaMedico = find.byKey(const Key('HomeHeader'));
    await tester.enterText(textFieldGuia<PERSON><PERSON><PERSON>, '<PERSON><PERSON>');
    await tester.pumpAndSettle();

    final Finder btnGuiaMedico = find.byKey(const Key('btnGuiaMedico'));
    await tester.tap(btnGuiaMedico);
    await tester.pumpAndSettle();

    expect(find.text('OFTALMOLOGIA'), findsWidgets);
  });
}
