import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:cliente_minha_unimed/main.dart' as app;

import '../../shared/utils/login_othercpf.dart';

void main() {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  testWidgets('Botão perfil - Verificar prestador favoritos',
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '19467273349', '123456');
    await tester.pumpAndSettle();

    final Finder btnStarsHome = find.byKey(const Key('btnStars'));
    await tester.tap(btnStarsHome);
    await tester.pumpAndSettle();

    expect(find.text('OFTALMOLOGIA'), findsWidgets);
  });
}
