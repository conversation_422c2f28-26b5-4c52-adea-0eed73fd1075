import 'package:cliente_minha_unimed/main.dart' as app;
import "package:flutter/material.dart";
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../shared/utils/initial_team_for_animation.dart';

const String login = '19467273349';
const String password = '123456';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets(
    'Teste realizar login com as credenciais corretas',
    (WidgetTester tester) async {
      await app.main();
      await tester.pumpAndSettle();

      await initialTeamForAnimation(tester);
      await tester.pumpAndSettle();

      final Finder textFieldUsername = find.byKey(const Key('tfLogin'));
      await tester.enterText(textFieldUsername, login);
      await tester.pumpAndSettle();

      final Finder textFieldPassword = find.byKey(const Key('tfPassword'));
      await tester.enterText(textFieldPassword, password);
      await tester.pumpAndSettle();

      final Finder buttonLogin = find.byKey(Key('btnLogin'));
      await tester.tap(buttonLogin);
      await tester.pumpAndSettle();

      await Future.delayed(Duration(milliseconds: 2000));
      await tester.pumpAndSettle();

      final Finder messageScreen = find.text('Olá NASSER!');
      expect(messageScreen, findsOneWidget);
    },
  );
}
