import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:cliente_minha_unimed/main.dart' as app;

import '../../shared/utils/login_othercpf.dart';

void main() {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  testWidgets(
    'Integrantion Test - Geração de boleto',
    (WidgetTester tester) async {
      await app.main();
      await tester.pumpAndSettle();

      //Inserir um CPF que tenha boleto para gerar
      await loginCPF(tester, '19467273349', '123456');
      await tester.pumpAndSettle();

      final Finder buttonFinanceiro = find.text('Financeiro');
      await tester.tap(buttonFinanceiro);
      await tester.pumpAndSettle();

      final Finder btnMinhasFaturas = find.text('Minhas Faturas');
      await tester.tap(btnMinhasFaturas);
      await tester.pumpAndSettle();

      final Finder btnGerar = find.text('GERAR');
      await tester.tap(btnGerar);
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();

      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle();

      expect(find.text('Protocolo gerado com sucesso'), findsOneWidget);

      final Finder btnFechar = find.text('Fechar');
      await tester.tap(btnFechar);
      await tester.pumpAndSettle();
    },
  );
}
