import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:cliente_minha_unimed/main.dart' as app;

import '../shared/utils/login_othercpf.dart';

double stars = -1;

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Teste de Avaliação HOME Aplicativo",
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '19467273349', '123456');
    await tester.pumpAndSettle();

    final Finder btnAvaliar = find.text('Avaliar');
    await tester.tap(btnAvaliar);
    await tester.pumpAndSettle();

    final Finder btnStars = find.byKey(const Key('keyStars')).first;
    await tester.tap(btnStars);
    await tester.pumpAndSettle();
  });
}
