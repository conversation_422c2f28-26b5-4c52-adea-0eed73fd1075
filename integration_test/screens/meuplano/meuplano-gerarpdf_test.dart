import 'package:cliente_minha_unimed/main.dart' as app;
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../shared/utils/initial_team_for_animation.dart';
import '../../shared/utils/login_othercpf.dart';

void main() async {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  testWidgets("Meu Plano -  Gerar PDF Meu Plano", (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await initialTeamForAnimation(tester);
    await tester.pumpAndSettle();

    await loginCPF(tester, '54749212334', '123456');
    await tester.pumpAndSettle();

    //Meu PLano
    final Finder btnMeuPlano = find.text('Meu Plano');
    await tester.tap(btnMeuPlano);
    await tester.pumpAndSettle();
    await tester.pumpAndSettle();

    //Gerar PDF
    final Finder btnGerar = find.text('Gerar PDF');
    await tester.tap(btnGerar);
    await tester.pumpAndSettle();
    await Future.delayed(Duration(seconds: 5));
    await tester.pumpAndSettle();
    await tester.pumpAndSettle();

    final Finder messageProtocolo = find.text('Protocolo gerado com sucesso');
    expect(messageProtocolo, findsOneWidget);
    await tester.pumpAndSettle();

    final Finder buttonFechar = find.text('Fechar');
    await tester.tap(buttonFechar);
    await tester.pumpAndSettle();
    await Future.delayed(Duration(seconds: 3));
    await tester.pumpAndSettle();

    final backButton = find.byTooltip('Back');
    await tester.tap(backButton);
  });
}
