import 'package:cliente_minha_unimed/main.dart' as app;

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../shared/utils/login_othercpf.dart';

void main() {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;
  testWidgets('Meu Plano - Gerar Declaração de Tempo De Contrato',
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '54749212334', '123456');
    await tester.pumpAndSettle();

    final Finder buttonMeuPlano = find.text('Meu Plano');
    await tester.tap(buttonMeuPlano);
    await tester.pumpAndSettle();

    final Finder btnGerar = find.text('<PERSON>era<PERSON> Tempo de Contrato');
    await tester.tap(btnGerar);
    await tester.pumpAndSettle();
    //await tester.tap(find.byKey(const Key('bntGerar')));
    await Future.delayed(Duration(seconds: 5));
    await tester.pumpAndSettle();
    await tester.pumpAndSettle();

    final Finder messageProtocolo = find.text('Protocolo gerado com sucesso');
    expect(messageProtocolo, findsOneWidget);
    await tester.pumpAndSettle();

    final Finder buttonFechar = find.text('Fechar');
    await tester.tap(buttonFechar);
    await tester.pumpAndSettle();
  });
}
