import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:cliente_minha_unimed/main.dart' as app;

import '../../shared/utils/login_othercpf.dart';

//Caso quereira outra ESPECIALIDADE só mudar o texto
final especialidade = find.text('MEDICO DE FAMILIA');



Future<void> fecharPopups(WidgetTester tester) async {
  // Lista de textos comuns em popups
  final List<String> botoesPopup = [
    'Cancelar',
    'Fechar',
    'OK',
    'Entendi',
    'Voltar',
    'N<PERSON>',
    '<PERSON>char aviso',
  ];

  bool encontrou;
  do {
    encontrou = false;
    for (final texto in botoesPopup) {
      final Finder botao = find.text(texto);
      if (await tester.pumpAndSettle() == 0) break;
      if (botao.evaluate().isNotEmpty) {
        await tester.tap(botao);
        await tester.pumpAndSettle();
        encontrou = true;
      }
    }
  } while (encontrou);
}




void main() async {
  final IntegrationTestWidgetsFlutterBinding binding =
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  binding.framePolicy = LiveTestWidgetsFlutterBindingFramePolicy.fullyLive;

  testWidgets('HOME - Agendamento de Teleconsulta - Já agendado',
      (WidgetTester tester) async {
    app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '60866109323', '123456');
    await tester.pumpAndSettle();

    await fecharPopups(tester);

    final Finder btnAgendemnto = find.text('Agende a sua consulta');
    await tester.tap(btnAgendemnto);
    await tester.pumpAndSettle();

    final Finder dropdownSelect = find.text('Escolha uma especialidade');
    await tester.tap(dropdownSelect);
    await tester.pumpAndSettle();

    final Finder selectEspecialidade = especialidade;
    await tester.tap(selectEspecialidade);
    await tester.pumpAndSettle();

    final Finder btnContinuar = find.text('CONTINUAR');
    await tester.tap(btnContinuar);
    await tester.pumpAndSettle();

    final Finder messageScreen = find.text(
        'Agendamento não permitido. O beneficiário informado já possui consulta(s) em aberto nesta especialidade.');
    expect(messageScreen, findsOneWidget);

    final Finder btnTeleconsultaIr = find.text('IR PARA TELECONSULTA');
    await tester.tap(btnTeleconsultaIr);
    await tester.pumpAndSettle();
  });
}
