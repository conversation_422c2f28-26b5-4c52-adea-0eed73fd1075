import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_state.dart';
import 'package:cliente_minha_unimed/models/document-required.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-surgery/checkin_surgery.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';

const int SIZE_FILE_BYTE = 5000000;
const String SIZE_FILE_STRING = "5Mb"; //5Mb

const String loadingSendMessage = 'Enviando...';
const String removingMessage = 'Removendo...';
const String loadingMessage = 'Carregando...';

class AuthorizationDocumentsBloc
    extends Bloc<AuthorizationDocumentsEvent, AuthorizationDocumentsState> {
  AuthorizationDocumentsBloc() : super(InitialState());

  final logger = UnimedLogger(className: 'AuthorizationDocumentsBloc');

  List<DocumentRequired> _attachments =
      List<DocumentRequired>.empty(growable: true);

  List<DocumentRequired> get attachments => _attachments;

  int _maxFilesAttachments = 10;
  int get maxFilesAttachments => _maxFilesAttachments;

  List<String> _formatFilesAllowed = ['png', 'jpg', 'jpeg', 'pdf'];
  get formatFilesAllowed => _formatFilesAllowed;

  @override
  Stream<AuthorizationDocumentsState> mapEventToState(
    AuthorizationDocumentsEvent event,
  ) async* {
    if (event is AttachFile) {
      yield* _handleAttachFile(event);
    } else if (event is AttachFiles) {
      yield LoadingState();

      _attachments = event.files;

      yield DoneState(_attachments);
    } else if (event is RemoveAttachedFile) {
      yield* _handleRemoveAttachedFile(event);
    } else if (event is EditAttachedFile) {
      yield* _handleEditAttachedFile(event);
    } else if (event is StopAttachedFile) {
      yield DoneState(_attachments);
    } else if (event is LoadAttachedFiles) {
      _attachments.clear();
      yield LoadingState();
      //yield LoadingDocumentState(index: 0, message: loadingMessage);

      try {
        //final api = Locator.instance.get<CheckinSurgeryApi>();

        event.attachment.forEach((element) {
          _attachments.add(
              DocumentRequired(required: true, name: element, id: element));
        });
        //_attachments = await api.getDocumentsRequiredList(card: event.card);
        //_attachments = List<DocumentRequired>.empty(growable: true);
        //_attachments.sort((a, b) => a.required ?? false ? -1 : 1);

        // final documents = await api.getDocuments(
        //   cdAgeCir: event.cdAgeCir,
        // );
        yield DoneState(_attachments);

        // if (documents.length > 0) {
        //   final statusDocuments = await Locator.instance
        //       .get<CheckinSurgeryApi>()
        //       .getStatusDocuments(cdAgeCir: event.cdAgeCir);

        //   for (int index = 0; index < documents.length; index++) {
        //     yield LoadingDocumentState(index: index, message: loadingMessage);
        //     // _attachments[index].name = null;

        //     final document = await api.getDocumentBase64(
        //       chave: documents[index].chave!,
        //     );

        //     String dir = (await getApplicationDocumentsDirectory()).path;
        //     File file = File("$dir/" + document.nomeArquivo!);
        //     await file.writeAsBytes(base64.decode(document.documentoBase64!));

        //     File? compressedFile;
        //     if (!document.nomeArquivo!.contains(".pdf"))
        //       compressedFile = await FileUtils.getCompressedFile(file);

        //     final statusDocument = statusDocuments.firstWhere(
        //         (element) => element.chave == documents[index].chave,
        //         orElse: () => CheckinSurgeryStatusDocument());
        //     _attachments
        //             .firstWhere((attachment) => documents[index]
        //                 .nomeArquivo!
        //                 .startsWith(_typeToNameFile(attachment.name!)))
        //             .attachment =
        //         FileAttach(
        //             file: file,
        //             chave: document.chave,
        //             name: document.nomeArquivo!,
        //             thumbnail: compressedFile,
        //             sended: true,
        //             status: statusDocument.motivo == "PENDENTE"
        //                 ? "PENDENTE"
        //                 : statusDocument.aprovado);

        //     yield DoneState(_attachments);
        //   }
        // }
      } catch (e) {
        logger
            .e("AuthorizationDocumentsBloc Error on Loading File Attachment$e");
        yield ErrorFileLoadingState(message: MessageException.GENERAL);
      }
    } else if (event is RemoveAllAttachedFiles) {
      _attachments.forEach((element) {
        FileUtils.deleteFile(element.attachment!.file);
        FileUtils.deleteFile(element.attachment!.thumbnail!);
      });

      _attachments = List<DocumentRequired>.empty(growable: true);
      yield DoneState([]);
    } else if (event is SendAttachedFiles) {
      // final api = Locator.instance.get<AutorizacoesApi>();
      int count = 0;
      int filesSended = 0;

      final sendAttachments =
          _attachments.where((element) => element.attachment != null);

      for (DocumentRequired file in sendAttachments) {
        try {
          yield LoadingDocumentState(
              index: count,
              message:
                  "$loadingSendMessage (${count + 1}/${sendAttachments.length})");

          filesSended++;

          yield FileSendedState(index: count);
          count++;
        } catch (e) {
          file.attachment!.sended = false;
          logger
              .e("AuthorizationDocumentsBloc Error on Send File Attachment$e");
          yield ErrorSendedState(index: count);
        }
      }
      if (filesSended == sendAttachments.length)
        yield FinishSendAllFileState(attachments: _attachments);
      else {
        yield DoneState(_attachments);
      }
    }
  }

  bool _fileIsNotAllow(filename) {
    try {
      final List<String> fileparts = filename.split('.');
      final String fileExtension = fileparts.last.toLowerCase();

      return !_formatFilesAllowed.contains(fileExtension);
    } catch (e) {
      logger.e(
          "AuthorizationDocumentsBloc Error on File Attachment validation $e");

      return true;
    }
  }

  String _typeToNameFile(String type) {
    String label = type.replaceAll('"', "").replaceAll(" ", "");
    //o nome teste covid tem uma string mt grande e o serviço tem limite, entao reduzimos
    if (label.startsWith("Teste")) {
      label = "testecovid";
    }

    return label;
  }

  Stream<AuthorizationDocumentsState> _handleAttachFile(
      AttachFile event) async* {
    // final df = new DateFormat('dd_MM_yyyy_hh_mm_ss_SSS');
    // String name =
    //     "${event.card}_${df.format(new DateTime.now())}${event.file.path.substring(event.file.path.lastIndexOf('.'))}";
    String type = _typeToNameFile(event.type!);

    String name =
        "${type}_${event.id}${event.file.path.substring(event.file.path.lastIndexOf('.'))}";
    debugPrint("$name");
    final fileSize = await event.file.length();

    if (fileSize > SIZE_FILE_BYTE) {
      yield AutorizacaoAttachmentErrorState(
          message: 'O tamanho de arquivo máximo permitido é $SIZE_FILE_STRING');

      return;
    }

    if (_attachments.length >= _maxFilesAttachments) {
      yield AutorizacaoAttachmentErrorState(
          message:
              'Você pode anexar no máximo $_maxFilesAttachments imagens por solicitação.');

      return;
    } else if (_fileIsNotAllow(name)) {
      yield AutorizacaoAttachmentErrorState(
          message: 'O formato do arquivo $name não é permitido');

      return;
    }

    yield LoadingState();
    final compressedFile = await FileUtils.getCompressedFile(event.file);
    _attachments[event.index].attachment =
        FileAttach(file: event.file, name: name, thumbnail: compressedFile);

    yield DoneState(_attachments);
  }

  Stream<AuthorizationDocumentsState> _handleRemoveAttachedFile(
      RemoveAttachedFile event) async* {
    yield LoadingDocumentState(index: event.index, message: removingMessage);

    try {
      if (event.chave != null) {
        final api = Locator.instance.get<CheckinSurgeryApi>();
        await api.removeDocument(
          chave: event.chave!,
        );
      }

      _attachments[event.index].attachment = null;

      yield DoneState(_attachments);
    } catch (e) {
      logger.e("AuthorizationDocumentsBloc Error on Remove File Attachment$e");
      yield ErrorState(e.toString());
    }
  }

  Stream<AuthorizationDocumentsState> _handleEditAttachedFile(
      EditAttachedFile event) async* {
    yield LoadingDocumentState(index: event.index, message: removingMessage);

    try {
      if (event.chave != null) {
        final api = Locator.instance.get<CheckinSurgeryApi>();
        await api.removeDocument(
          chave: event.chave!,
        );
      }

      _attachments.elementAt(event.index).attachment!.sended = null;

      String type = _typeToNameFile(event.type!);

      String name =
          "${type}_${event.id}${event.file.path.substring(event.file.path.lastIndexOf('.'))}";
      debugPrint("$name");

      final compressedFile = await FileUtils.getCompressedFile(event.newFile);

      _attachments[event.index].attachment =
          FileAttach(file: event.file, name: name, thumbnail: compressedFile);
      // _attachments
      //     .elementAt(event.index)
      //     .attachment!
      //     .setFile(event.newFile, compressedFile);

      yield DoneState(_attachments);
    } catch (e) {
      logger.e("AuthorizationDocumentsBloc Error on Edit File Attachment $e");
      yield ErrorState(e.toString());
    }
  }
}
