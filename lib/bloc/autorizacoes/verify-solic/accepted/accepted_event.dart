import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AcceptedEvent extends Equatable {
  const AcceptedEvent();

  @override
  List<Object?> get props => [];
}

class AcceptRequest extends AcceptedEvent {
  final EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao;
  @override
  List<Object?> get props => [envioSolicitacaoAutorizacao];

  AcceptRequest({this.envioSolicitacaoAutorizacao});
}

class CleanRequest extends AcceptedEvent {
  @override
  List<Object> get props => [];
}
