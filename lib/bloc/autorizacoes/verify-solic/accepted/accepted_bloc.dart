import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_state.dart';
import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:cliente_minha_unimed/shared/services/authorization.service.dart';

import 'package:flutter/material.dart';

class AcceptedSolicBloc extends Bloc<AcceptedEvent, AcceptedState> {
  AcceptedSolicBloc() : super(AcceptedInitial());

  EnvioSolicitacaoAutorizacao? _envioSolicitacaoAutorizacao =
      EnvioSolicitacaoAutorizacao();

  EnvioSolicitacaoAutorizacao? get envioSolicitacaoAutorizacao =>
      _envioSolicitacaoAutorizacao;

  @override
  Stream<AcceptedState> mapEventToState(
    AcceptedEvent event,
  ) async* {
    if (event is AcceptRequest) {
      debugPrint("======== TO AQUI ACCEPTREQUEST");
      debugPrint("======== ${event.envioSolicitacaoAutorizacao!.telefone}");
      _envioSolicitacaoAutorizacao = event.envioSolicitacaoAutorizacao;
      yield RequestAccepted(
        envioSolicitacaoAutorizacao: event.envioSolicitacaoAutorizacao,
      );
    } else if (event is CleanRequest) {
      AuthorizationService.deleteAutorizationData();
      _envioSolicitacaoAutorizacao = EnvioSolicitacaoAutorizacao();
      yield NoRequestState();
    }
  }
}
