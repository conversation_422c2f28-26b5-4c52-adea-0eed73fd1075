import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';

@immutable
abstract class AutorizacaoAttachmentsState extends Equatable {}

class InitialState extends AutorizacaoAttachmentsState {
  @override
  List<Object> get props => [];
}

class LoadingState extends AutorizacaoAttachmentsState {
  @override
  List<Object> get props => [];
}

class ErrorState extends AutorizacaoAttachmentsState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends AutorizacaoAttachmentsState {
  final List<FileAttach>? attachments;

  @override
  List<Object?> get props => [attachments];

  DoneState(this.attachments);
}

class AutorizacaoAttachmentErrorState extends AutorizacaoAttachmentsState {
  final message;
  AutorizacaoAttachmentErrorState({required this.message});
  @override
  List<Object> get props => [this.message];
}
