import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AutorizacoesEnvioState extends Equatable {}

class AutorizacoesEnvioDone2State extends AutorizacoesEnvioState {
  @override
  List<Object> get props => [];
}

class AutorizacoesEnvioInitialState extends AutorizacoesEnvioState {
  @override
  List<Object> get props => [];
}

class AutorizacoesEnvioLoadingState extends AutorizacoesEnvioState {
  @override
  List<Object> get props => [];
}

class AutorizacoesEnvioErrorState extends AutorizacoesEnvioState {
  final String message;

  @override
  List<Object> get props => [message];

  AutorizacoesEnvioErrorState(this.message);
}

class AutorizacoesEnvioDoneState extends AutorizacoesEnvioState {
  final RetornoSolicitacaoAutorizacao retorno;

  @override
  List<Object> get props => [retorno];

  AutorizacoesEnvioDoneState(this.retorno);
}
