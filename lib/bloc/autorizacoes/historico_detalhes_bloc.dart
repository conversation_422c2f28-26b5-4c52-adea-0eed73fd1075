import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/historico_detalhes_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/historico_detalhes_state.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class AutorizacoesHistoricoDetalhesBloc extends Bloc<
    AutorizacoesHistoricoDetalhesEvent, AutorizacoesHistoricoDetalhesState> {
  AutorizacoesHistoricoDetalhesBloc() : super(InitialDetalhesState());
  final logger = UnimedLogger(className: 'AutorizacoesHistoricoDetalhesBloc');

  @override
  Stream<AutorizacoesHistoricoDetalhesState> mapEventToState(
    AutorizacoesHistoricoDetalhesEvent event,
  ) async* {
    if (event is LoadDetalhes) {
      yield LoadingDetalhesState(event.solicitacao);

      logger.d('solicitacao: ${event.solicitacao.protocolo}');


    } else if (event is ClearDetalhes) {
      yield InitialDetalhesState();
    }
  }
}
