import 'package:cliente_minha_unimed/models/beneficiario-data.dart';
import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AutorizacoesEnvioEvent extends Equatable {}

class LoadButton extends AutorizacoesEnvioEvent {
  List<Object> get props => [];
}

class Enviar extends AutorizacoesEnvioEvent {
  final EnvioSolicitacaoAutorizacao dao;
  final context;
  final BeneficiarioData? beneficiarioData;
  final String cpfLogin;
  @override
  List<Object?> get props => [dao, beneficiarioData];

  Enviar(
      {required this.dao,
      this.context,
      this.beneficiarioData,
      required this.cpfLogin});
}

class Inicio extends AutorizacoesEnvioEvent {
  @override
  List<Object> get props => [];
}
