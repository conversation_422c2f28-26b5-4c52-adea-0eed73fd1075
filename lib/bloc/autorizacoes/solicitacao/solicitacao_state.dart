import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class SolicitacaoState extends Equatable {}

class InitialSolicitacaoState extends SolicitacaoState {
  @override
  List<Object> get props => [];
}

class LoadingSolicitacaoState extends SolicitacaoState {
  @override
  List<Object> get props => [];
}

class ErrorSolicitacaoState extends SolicitacaoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSolicitacaoState(this.message);
}

class DoneGetSolicitacaoState extends SolicitacaoState {
  final RetornoSolicitacaoModel solicitacao;

  @override
  List<Object> get props => [
        solicitacao,
      ];

  DoneGetSolicitacaoState(
    this.solicitacao,
  );
}
