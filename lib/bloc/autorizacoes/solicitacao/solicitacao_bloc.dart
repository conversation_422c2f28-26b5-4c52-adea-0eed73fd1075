import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_state.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class SolicitacaoBloc extends Bloc<SolicitacaoEvent, SolicitacaoState> {
  final logger = UnimedLogger(className: 'SolicitacaoBloc');

  SolicitacaoBloc() : super(InitialSolicitacaoState());

  @override
  Stream<SolicitacaoState> mapEventToState(
    SolicitacaoEvent event,
  ) async* {
    if (event is GetSolicitacao) {
      yield LoadingSolicitacaoState();

      try {
        final result = await Locator.instance
            .get<SolicitacoesApi>()
            .carregarSolicitacao(numProtocolo: event.numProtocolo);

        yield DoneGetSolicitacaoState(result);
      } on SolicitacaoException catch (e) {
        yield ErrorSolicitacaoState(e.message);
      } on GenericException catch (_) {
        yield ErrorSolicitacaoState(MessageException.GENERAL);
      } on Exception catch (_) {
        yield ErrorSolicitacaoState(MessageException.GENERAL);
      }
    }
  }
}
