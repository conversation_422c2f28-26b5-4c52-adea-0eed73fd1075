import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'detalhe_guia_event.dart';
part 'detalhe_guia_state.dart';

class DetalheTimelineGuiaBloc extends Bloc<DetalheGuiaEvent, DetalheGuiaState> {
  DetalheTimelineGuiaBloc() : super(DetalheGuiaInitial());

  @override
  Stream<DetalheGuiaState> mapEventToState(
    DetalheGuiaEvent event,
  ) async* {
    if (event is BuscarDetalheTimelineEvent) {
      yield LoadingBuscarDetalheState();
      try {
        final timeline = await Locator.instance
            .get<SolicitacoesApi>()
            .carregarDetalheTimeline(guia: event.guia);
        yield SuccessBuscarDetalheState(timeline: timeline);
      } catch (ex) {
        yield ErrorBuscarDetalheState(message: ex.toString());
      }
    }
  }
}
