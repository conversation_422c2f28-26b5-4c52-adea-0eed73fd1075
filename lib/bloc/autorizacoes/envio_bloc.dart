import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_state.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/services/authorization.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class AutorizacoesEnvioBloc
    extends Bloc<AutorizacoesEnvioEvent, AutorizacoesEnvioState> {
  final logger = UnimedLogger(className: 'AutorizacoesEnvioBloc');

  AutorizacoesEnvioBloc() : super(AutorizacoesEnvioInitialState());

  @override
  Stream<AutorizacoesEnvioState> mapEventToState(
    AutorizacoesEnvioEvent event,
  ) async* {
    if (event is Enviar) {
      yield AutorizacoesEnvioLoadingState();

      if (event.dao.anexos!.isEmpty) {
        logger.d('event.dao.anexos.isEmpty: ${event.dao.anexos!.isEmpty}');

        yield AutorizacoesEnvioErrorState(
            'Você precisa anexar algum documento para completar o envio da solicitação.');
      } else if (event.dao.hospitalSelecionado == null) {
        logger.d(
            'event.dao.hospitalSelecionado.isEmpty: ${event.dao.hospitalSelecionado == null}');
        yield AutorizacoesEnvioErrorState(
            'Você precisa selecionar algum hospital para atendimento');
      } else {
        final api = Locator.instance.get<AutorizacoesApi>();

        try {
          // test para liberar ação do clique do botão na tela
          await Future.delayed(Duration(milliseconds: 50));

          final resultado = await api.sendSolicitacaoV3(
              dao: event.dao, cpfLogin: event.cpfLogin);

          await AuthorizationService.deleteAuthorizationData(event.dao);
          yield AutorizacoesEnvioDoneState(resultado);
        } on AutorizacoesException catch (e) {
          yield AutorizacoesEnvioErrorState(e.message);
        } on OpenSessionAutorizacoesException catch (e) {
          yield AutorizacoesEnvioErrorState(e.message);
        } on Exception catch (_) {
          yield AutorizacoesEnvioErrorState(MessageException.GENERAL);
        }
      }
    } else if (event is Inicio) {
      yield AutorizacoesEnvioInitialState();
    } else if (event is LoadButton) {
      yield AutorizacoesEnvioLoadingState();
    }
  }
}
