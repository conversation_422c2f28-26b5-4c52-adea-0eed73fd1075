import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/profile_roles.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/eva-wpp.vo.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'eva_wpp_event.dart';
part 'eva_wpp_state.dart';

class EvaWppBloc extends Bloc<EvaWppEvent, EvaWppState> {
  EvaWppBloc() : super(EvaWppInitial());

  @override
  Stream<EvaWppState> mapEventToState(
    EvaWppEvent event,
  ) async* {
    if (event is CheckTalkEva) {
      yield LoadingEvaWppState();

      try {
        final _response = await Locator.instance.get<ProfileRolesApi>().checkTalkEvaWpp(perfil: event.perfil);

        yield DoneState(_response);
      } catch (ex) {
        yield ErrorState(ex.toString());
      }
    }
  }
}
