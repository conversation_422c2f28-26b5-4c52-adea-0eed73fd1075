import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_event.dart';

class ExamBloc extends Bloc<ExamEvent, ExamState> {
  ExamBloc() : super(ExamInitial());

  @override
  Stream<ExamState> mapEventToState(
    ExamEvent event,
  ) async* {
    if (event is GetListExamsEvent) {
      yield LoadingListExamState();

    }
  
  }
}
