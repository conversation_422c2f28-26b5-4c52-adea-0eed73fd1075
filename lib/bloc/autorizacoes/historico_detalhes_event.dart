import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/solicitacao-autorizacao.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class AutorizacoesHistoricoDetalhesEvent extends Equatable {}

class LoadDetalhes extends AutorizacoesHistoricoDetalhesEvent {
  final Carteira? carteira;
  final SolicitacaoAutorizacao solicitacao;

  @override
  List<Object?> get props => [carteira, solicitacao];

  LoadDetalhes(this.carteira, this.solicitacao);
}

class ClearDetalhes extends AutorizacoesHistoricoDetalhesEvent {
  @override
  List<Object> get props => [];
}
