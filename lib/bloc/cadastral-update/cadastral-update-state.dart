part of 'cadastral-update-bloc.dart';

abstract class CadastralUpdateState extends Equatable {
  const CadastralUpdateState();

  @override
  List<Object?> get props => [];
}

class CadastralUpdateInitial extends CadastralUpdateState {}

class LoadingCadastralUpdateState extends CadastralUpdateState {}

class ErrorCadastralUpdateState extends CadastralUpdateState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorCadastralUpdateState(this.message);
}

class LoadedCadastralUpdateState extends CadastralUpdateState {
  final VEServiceModel? veServiceModel;

  @override
  List<Object?> get props => [veServiceModel];

  LoadedCadastralUpdateState({this.veServiceModel});
}

class AdvancedStepCadastralUpdateState extends CadastralUpdateState {}

class BackedStepCadastralUpdateState extends CadastralUpdateState {}

class PALoadingState extends CadastralUpdateState {}
