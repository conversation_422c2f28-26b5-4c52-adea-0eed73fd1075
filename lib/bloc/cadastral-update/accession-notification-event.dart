import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AccessionNotificationEvent extends Equatable {}

class CheckAccessionNotificationEvent extends AccessionNotificationEvent {
  final Perfil perfil;
  final int cpf;

  @override
  List<Object> get props => [perfil, cpf];

  CheckAccessionNotificationEvent({required this.perfil, required this.cpf});
}

class SetToInitialAccessionNotificationEvent
    extends AccessionNotificationEvent {
  @override
  List<Object> get props => [];
}

class AdvanceToConfirmAccessionNotificationEvent
    extends AccessionNotificationEvent {
  final String selectedType;
  @override
  List<Object> get props => [selectedType];

  AdvanceToConfirmAccessionNotificationEvent({required this.selectedType});
}

class AdvanceToTokenUpdateEvent extends AccessionNotificationEvent {
  final String data;
  final String email;
  final String selectedType;

  @override
  List<Object> get props => [data, selectedType];

  AdvanceToTokenUpdateEvent({
    required this.data,
    required this.email,
    required this.selectedType,
  });
}

class RegisterUpdateEvent extends AccessionNotificationEvent {
  final Perfil perfil;
  final int cpf;
  final String email;
  final String data;
  final String selectedType;

  @override
  List<Object> get props => [perfil, cpf, email, data, selectedType];

  RegisterUpdateEvent({
    required this.perfil,
    required this.cpf,
    required this.email,
    required this.data,
    required this.selectedType,
  });
}
