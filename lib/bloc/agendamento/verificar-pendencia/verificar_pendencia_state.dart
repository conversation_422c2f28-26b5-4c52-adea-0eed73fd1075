import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/autorizacao-teleconsulta.vo.dart';

@immutable
abstract class VerificarPendenciaState extends Equatable {}

class InitialVerificarPendenciaState extends VerificarPendenciaState {
  @override
  List<Object> get props => [];
}

class LoadingVerificarPendenciaState extends VerificarPendenciaState {
  @override
  List<Object> get props => [];
}

class ErrorVerificarPendenciaState extends VerificarPendenciaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorVerificarPendenciaState(this.message);
}

class DoneVerificarPendenciaState extends VerificarPendenciaState {
  final RetornoProtocolo? retornoProtocolo;

  @override
  List<Object?> get props => [retornoProtocolo];

  DoneVerificarPendenciaState({this.retornoProtocolo});
}
