import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';

@immutable
abstract class VerificarPendenciaEvent extends Equatable {}

class GetStatusProtocolo extends VerificarPendenciaEvent {
  final AgendamentoVO agendamentoVO;

  GetStatusProtocolo({required this.agendamentoVO});

  @override
  List<Object> get props => [agendamentoVO];
}
