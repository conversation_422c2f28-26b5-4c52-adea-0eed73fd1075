import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/especialidade-agendamento.vo.dart';

@immutable
abstract class EspecialidadeEvent extends Equatable {}

class ListAllEspecialidadesEvent extends EspecialidadeEvent {
  final Perfil perfil;

  @override
  List<Object> get props => [perfil];

  ListAllEspecialidadesEvent({required this.perfil});
}

class SelectEspecialidadeEvent extends EspecialidadeEvent {
  final EspecialidadeAgendamento especialidade;

  @override
  List<Object> get props => [especialidade];

  SelectEspecialidadeEvent({required this.especialidade});
}

class ListEspecialitiesClinica extends EspecialidadeEvent {
  final ProviderModel prestador;
  final Perfil? perfil;
  final bool isTeleconsulta;

  @override
  List<Object?> get props => [
        prestador,
        perfil,
        isTeleconsulta,
      ];

  ListEspecialitiesClinica({
    required this.prestador,
    required this.perfil,
    required this.isTeleconsulta,
  });
}
