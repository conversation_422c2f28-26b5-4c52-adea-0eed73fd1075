import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AgendamentoEvent extends Equatable {}

class CriarSolicitacaoEvent extends AgendamentoEvent {
  final AgendaAgendamentoVO? agenda;
  final Perfil perfil;
  final ProviderModel prestador;
  final UserCredentials? user;
  final EspecialtyModel? especialidade;
  final Contact? contact;
  final String? remotePathPdf;

  @override
  List<Object?> get props => [agenda, perfil, prestador, user];

  CriarSolicitacaoEvent({
    required this.agenda,
    required this.perfil,
    required this.prestador,
    required this.user,
    required this.especialidade,
    this.contact,
    this.remotePathPdf,
  });
}

class ListarEvent extends AgendamentoEvent {
  final Perfil? perfil;

  @override
  List<Object?> get props => [perfil];

  ListarEvent({
    required this.perfil,
  });
}

class BuscarEvent extends AgendamentoEvent {
  final String keySearch;
  final Perfil? perfil;

  @override
  List<Object?> get props => [keySearch, perfil];

  BuscarEvent({
    required this.keySearch,
    required this.perfil,
  });
}

class AgendamentoFiltro extends AgendamentoEvent {
  final String? filter;

  @override
  List<Object?> get props => [filter];

  AgendamentoFiltro({required this.filter});
}

class BuscarAgendamentoEvent extends AgendamentoEvent {
  final List<AgendamentoVO> agendamentos;
  final String keySearch;

  @override
  List<Object> get props => [agendamentos, keySearch];

  BuscarAgendamentoEvent({
    required this.agendamentos,
    required this.keySearch,
  });
}

class ChangeToModeSearch extends AgendamentoEvent {
  @override
  List<Object> get props => [];
}

class FilterOnOff extends AgendamentoEvent {
  final bool enable;

  @override
  List<Object> get props => [enable];

  FilterOnOff({required this.enable});
}

class UpdatePermissionsStatus extends AgendamentoEvent {
  UpdatePermissionsStatus();

  @override
  List<Object?> get props => [];
}

class CheckTeleterapiaTerm extends AgendamentoEvent {
  final String protocolo;
  final Perfil perfil;
  final AgendamentoVO agendamento;

  @override
  List<Object?> get props => [];

  CheckTeleterapiaTerm({
    required this.protocolo,
    required this.perfil,
    required this.agendamento,
  });
}

class SetToAcceptedTeleterapiaTerm extends AgendamentoEvent {
  final String protocolo;

  @override
  List<Object?> get props => [];

  SetToAcceptedTeleterapiaTerm({
    required this.protocolo,
  });
}

class GoToTeleconsultaStatusEvent extends AgendamentoEvent {
  final AgendamentoVO agendamento;

  @override
  List<Object?> get props => [];

  GoToTeleconsultaStatusEvent({
    required this.agendamento,
  });
}
