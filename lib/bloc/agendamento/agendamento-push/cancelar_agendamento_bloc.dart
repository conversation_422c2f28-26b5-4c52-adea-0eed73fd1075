import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_state.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class CancelarAgendamentoBloc
    extends Bloc<CancelarAgendamentoEvent, CancelarAgendamentoState> {
  CancelarAgendamentoBloc() : super(CancelarAgendamentoInitialState());

  @override
  Stream<CancelarAgendamentoState> mapEventToState(
    CancelarAgendamentoEvent event,
  ) async* {
    if (event is FinalizarAgendamentoEvent) {
      String? _protocolo = event.agendamento.protocolo;

      yield CancelarAgendamentoLoadingState(agendamento: event.agendamento);

      yield CancelarAgendamentoLoadingState(
        agendamento: event.agendamento,
        protocolo: _protocolo,
      );

      final api = Locator.instance.get<AgendamentoApi>();

      try {
        await api.finalizarConsultaCanceladaBeneficiario(
          protocolo: event.agendamento.protocolo,
          observacao: event.observacoes,
          perfil: event.perfil,
        );

        yield CancelarAgendamentoDoneState(agendamento: event.agendamento);
      } on UnimedException catch (e) {
        yield CancelarAgendamentoErrorState(
          e.message,
          agendamento: event.agendamento,
        );
      } on Exception catch (e) {
        yield CancelarAgendamentoErrorState("$e",
            agendamento: event.agendamento);
      }
    } else if (event is FinalizarAgendamentoComTipoEvent) {
      String? _protocolo = event.protocolo;
      _protocolo ??= event.agendamento!.protocolo;

      yield CancelarAgendamentoLoadingState(protocolo: _protocolo);

      final api = Locator.instance.get<AgendamentoApi>();

      try {
        await api.finalizarConsultaComTipo(
          protocolo: _protocolo!,
          observacao: event.observacoes,
          tipoFinalizacao: event.tipoFinalizacao,
          perfil: event.perfil!,
        );

        yield CancelarAgendamentoDoneState(protocolo: _protocolo);
      } on UnimedException catch (e) {
        yield CancelarAgendamentoErrorState(e.message, protocolo: _protocolo);
      } on Exception catch (e) {
        yield CancelarAgendamentoErrorState("$e", protocolo: _protocolo);
      }
    } else if (event is FinalizarAgendamentoBtnEvent) {
      String? _protocolo = event.agendamento.protocolo;

      yield CancelarAgendamentoLoadingBtnState(agendamento: event.agendamento);

      yield CancelarAgendamentoLoadingBtnState(
        agendamento: event.agendamento,
        protocolo: _protocolo,
      );

      final api = Locator.instance.get<AgendamentoApi>();

      try {
        final message = await api.finalizarConsultaCanceladaBeneficiario(
          protocolo: event.agendamento.protocolo,
          observacao: event.observacoes,
          perfil: event.perfil!,
        );

        yield CancelarAgendamentoDoneBtnState(
            agendamento: event.agendamento, message: message);

      }  on NoInternetException catch (ex) {
            yield CancelarAgendamentoErrorBtnState(
                  ex.message,
                  agendamento: event.agendamento,
        );
        
      } on UnimedException catch (e) {
        yield CancelarAgendamentoErrorBtnState(
          e.message,
          agendamento: event.agendamento,
        );
      } on Exception catch (e) {
        yield CancelarAgendamentoErrorBtnState(
          "$e",
          agendamento: event.agendamento,
        );
      }
    } else if (event is GetReasonCancelAgendamentoEvent) {
      yield LoadingCancelReasonState();
      final api = Locator.instance.get<AgendamentoApi>();

      try {
        final reasons = await api.getCancelReasonList();

        yield LoadedCancelReasonAgendamentoState(reasons: reasons);
      } catch (e) {
        yield ErroCancelReasonState(message: "Falha ao buscar movitos");
      }
    } else if (event is SelectReasonCancelAgendamentoEvent) {
      yield SeletingCancelReasonState();

      yield SelectedCancelReasonState(reason: event.reason);
    }
  }
}
