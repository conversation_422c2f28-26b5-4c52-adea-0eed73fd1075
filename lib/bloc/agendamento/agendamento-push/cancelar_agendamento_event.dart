import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';

@immutable
abstract class CancelarAgendamentoEvent extends Equatable {}

class FinalizarAgendamentoEvent extends CancelarAgendamentoEvent {
  final String observacoes;
  final AgendamentoVO agendamento;
  final Perfil perfil;

  @override
  List<Object> get props => [observacoes, agendamento, perfil];

  FinalizarAgendamentoEvent({
    required this.observacoes,
    required this.agendamento,
    required this.perfil,
  });
}

class FinalizarAgendamentoComTipoEvent extends CancelarAgendamentoEvent {
  final AgendamentoVO? agendamento;
  final String? protocolo;
  final String observacoes;
  final String? tipoFinalizacao;
  final Perfil? perfil;

  @override
  List<Object?> get props =>
      [agendamento, protocolo, observacoes, tipoFinalizacao, perfil];

  FinalizarAgendamentoComTipoEvent({
    this.agendamento,
    required this.observacoes,
    this.protocolo,
    required this.tipoFinalizacao,
    required this.perfil,
  }) : assert(agendamento != null || protocolo != null);
}

class FinalizarAgendamentoBtnEvent extends CancelarAgendamentoEvent {
  final String observacoes;
  final AgendamentoVO agendamento;
  final Perfil? perfil;

  @override
  List<Object?> get props => [observacoes, agendamento, perfil];

  FinalizarAgendamentoBtnEvent({
    required this.observacoes,
    required this.agendamento,
    required this.perfil,
  });
}

class GetReasonCancelAgendamentoEvent extends CancelarAgendamentoEvent {
  @override
  List<Object?> get props => [];

  GetReasonCancelAgendamentoEvent();
}

class SelectReasonCancelAgendamentoEvent extends CancelarAgendamentoEvent {
  final CancelReason reason;

  @override
  List<Object?> get props => [reason];

  SelectReasonCancelAgendamentoEvent({required this.reason});
}
