import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';

@immutable
abstract class CancelarAgendamentoState extends Equatable {}

class CancelarAgendamentoInitialState extends CancelarAgendamentoState {
  @override
  List<Object> get props => [];
}

class CancelarAgendamentoLoadingState extends CancelarAgendamentoState {
  final AgendamentoVO? agendamento;
  final String? protocolo;

  @override
  List<Object?> get props => [agendamento];

  CancelarAgendamentoLoadingState({this.agendamento, this.protocolo})
      : assert(
          agendamento != null || protocolo != null,
        );
}

class CancelarAgendamentoErrorState extends CancelarAgendamentoState {
  final String? message;
  final AgendamentoVO? agendamento;
  final String? protocolo;

  @override
  List<Object?> get props => [message, agendamento, protocolo];

  CancelarAgendamentoErrorState(
    this.message, {
    this.agendamento,
    this.protocolo,
  }) : assert(message is String && (agendamento != null || protocolo != null));
}

class CancelarAgendamentoDoneState extends CancelarAgendamentoState {
  final AgendamentoVO? agendamento;
  final String? protocolo;

  @override
  List<Object?> get props => [agendamento, protocolo];

  CancelarAgendamentoDoneState({
    this.agendamento,
    this.protocolo,
  }) : assert(
          agendamento != null || protocolo != null,
        );
}

class CancelarAgendamentoLoadingBtnState extends CancelarAgendamentoState {
  final AgendamentoVO? agendamento;
  final String? protocolo;

  @override
  List<Object?> get props => [agendamento];

  CancelarAgendamentoLoadingBtnState({this.agendamento, this.protocolo})
      : assert(
          agendamento != null || protocolo != null,
        );
}

class CancelarAgendamentoErrorBtnState extends CancelarAgendamentoState {
  final String? message;
  final AgendamentoVO? agendamento;
  final String? protocolo;

  @override
  List<Object?> get props => [message, agendamento, protocolo];

  CancelarAgendamentoErrorBtnState(
    this.message, {
    this.agendamento,
    this.protocolo,
  }) : assert(message is String && (agendamento != null || protocolo != null));
}

class CancelarAgendamentoDoneBtnState extends CancelarAgendamentoState {
  final AgendamentoVO? agendamento;
  final String? protocolo;
  final String message;

  @override
  List<Object?> get props => [agendamento, protocolo];

  CancelarAgendamentoDoneBtnState({
    this.agendamento,
    this.protocolo,
    required this.message,
  }) : assert(
          agendamento != null || protocolo != null,
        );
}

class LoadedCancelReasonAgendamentoState extends CancelarAgendamentoState {
  final List<CancelReason> reasons;

  @override
  List<Object?> get props => [reasons];

  LoadedCancelReasonAgendamentoState({
    required this.reasons,
  });
}

class LoadingCancelReasonState extends CancelarAgendamentoState {
  @override
  List<Object?> get props => [];

  LoadingCancelReasonState();
}

class ErroCancelReasonState extends CancelarAgendamentoState {
  final String message;

  @override
  List<Object?> get props => [message];

  ErroCancelReasonState({required this.message});
}

class SeletingCancelReasonState extends CancelarAgendamentoState {
  @override
  List<Object?> get props => [];

  SeletingCancelReasonState();
}

class SelectedCancelReasonState extends CancelarAgendamentoState {
  final CancelReason reason;

  @override
  List<Object?> get props => [reason];

  SelectedCancelReasonState({required this.reason});
}
