import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/especialidade_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/especialidade_state.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/teleconsulta.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/especialidade-agendamento.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class EspecialidadeBloc extends Bloc<EspecialidadeEvent, EspecialidadeState> {
  EspecialidadeBloc() : super(EspecialidadeInitialState());

  final logger = UnimedLogger(className: 'EspecialidadeBloc');

  Iterable<EspecialidadeAgendamento> _especialidades = [];

  Iterable<dynamic> get especialidades => _especialidades;

  EspecialidadeAgendamento? _especialidade;

  EspecialidadeAgendamento? get especialidade => _especialidade;

  @override
  Stream<EspecialidadeState> mapEventToState(
    EspecialidadeEvent event,
  ) async* {
    if (event is ListAllEspecialidadesEvent) {
      yield EspecialidadeLoadingState();

      _especialidade = null;
      final api = Locator.instance.get<AgendamentoApi>();

      try {
        _especialidades = await api.getAllEspecialidades(event.perfil);

        yield EspecialidadeDoneState(_especialidades);
      } on UnimedException catch (e) {
        yield EspecialidadeErrorState(e.message);
      } on Exception catch (e) {
        yield EspecialidadeErrorState("$e");
      }
    } else if (event is SelectEspecialidadeEvent) {
      _especialidade = event.especialidade;

      yield DoneSelectEspecialidadeState(_especialidade);
    } else if (event is ListEspecialitiesClinica) {
      yield LoadingEspecialitiesState();

      try {
        final api = Locator.instance.get<TeleconsultaApi>();
        final Iterable<EspecialtyModel> _especialities =
            await api.listEspecialitiesClinica(
          perfil: event.perfil!,
          prestador: event.prestador,
          isTeleconsulta: event.isTeleconsulta,
        );

        if (_especialities.isEmpty)
          yield EspecialitiesNoDataState();
        else
          yield LoadedEspecialitiesState(_especialities);
      } on TeleconsultaException catch (e) {
        yield ErrorEspecialitiesState(e.message);
      } on UnimedException catch (e) {
        yield ErrorEspecialitiesState(e.message);
      } catch (ex) {
        logger.e('Falha ao listar especialidades => $ex');
        yield ErrorEspecialitiesState("Não foi possível listar especilidades");
      }
    }
  }
}
