import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/autorizacao-teleconsulta.vo.dart';

@immutable
abstract class TeleconsultaState extends Equatable {}

class TeleconsultaInitialState extends TeleconsultaState {
  @override
  List<Object> get props => [];
}

// Agendas de Teleconsulta
class TeleconsultaAgendaLoadingState extends TeleconsultaState {
  TeleconsultaAgendaLoadingState(this.month);
  final int month;
  @override
  List<Object> get props => [];
}

class TeleconsultaAgendaErrorState extends TeleconsultaState {
  final String message;
  final DateTime? requestedDate;

  @override
  List<Object> get props => [message];

  TeleconsultaAgendaErrorState(this.message, this.requestedDate);
}

class TeleconsultaAgendaDoneState extends TeleconsultaState {
  final Iterable<AgendaAgendamentoVO> agendas;
  final DateTime? dtInicio;

  @override
  List<Object?> get props => [agendas, dtInicio];

  TeleconsultaAgendaDoneState(this.agendas, this.dtInicio);
}

class TeleconsultaAgendaNoDataState extends TeleconsultaState {
  TeleconsultaAgendaNoDataState(this.requestedDate);
  final DateTime? requestedDate;
  @override
  List<Object?> get props => [requestedDate];
}

//
// Teleconsultas Autorizar
class TeleconsultaAutorizarLoadingState extends TeleconsultaState {
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [agendamento];

  TeleconsultaAutorizarLoadingState(this.agendamento);
}

class TeleconsultaAutorizarErrorState extends TeleconsultaState {
  final String message;
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [message, agendamento];

  TeleconsultaAutorizarErrorState(this.message, this.agendamento);
}

class TeleconsultaAutorizarPermitState extends TeleconsultaState {
  final AgendamentoVO agendamento;
  final AutorizacaoTeleconsultaAgendamento autorizacao;

  @override
  List<Object> get props => [agendamento, autorizacao];

  TeleconsultaAutorizarPermitState(this.agendamento, this.autorizacao);
}

class TeleconsultaAutorizarDenyState extends TeleconsultaState {
  final AgendamentoVO agendamento;
  final AutorizacaoTeleconsultaAgendamento autorizacao;

  @override
  List<Object> get props => [agendamento, autorizacao];

  TeleconsultaAutorizarDenyState(this.agendamento, this.autorizacao);
}

class AdvancedStepState extends TeleconsultaState {
  final int toIndex;

  @override
  List<Object> get props => [toIndex];

  AdvancedStepState({required this.toIndex});
}

class BackedStepState extends TeleconsultaState {
  @override
  List<Object?> get props => [];
}
