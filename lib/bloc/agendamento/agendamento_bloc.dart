import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_state.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/sqlite-manager/tables/teleterapia_schedule_cache.table.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class AgendamentoBloc extends Bloc<AgendamentoEvent, AgendamentoState> {
  AgendamentoBloc() : super(AgendamentoInitialState());

  final logger = UnimedLogger(className: 'AgendamentoBloc');

  List<AgendamentoVO>? _agendamentos;
  List<AgendamentoVO>? _agendamentosFiltrados;
  Map<String?, List<AgendamentoVO>> _status =
      Map<String?, List<AgendamentoVO>>();
  List<String?> _justStatus = [];

  // Iterable<Prestador> _prestadores;
  Iterable<ProviderModel>? _prestadoresFilted;
  Iterable<ProviderModel>? get prestadores => _prestadoresFilted;

  String? filter;
  bool _isSearching = false;
  bool get isSearching => _isSearching;

  late PermissionStatus _microfonePermissionStatus;
  PermissionStatus get microfonePermissionStatus => _microfonePermissionStatus;

  late PermissionStatus _cameraPermissionStatus;
  PermissionStatus get cameraPermissionStatus => _cameraPermissionStatus;

  late PermissionStatus _bluetoothPermissionStatus;
  PermissionStatus get bluetoothPermissionStatus => _bluetoothPermissionStatus;

  @override
  Stream<AgendamentoState> mapEventToState(
    AgendamentoEvent event,
  ) async* {
    if (event is CriarSolicitacaoEvent) {
      try {
        yield AgendamentoSolicitarLoadingState(event.agenda);

        await _sendAgreements(event);

        final api = Locator.instance.get<AgendamentoApi>();

        final _data = await api.createAgendamento(
          agenda: event.agenda!,
          perfil: event.perfil,
          prestador: event.prestador,
          user: event.user,
          especialidade: event.especialidade,
          contact: event.contact,
        );

        yield AgendamentoSolicitarSuccessState(
          _data['protocolo'],
          event.agenda,
        );
      } on ServiceTimeoutException catch (e) {
        yield AgendamentoSolicitarErrorState(e.message, event.agenda);
      } on UnimedException catch (e) {
        yield AgendamentoSolicitarErrorState(e.message, event.agenda);
      } on Exception catch (e) {
        yield AgendamentoSolicitarErrorState("$e", event.agenda);
      }
    } else if (event is ListarEvent) {
      yield AgendamentoListarLoadingState();

      try {
        _microfonePermissionStatus = await Permission.microphone.status;
        _cameraPermissionStatus = await Permission.camera.status;
        _bluetoothPermissionStatus = await Permission.bluetoothConnect.status;

        _agendamentos = await Locator.instance
            .get<AgendamentoApi>()
            .getAllAgendamentos(event.perfil!);

        if (_agendamentos!.length <= 0) {
          yield AgendamentoListarNoDataState();
        } else {
          _handleByStatus(_agendamentos);

          debugPrint("$_status");
          if (_isSearching) {
            add(AgendamentoFiltro(filter: filter));
          } else {
            yield AgendamentoListarSuccessState(_agendamentos, _status);
          }
        }
      } on NoInternetException catch (e) {
        yield AgendamentoListarErrorState(e.toString());
      } on ServiceTimeoutException catch (_) {
        yield AgendamentoListarErrorState(MessageException.GENERIC_TIMEOUT);
      } on AgendamentoException catch (ex) {
        yield AgendamentoListarErrorState(ex.message);
      }
    } else if (event is FilterOnOff) {
      _isSearching = event.enable;

      if (!_isSearching) filter = '';

      debugPrint('Estado: ' + _isSearching.toString());
    } else if (event is AgendamentoFiltro) {
      //yield AgendamentoSearching("Buscando");

      filter = event.filter == null ? '' : event.filter;

      debugPrint('Key: ' + event.filter!);

      if (event.filter!.isEmpty) {
        _agendamentosFiltrados = _agendamentos;
      } else {
        // final formattedFilterText = event.filter.trim().toLowerCase();

        // logger.d('Text to Search: => $formattedFilterText');
        _agendamentosFiltrados = List<AgendamentoVO>.empty(growable: true);
        _agendamentos!.forEach((agendamento) {
          if (agendamento
              .toJson()
              .toString()
              .toLowerCase()
              .contains(event.filter!.toLowerCase()))
            _agendamentosFiltrados!.add(agendamento);
        });
      }
      _handleByStatus(_agendamentosFiltrados);

      yield AgendamentoListarPesquisaSuccessState(
        _agendamentos,
        _agendamentosFiltrados,
        _status,
      );
    } else if (event is UpdatePermissionsStatus) {
      try {
        _microfonePermissionStatus = await Permission.microphone.status;
        _cameraPermissionStatus = await Permission.camera.status;
        _bluetoothPermissionStatus = await Permission.bluetoothConnect.status;
      } catch (e) {
        logger.e('error in UpdatePermissionsStatus: $e');
      }
    } else if (event is CheckTeleterapiaTerm) {
      try {
        yield CheckTeleterapiaTermLoadingState();
        final api = Locator.instance.get<AgendamentoApi>();
        final isTeleterapia = await api.checkIfIsTeleterapia(
            protocolo: event.protocolo, perfil: event.perfil);
        logger.d('the schedule is teleterapia? $isTeleterapia');

        if (isTeleterapia) {
          final isAcceptedTerm =
              await api.isAcceptedTeleterapiaTerm(protocolo: event.protocolo);
          logger.d('this teleterapia is accepted term? $isAcceptedTerm');

          yield IsTeleterapiaSuccess(
            isAcceptedTerm: isAcceptedTerm,
            protocolo: event.protocolo,
            agendamento: event.agendamento,
          );
          _setAgendamentoSuccess();
        } else {
          yield IsNoTeleterapiaState(
            agendamento: event.agendamento,
          );
          _setAgendamentoSuccess();
        }
      } on NoInternetException catch (e) {
        yield AgendamentoCheckTeleterapiaTermErrorState(e.toString());
      } on ServiceTimeoutException catch (_) {
        yield AgendamentoCheckTeleterapiaTermErrorState(
            MessageException.GENERIC_TIMEOUT);
      } on AgendamentoException catch (ex) {
        yield AgendamentoCheckTeleterapiaTermErrorState(ex.message);
      } on SqlException catch (e) {
        logger.e('Error on CheckTeleterapiaTerm - SqlException - $e');
        yield AgendamentoSqlErrorState(
            agendamento: event.agendamento, protocolo: event.protocolo);
        _setAgendamentoSuccess();
      } catch (e) {
        yield AgendamentoCheckTeleterapiaTermErrorState(e.toString());
      }
    } else if (event is SetToAcceptedTeleterapiaTerm) {
      await TeleterapiaScheduleCacheTableSQLite()
          .setToAcceptedTeleterapiaTerm(protocolo: event.protocolo);
    } else if (event is GoToTeleconsultaStatusEvent) {
      yield GoToTeleconsultaStatusState(agendamento: event.agendamento);
    }
  }

  _setAgendamentoSuccess() async* {
    await Future.delayed(const Duration(seconds: 1));
    if (_isSearching) {
      add(AgendamentoFiltro(filter: filter));
    } else {
      yield AgendamentoListarSuccessState(_agendamentos, _status);
    }
  }

  void _handleByStatus(List<AgendamentoVO>? agendamentos) {
    _status = Map<String?, List<AgendamentoVO>>();

    if (agendamentos != null && agendamentos.length > 0) {
      agendamentos.forEach((agendamento) {
        final _key = agendamento.status;

        if (_status.containsKey(_key)) {
          _status[_key]!.add(agendamento);
        } else {
          _justStatus.add(_key);
          _status[_key] = List<AgendamentoVO>.empty(growable: true)
            ..add(agendamento);
        }
      });
    }
  }

  Future _sendAgreements(CriarSolicitacaoEvent event) async {
    try {
      if (event.contact!.agreeWhatsapp != null &&
          event.contact!.agreeWhatsapp == true) {
        await Locator.instance.get<BeneficiarioApi>().saveWhatsappNumber(
              event.contact!.telefone!,
              event.perfil,
              event.user!.cpfSabius,
              event.contact!.telefone,
              '',
              event.contact!.email,
            );
      }
      if (event.contact!.requestEmailTerm != null &&
          event.contact!.requestEmailTerm == true) {
        await Locator.instance.get<BeneficiarioApi>().sendEmailTerms(
              event.contact!.email,
              event.remotePathPdf!,
              event.perfil,
            );
      }
    } catch (e) {
      logger.e('Error on _sendAgreements $e');
      throw e;
    }
  }
}
