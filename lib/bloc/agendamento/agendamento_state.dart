import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AgendamentoState extends Equatable {}

class AgendamentoInitialState extends AgendamentoState {
  @override
  List<Object> get props => [];
}

class CheckTeleterapiaTermLoadingState extends AgendamentoState {
  @override
  List<Object?> get props => [];
}

// States Solicitação de Agendamento
class AgendamentoSolicitarLoadingState extends AgendamentoState {
  final AgendaAgendamentoVO? agenda;

  @override
  List<Object?> get props => [agenda];

  AgendamentoSolicitarLoadingState(this.agenda);
}

class AgendamentoSolicitarErrorState extends AgendamentoState {
  final String message;
  final AgendaAgendamentoVO? agenda;

  @override
  List<Object?> get props => [message, agenda];

  AgendamentoSolicitarErrorState(this.message, this.agenda);
}

class AgendamentoSolicitarSuccessState extends AgendamentoState {
  final String? protocolo;
  final AgendaAgendamentoVO? agenda;

  @override
  List<Object?> get props => [protocolo, agenda];

  AgendamentoSolicitarSuccessState(this.protocolo, this.agenda);
}

// States Listagem de agendamentos
class AgendamentoListarLoadingState extends AgendamentoState {
  @override
  List<Object> get props => [];

  AgendamentoListarLoadingState();
}

class AgendamentoListarErrorState extends AgendamentoState {
  final String message;

  @override
  List<Object> get props => [message];

  AgendamentoListarErrorState(this.message);
}

class AgendamentoListarSuccessState extends AgendamentoState {
  final List<AgendamentoVO>? agendamentos;
  final Map<String?, List<AgendamentoVO>> separadosPorStatus;

  @override
  List<Object?> get props => [agendamentos, separadosPorStatus];

  AgendamentoListarSuccessState(this.agendamentos, this.separadosPorStatus);
}

class AgendamentoListarNoDataState extends AgendamentoState {
  @override
  List<Object> get props => [];
}

class SearchModeState extends AgendamentoState {
  @override
  List<Object> get props => [];
}

class LoadedSearchState extends AgendamentoState {
  final List<ProviderModel> prestadores;

  @override
  List<Object> get props => [prestadores];

  LoadedSearchState(this.prestadores);
}

class AgendamentoListarPesquisaSuccessState extends AgendamentoState {
  final List<AgendamentoVO>? agendamentos;
  final List<AgendamentoVO>? agendamentosFiltrados;
  final Map<String?, List<AgendamentoVO>> separadosPorStatus;

  @override
  List<Object?> get props =>
      [agendamentos, agendamentosFiltrados, separadosPorStatus];

  AgendamentoListarPesquisaSuccessState(
    this.agendamentos,
    this.agendamentosFiltrados,
    this.separadosPorStatus,
  );
}

class IsTeleterapiaSuccess extends AgendamentoState {
  final bool isAcceptedTerm;
  final String protocolo;
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [];

  IsTeleterapiaSuccess({
    required this.isAcceptedTerm,
    required this.protocolo,
    required this.agendamento,
  });
}

class IsNoTeleterapiaState extends AgendamentoState {
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [];

  IsNoTeleterapiaState({
    required this.agendamento,
  });
}

class AgendamentoCheckTeleterapiaTermErrorState extends AgendamentoState {
  final String message;

  @override
  List<Object> get props => [message];

  AgendamentoCheckTeleterapiaTermErrorState(this.message);
}

class AgendamentoSqlErrorState extends AgendamentoState {
  final String protocolo;
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [];

  AgendamentoSqlErrorState({
    required this.protocolo,
    required this.agendamento,
  });
}

class EmptyTeleterapiaTerm extends AgendamentoState {
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [];

  EmptyTeleterapiaTerm({
    required this.agendamento,
  });
}

class GoToTeleconsultaStatusState extends AgendamentoState {
  final AgendamentoVO agendamento;

  @override
  List<Object> get props => [];

  GoToTeleconsultaStatusState({
    required this.agendamento,
  });
}
