import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class ControleAnimacaoConsultaState extends Equatable {}

class InitialControleAnimacaoState extends ControleAnimacaoConsultaState {
  @override
  List<Object> get props => [];
}

class TelaEspecialidade extends ControleAnimacaoConsultaState {
  final ProviderModel? clinicaUnimed;
  final Perfil? perfil;

  TelaEspecialidade({this.clinicaUnimed, this.perfil});

  @override
  List<Object?> get props => [clinicaUnimed, perfil];
}

class TelaAgenda extends ControleAnimacaoConsultaState {
  final ProviderModel? prestador;
  final ProviderModel? clinicaUnimed;
  final EspecialtyModel? especialidade;
  TelaAgenda({this.prestador, this.clinicaUnimed, this.especialidade});
  @override
  List<Object?> get props => [prestador, clinicaUnimed, especialidade];
}
