import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/controle-animacao-consulta/controle_animacao_consulta_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/controle-animacao-consulta/controle_animacao_consulta_state.dart';

class ControleAnimacaoConsultaBloc
    extends Bloc<ControleAnimacaoConsultaEvent, ControleAnimacaoConsultaState> {
  ControleAnimacaoConsultaBloc() : super(InitialControleAnimacaoState());

  @override
  Stream<ControleAnimacaoConsultaState> mapEventToState(
    ControleAnimacaoConsultaEvent event,
  ) async* {
    if (event is MudarParaEspecialidade) {
      yield TelaEspecialidade(
        perfil: event.perfil,
        clinicaUnimed: event.clinicaUnimed,
      );
    }
    if (event is MudarParaAgenda) {
      yield TelaAgenda(
        prestador: event.prestador,
        clinicaUnimed: event.clinicaUnimed,
        especialidade: event.especialidade,
      );
    }
  }
}
