import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/consulta_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/consulta_state.dart';
import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/consulta.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/teleconsulta.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

import '../../models/medical-guide/provider.model.dart';

class ConsultaBloc extends Bloc<ConsultaEvent, ConsultaState> {
  ConsultaBloc() : super(ConsultaInitialState());

  int _pageIndex = 0;
  int get pageIndex => _pageIndex;

  EspecialtyModel? _especialidadeSelected;
  EspecialtyModel? get especialidadeSelected => _especialidadeSelected;

  AddressModel? _enderecoSelected;
  AddressModel? get enderecoSelected => _enderecoSelected;

  AgendaAgendamentoVO? _horarioSelected;
  AgendaAgendamentoVO? get horarioSelected => _horarioSelected;

  @override
  Stream<ConsultaState> mapEventToState(
    ConsultaEvent event,
  ) async* {
    if (event is AdvanceStep) {
      event.indexPage < 0 ? _pageIndex++ : _pageIndex = event.indexPage;

      yield AdvancedStepState(toIndex: _pageIndex);
      // yield _handleSubtitle(_pageIndex);
    } else if (event is BackStep) {
      event.indexPage < 0 ? _pageIndex-- : _pageIndex = event.indexPage;

      yield BackedStepState();
      // yield _handleSubtitle(_pageIndex);
    } else if (event is ListAllPrestadoresClinicaUnimedEvent) {
      yield ConsultaListPrestadorLoadingState();

      final api = Locator.instance.get<ConsultaApi>();

      try {
        final _prestadores = await api.listPrestadoresClinicaMedica(
          perfil: event.perfil!,
          clinicaUnimed: event.clinicaUnimed,
          especialidade: event.especialidade,
          teleconsulta: event.teleconsulta,
        );

        if (_prestadores.length <= 0) {
          yield ConsultaListPrestadorNoDataState();
        } else {
          yield ConsultaListPrestadorDoneState(_prestadores);
        }
      } on UnimedException catch (e) {
        yield ConsultaListPrestadorErrorState(e.message);
      } on Exception catch (e) {
        yield ConsultaListPrestadorErrorState("$e");
      } catch (ex) {
        yield ConsultaAgendaPrestadorErrorState(
          "Não foi possível listar agenda disponível",
          DateTime.now(),
        );
      }
    } else if (event is ListAllAgendasPrestadorEvent) {
      yield ConsultaAgendaPrestadorLoadingState(event.dataInicio.month);

      try {
        final api = Locator.instance.get<TeleconsultaApi>();
        final Iterable<AgendaAgendamentoVO> _agendas =
            await api.listAgendasLivresByPrestador(
          perfil: event.perfil!,
          prestador: event.prestador!,
          clinicaUnimed: event.clinicaUnimed,
          especialidade: event.especialidade,
          isTeleconsulta: false,
          dataInicio: event.dataInicio,
          dataFim: event.dataFim,
          endereco: event.endereco,
        );
        final Iterable<AgendaAgendamentoVO> filteredAgendaBySpecialty =
            _filterAgendaBySpecialty(
                _agendas, event.especialidade!.codEspecialidade);

        final Iterable<AgendaAgendamentoVO> filteredAgenda =
            _filterAgendaByAddress(
          filteredAgendaBySpecialty,
          event.endereco?.codEndereco,
        );
        if (filteredAgenda.length <= 0) {
          yield ConsultaAgendaPrestadorNoDataState(event.dataInicio);
        } else {
          yield ConsultaAgendaPrestadorDoneState(
            filteredAgenda,
            event.dataInicio,
          );
        }
      } on TeleconsultaException catch (e) {
        yield ConsultaAgendaPrestadorErrorState(e.message, event.dataInicio);
      } on UnimedException catch (e) {
        yield ConsultaAgendaPrestadorErrorState(e.message, event.dataInicio);
      } on Exception catch (e) {
        yield ConsultaAgendaPrestadorErrorState("$e", event.dataInicio);
      } catch (ex) {
        yield ConsultaAgendaPrestadorErrorState(
            "Não foi possível listar agenda disponível", event.dataInicio);
      }
    } else if (event is ResetConsultaEvent) {
      _especialidadeSelected = null;
      _horarioSelected = null;
      _enderecoSelected = null;
      yield ConsultaInitialState();
    } else if (event is SelectEspecialty) {
      _especialidadeSelected = event.especialtyModel;
    } else if (event is SelectAddress) {
      _enderecoSelected = event.addressModel;
    } else if (event is SelectHorario) {
      _horarioSelected = event.horario;
    }
  }

  Iterable<AgendaAgendamentoVO> _filterAgendaBySpecialty(
      Iterable<AgendaAgendamentoVO> agenda, String? codEspecialidade) {
    if (codEspecialidade == null) return agenda;
    List<AgendaAgendamentoVO> _filtered = [];
    for (var item in agenda) {
      for (var especialidade in item.especialidades!) {
        if (especialidade.codEspecialidade == codEspecialidade) {
          _filtered.add(item);
        }
      }
    }

    return _filtered;
  }

  Iterable<AgendaAgendamentoVO> _filterAgendaByAddress(
      Iterable<AgendaAgendamentoVO> agenda, String? codAddress) {
    if (codAddress == null) return agenda;
    List<AgendaAgendamentoVO> _filtered = [];
    for (var item in agenda) {
      if (item.local!.codEndereco == codAddress) {
        _filtered.add(item);
      }
    }

    return _filtered;
  }
}
