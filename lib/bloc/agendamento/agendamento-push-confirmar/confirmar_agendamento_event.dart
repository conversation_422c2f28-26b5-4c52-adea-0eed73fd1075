import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class ConfirmarAgendamentoEvent extends Equatable {}

class ConfirmarConsultaAgendamentoEvent extends ConfirmarAgendamentoEvent {
  final String? protocolo;
  final Perfil? perfil;

  @override
  List<Object?> get props => [protocolo, perfil];

  ConfirmarConsultaAgendamentoEvent({
    required this.protocolo,
    required this.perfil,
  });
}
