import 'dart:async';

import 'package:bloc/bloc.dart';

import 'package:cliente_minha_unimed/bloc/agendamento/chat-pendencia/chat_pendencia_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/chat-pendencia/chat_pendencia_state.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class ChatPendenciaBloc extends Bloc<ChatPendenciaEvent, ChatPendenciaState> {
  ChatPendenciaBloc() : super(InitialChatPendenciaState());

  @override
  Stream<ChatPendenciaState> mapEventToState(
    ChatPendenciaEvent event,
  ) async* {
    if (event is GetListChat) {
      yield LoadingListarChatState();

      try {
        final _retorno = await Locator.instance
            .get<AgendamentoApi>()
            .listarMensagens(protocolo: event.protocolo);

        yield DoneListarChatState(listRetorno: _retorno);
      } catch (err) {
        yield ErrorListarChatState('$err');
      }
    }
    if (event is SalvarMensagem) {
      yield LoadingSalvarChatState();

      try {
        String? message =
            await Locator.instance.get<AgendamentoApi>().salvarChatAtendimento(
                  protocolo: event.protocolo,
                  mensagem: event.mensagem,
                );

        yield DoneSalvarChatState(message);
      } catch (err) {
        yield ErrorSalvarChatState('$err');
      }
    }
  }
}
