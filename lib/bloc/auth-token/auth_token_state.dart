part of 'auth_token_bloc.dart';

abstract class AuthTokenState extends Equatable {
  const AuthTokenState();

  @override
  List<Object?> get props => [];
}

class AuthTokenInitial extends AuthTokenState {}

class SolicitationAuthTokenLoading extends AuthTokenState {}

class LoadingRegisterDevice extends AuthTokenState {}

class DoneRegisterDevice extends AuthTokenState {}

class AuthTokenLoading extends AuthTokenState {}

class RequestGeolocation extends AuthTokenState {}

class OpenAppConfigs extends AuthTokenState {}

class AuthTokenError extends AuthTokenState {
  AuthTokenError(this.message);
  final String? message;
  @override
  List<Object?> get props => [message];
}

class AuthTokenDone extends AuthTokenState {
  AuthTokenDone(this.token, this.expireTimeInSeconds, this.isTokenTea);
  final String token;
  final int expireTimeInSeconds;
  final bool isTokenTea;

  @override
  List<Object> get props => [token, expireTimeInSeconds, isTokenTea];
}
