import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/auth-token/auth-token-tea.model.dart';
import 'package:cliente_minha_unimed/models/auth-token/auth-token.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth-token.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/geolocation.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:location_permissions/location_permissions.dart';

part 'auth_token_event.dart';
part 'auth_token_state.dart';

class AuthTokenBloc extends Bloc<AuthTokenEvent, AuthTokenState> {
  AuthTokenBloc({required this.authTokenApi}) : super(AuthTokenInitial());
  final AuthTokenApi authTokenApi;
  final UnimedLogger logger = UnimedLogger(className: 'AuthTokenBloc');
  AuthTokenModel? _authToken;
  AuthTokenTeaModel? _authTokenTea;
  // String? _carteiraNumero;
  late DateTime _lastTokenRequestTime;
  Position? myLocation;

  @override
  Stream<AuthTokenState> mapEventToState(
    AuthTokenEvent event,
  ) async* {
    if (event is GetAuthToken) {
      yield* _handleGetAuthToken(event);
    } else if (event is OpenAppConfigsEvent) {
      // await LocationPermissions().requestPermissions();
      // await (Locator.instance.get<GeolocationService>().requestPermission());
      PermissionStatus permission =
          await LocationPermissions().checkPermissionStatus();
      if (permission != PermissionStatus.granted) {
        await LocationPermissions().openAppSettings();
      }
    } else if (event is RegisterDevice) {
      yield* _handleRegisterDevice(event);
    }
  }

  Stream<AuthTokenState> _handleRegisterDevice(RegisterDevice event) async* {
    try {
      yield LoadingRegisterDevice();

      final api = Locator.instance.get<AuthTokenApi>();
      await api.registerDevice(
        userId: event.userId,
        so: event.so,
        deviceData: event.deviceData,
      );

      yield DoneRegisterDevice();
    } on ServiceTimeoutException catch (e) {
      yield AuthTokenError(e.message);
    } on SocketException catch (e) {
      yield AuthTokenError(e.message);
    } on NoInternetException catch (e) {
      yield AuthTokenError(e.message);
    } catch (e) {
      logger.e('Exception in _handleRegisterDevice $e');
      yield AuthTokenError(e.toString());
    }
  }

  Stream<AuthTokenState> _handleGetAuthToken(GetAuthToken event) async* {
    try {
      PermissionStatus permission =
          await LocationPermissions().checkPermissionStatus();
      bool _locationPermissionInProgress = false;
      _authTokenTea = null;
      _authToken = null;
      yield SolicitationAuthTokenLoading();
      if (event.requestLocation) {
        logger.i('send location has been granted');
        if (permission == PermissionStatus.granted) {
          myLocation = await (Locator.instance
              .get<GeolocationService>()
              .getCurrentPosition());
          logger.i(
              'location permission has been granted with high precision - ${myLocation == null ? 'Current Location is null' : 'Latitude: ${myLocation!.latitude}, longitude: ${myLocation!.longitude}'}');
        } else {
          _locationPermissionInProgress = true;
          // yield RequestGeolocation();
          if (event.requestPermission) {
            yield OpenAppConfigs();
          } else {
            yield RequestGeolocation();
          }
        }
      }

      if (!_locationPermissionInProgress) {
        final api = Locator.instance.get<AuthTokenApi>();
        try {
          _authTokenTea = await api.getAuthTokenTea(
            beneficiaryCard: event.beneficiaryCard!,
            position: myLocation,
          );
        } catch (e) {
          logger.e('Exception in getAuthTokenTea $e');
        }
        try {
          _authToken =
              await api.getAuthToken(beneficiaryCard: event.beneficiaryCard!);
        } catch (e) {
          logger.e('Exception in getAuthToken $e');
        }

        _lastTokenRequestTime = DateTime.now();
      }
      // }

      String? _token;
      int? _tempoToken;
      bool _isTokenTea = false;
      // _carteiraNumero = event.beneficiaryCard?.carteiraNumero;

      if (!_locationPermissionInProgress) {
        if ((_authTokenTea != null && _authToken != null) ||
            _authTokenTea != null) {
          _token = _authTokenTea!.tokenTea;
          _tempoToken = _authTokenTea!.qtdTempoToken;
          _isTokenTea = true;
          logger.i(
              'TokenTea is not null and Tokenapp is ${_authToken == null ? 'null' : 'not null'}, TokenTea will be displayed');
        } else if (_authToken != null) {
          _token = _authToken!.token;
          _tempoToken = _authToken!.expireTimeInSeconds;
          logger.i(
              'TokenTea is null and Tokenapp is not null, Tokenapp will be displayed');
        }

        if (_token != null && _tempoToken != null) {
          final diff =
              DateTime.now().difference(_lastTokenRequestTime).inSeconds;
          logger.i(
              'location permission has been granted with high precision - ${myLocation == null ? 'Current Location is null' : 'Latitude: ${myLocation!.latitude}, longitude: ${myLocation!.longitude}'}');
          yield AuthTokenDone(_token, _tempoToken - diff, _isTokenTea);
        } else {
          logger.e(
              'Exception in _handleGetAuthToken - token or tempoToken is null');
          final String message =
              'Não foi possível acessar o Token de Autorização';
          yield AuthTokenError(message);
        }
      }
    } catch (e) {
      logger.e('Exception in _handleGetAuthToken $e');
    }
  }
}
