import 'package:cliente_minha_unimed/models/noticia-portal.model.dart';

abstract class NoticiaState {}

class NoticiaInitialState extends NoticiaState {}

class NoticiaLoadingState extends NoticiaState {}

class NoticiaLoadedState extends NoticiaState {
  List<NoticiaPortal>? listNoticiasContent;
  NoticiaLoadedState({required this.listNoticiasContent});
}

class NoticiaErrorState extends NoticiaState {
  final String? message;
  NoticiaErrorState({required this.message});
}
