import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:cliente_minha_unimed/models/noticia-portal.model.dart';
import 'package:cliente_minha_unimed/shared/api/portal.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'noticia_event.dart';
import 'noticia_state.dart';

class NoticiaBloc extends HydratedBloc<NoticiaEvent, NoticiaState> {
  NoticiaBloc() : super(NoticiaInitialState());
  List<NoticiaPortal>? _listNoticiasContent =
      List<NoticiaPortal>.empty(growable: true);

  List<NoticiaPortal>? get listNoticiasContent => _listNoticiasContent;

  @override
  Stream<NoticiaState> mapEventToState(NoticiaEvent event) async* {
    if (event is GetNoticia) {
      try {
        if (isCached(event.idNoticia)) {
          yield NoticiaLoadingState();
          await Future.delayed(Duration(milliseconds: 200));
          yield NoticiaLoadedState(listNoticiasContent: _listNoticiasContent);
        } else {
          yield NoticiaLoadingState();
          final noticiaPortal = await Locator.instance
              .get<PortalApi>()
              .getNoticia(idNoticia: event.idNoticia);
          _listNoticiasContent!.add(noticiaPortal);
          yield NoticiaLoadedState(listNoticiasContent: _listNoticiasContent);
        }
      } on PortalException catch (e) {
        yield NoticiaErrorState(message: e.message);
      } catch (e) {
        yield NoticiaErrorState(
            message: 'Houve um erro ao carregar as notícias. Tente novamente.');
      }
    } else if (event is ClearCachedNoticiasEvent) {
      if (event.isOutOfdate) _listNoticiasContent!.clear();
      yield NoticiaInitialState();
    }
  }

  @override
  NoticiaState? fromJson(Map<String, dynamic> json) {
    try {
      _listNoticiasContent = json['listNoticiasContent']
          .map<NoticiaPortal>((v) => NoticiaPortal.fromJson(v))
          .toList();

      return NoticiaLoadedState(listNoticiasContent: _listNoticiasContent);
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(NoticiaState state) {
    try {
      final listJson = _listNoticiasContent!.map((v) => v.toJson()).toList();

      return {'listNoticiasContent': listJson};
    } catch (_) {
      return null;
    }
  }

  isCached(idNoticia) {
    final idx = _listNoticiasContent!.indexWhere((v) => v.id == idNoticia);

    return idx < 0 ? false : true;
  }
}
