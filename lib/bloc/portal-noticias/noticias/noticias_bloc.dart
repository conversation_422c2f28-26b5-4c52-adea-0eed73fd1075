import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:cliente_minha_unimed/models/noticias-portal.model.dart';
import 'package:cliente_minha_unimed/shared/api/portal.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'noticias_event.dart';
import 'noticias_state.dart';

class NoticiasBloc extends HydratedBloc<NoticiasEvent, NoticiasState> {
  NoticiasBloc() : super(NoticiasInitialState());
  NoticiasPortalObject? _noticiasPortalObject;

  NoticiasPortalObject? get noticiasPortalObject => _noticiasPortalObject;
  int _timeDiffInSeconds = 12 * 60 * 60; // 12 hours

  @override
  Stream<NoticiasState> mapEventToState(NoticiasEvent event) async* {
    if (event is ListAllNoticias) {
      try {
        if (!isOutOfdate) {
          yield LoadingListAllNoticiasState();
          await Future.delayed(Duration(milliseconds: 200));
          yield DoneListAllNoticiasState(
              noticiasPortalObject: noticiasPortalObject);
        } else {
          yield LoadingListAllNoticiasState();
          final noticias =
              await Locator.instance.get<PortalApi>().getNoticias();
          _noticiasPortalObject = NoticiasPortalObject(
              listNoticiasPortal: noticias,
              millisecondsSinceEpoch: DateTime.now().millisecondsSinceEpoch);
          yield DoneListAllNoticiasState(
              noticiasPortalObject: noticiasPortalObject);
        }
      } on PortalException catch (e) {
        yield ListAllNoticiasErrorState(message: e.message);
      } catch (e) {
        yield ListAllNoticiasErrorState(
            message: 'Houve um erro ao carregar as notícias. Tente novamente.');
      }
    }
  }

  @override
  NoticiasState? fromJson(Map<String, dynamic> json) {
    try {
      _noticiasPortalObject = NoticiasPortalObject.fromJson(json);

      return DoneListAllNoticiasState(
        noticiasPortalObject: noticiasPortalObject,
      );
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(NoticiasState state) {
    try {
      return _noticiasPortalObject!.toJson();
    } catch (_) {
      return null;
    }
  }

  bool get isOutOfdate {
    // caso em que é a primeira vez o app está rodando
    if (_noticiasPortalObject == null) return true;

    final sec = DateTime.now()
        .difference(DateTime.fromMillisecondsSinceEpoch(
            _noticiasPortalObject!.millisecondsSinceEpoch!))
        .inSeconds;
    return (sec > _timeDiffInSeconds) ? true : false;
  }
}
