import 'package:cliente_minha_unimed/models/noticias-portal.model.dart';

abstract class NoticiasState {}

class NoticiasInitialState extends NoticiasState {}

class LoadingListAllNoticiasState extends NoticiasState {}

class ListAllNoticiasErrorState extends NoticiasState {
  final String? message;
  ListAllNoticiasErrorState({this.message});
}

class DoneListAllNoticiasState extends NoticiasState {
  final NoticiasPortalObject? noticiasPortalObject;
  DoneListAllNoticiasState({this.noticiasPortalObject});
}
