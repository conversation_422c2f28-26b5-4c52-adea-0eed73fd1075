import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/banners/banners_event.dart';
import 'package:cliente_minha_unimed/bloc/banners/banners_state.dart';
import 'package:cliente_minha_unimed/shared/api/banner.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/preferences.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class BannersBloc extends Bloc<BannersEvent, BannersState> {
  BannersBloc() : super(InitialState());
  final logger = UnimedLogger(className: 'BannersBloc');

  @override
  Stream<BannersState> mapEventToState(
    BannersEvent event,
  ) async* {
    if (event is ListAllBannersEvent) {
      yield LoadingState();
      try {
        final _banners = await Locator.instance
            .get<BannerApiDart>()
            .getBanner(event.cpfLogado);

        final _filteredBanners = _banners;

        if (_filteredBanners == null || _filteredBanners.length <= 0) {
          yield NoBannerDataState();
        } else {
          yield DoneState(_filteredBanners);
        }
      } on UnimedException catch (e) {
        logger.e('UnimedException ${e.message}');

        yield ErrorState(e.message);
      } on SocketException catch (_) {
        yield ErrorState(
          "Não foi possível conectar, tente novamente mais tarde.",
        );
      } on HandshakeException catch (_) {
        yield ErrorState(
          "Não foi possível conectar, tente novamente mais tarde.",
        );
      }
    } else if (event is MarkBannerAsRead) {
      await Locator.instance
          .get<BannersPreferences>()
          .setBannerIdRead(event.banner.id, event.cpfLogado);
    }
  }
}
