import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/remote-config/banner-remote-config.model.dart';

@immutable
abstract class BannersEvent extends Equatable {}

class ListAllBannersEvent extends BannersEvent {
  final int cpfLogado;
  @override
  List<Object> get props => [cpfLogado];

  ListAllBannersEvent(this.cpfLogado);
}

class MarkBannerAsRead extends BannersEvent {
  final BannerRemoteConfig banner;
  final int cpfLogado;

  @override
  List<Object> get props => [banner, cpfLogado];

  MarkBannerAsRead(this.banner, this.cpfLogado);
}
