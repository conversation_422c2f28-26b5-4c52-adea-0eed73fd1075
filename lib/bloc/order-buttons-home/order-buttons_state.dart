
import 'package:cliente_minha_unimed/models/order-buttons-home/order-buttons-home.model.dart';
import 'package:equatable/equatable.dart';

abstract class OrderButtonsHomeState extends Equatable {
  const OrderButtonsHomeState();

  @override
  List<Object> get props => [];
}

class OrderButtonsHomeInitial extends OrderButtonsHomeState {}

class OrderButtonsHomeLoading extends OrderButtonsHomeState {}

class OrderButtonsHomeError extends OrderButtonsHomeState {
  final String message;

  OrderButtonsHomeError(this.message);

  @override
  List<Object> get props => [message];
}

class OrderButtonsHomeEmpty extends OrderButtonsHomeState {
  final String message;

  OrderButtonsHomeEmpty(this.message);

  @override
  List<Object> get props => [message];
}

class OrderButtonsHomeLoaded extends OrderButtonsHomeState {
  final List<OrderButtonsHomeModel> orderButtonsHome;

  OrderButtonsHomeLoaded(this.orderButtonsHome);

  @override
  List<Object> get props => [orderButtonsHome];
}


