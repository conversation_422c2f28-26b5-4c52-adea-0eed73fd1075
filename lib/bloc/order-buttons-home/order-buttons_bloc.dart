import 'package:cliente_minha_unimed/bloc/order-buttons-home/order-buttons_event.dart';
import 'package:cliente_minha_unimed/bloc/order-buttons-home/order-buttons_state.dart';
import 'package:cliente_minha_unimed/shared/api/order-home-buttons.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OrderButtonsHomeBloc extends Bloc<OrderButtonsHomeEvent, OrderButtonsHomeState> {
  OrderButtonsHomeBloc() : super(OrderButtonsHomeInitial());

  @override
  Stream<OrderButtonsHomeState> mapEventToState(OrderButtonsHomeEvent event) async* {
    if (event is GetListOrderButtonsHome) {
      try {
        yield OrderButtonsHomeLoading();

        final result = await Locator.instance.get<OrderHomeButtonApi>().getOrderButtons(
              carteira: event.carteira,
            );

        yield OrderButtonsHomeLoaded(result);
      } catch (e) {
        yield OrderButtonsHomeError(e.toString());
      }
    }
  }
}
