import 'dart:async';
import 'dart:io' as io;
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/pdf.api.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:equatable/equatable.dart';
import 'package:path_provider/path_provider.dart';

part 'pdf_by_url_event.dart';
part 'pdf_by_url_state.dart';

// TODO trocar nomes byURL por um nome mais geral , pois agora esse bloc gerar o pdf tanto pela url como pelo path.
class PdfByUrlBloc extends Bloc<PdfByUrlEvent, PdfByUrlState> {
  PdfByUrlBloc({required this.pdfApi}) : super(PdfByUrlInitial());
  final PdfApi pdfApi;

  @override
  Stream<PdfByUrlState> mapEventToState(
    PdfByUrlEvent event,
  ) async* {
    if (event is SetPDFByUrlError) {
      yield PdfByUrlError(event.message);
    } else if (event is GetPDFByUrl) {
      yield* _handleGetPDFByUrl(event);
    } else if (event is GetPDFByPath) {
      yield* _handleGetPDFByPath(event);
    } else if (event is ResetState) {
      yield PdfByUrlInitial();
    }
  }

  Stream<PdfByUrlState> _handleGetPDFByPath(GetPDFByPath event) async* {
    try {
      yield PdfByUrlLoading();

      final filename = FileUtils.formatFilenamePDF(event.filename);
      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      io.File file = io.File('$documentDirectory' +
          '/${DateTime.now().millisecondsSinceEpoch.toString()}_$filename');

      final fileBytes = await io.File('${event.path}').readAsBytes();
      await file.writeAsBytes(fileBytes);

      yield PdfByUrlDone(file: file);
    } catch (e) {
      final String message = 'Não foi possível carregar o documento!';
      yield PdfByUrlError(message);
    }
  }

  Stream<PdfByUrlState> _handleGetPDFByUrl(GetPDFByUrl event) async* {
    try {
      yield PdfByUrlLoading();
      final _filename = FileUtils.formatFilenamePDF(event.filename);
      io.File _file;
      _file = event.fromFirebase
          ? await pdfApi.getFirebaseFile(event.url, _filename)
          : await pdfApi.createPDFFileFromUrl(
              event.url,
              _filename,
              event.headers,
            );

      yield PdfByUrlDone(file: _file);
    } catch (e) {
      final String message = 'Não foi possível carregar o documento!';
      yield PdfByUrlError(message);
    }
  }
}
