part of 'pdf_by_url_bloc.dart';

abstract class PdfByUrlState extends Equatable {
  const PdfByUrlState();

  @override
  List<Object> get props => [];
}

class PdfByUrlInitial extends PdfByUrlState {}

class PdfByUrlLoading extends PdfByUrlState {}

class PdfByUrlDone extends PdfByUrlState {
  PdfByUrlDone({required this.file});
  final io.File file;
}

class PdfByUrlError extends PdfByUrlState {
  PdfByUrlError(this.message);
  final String message;
  @override
  List<Object> get props => [message];
}
