part of 'pdf_by_url_bloc.dart';

abstract class PdfByUrlEvent extends Equatable {
  const PdfByUrlEvent();

  @override
  List<Object?> get props => [];
}

class GetPDFByUrl extends PdfByUrlEvent {
  GetPDFByUrl({
    required this.url,
    required this.filename,
    this.fromFirebase = false,
    this.headers,
  });
  final String url;
  final String filename;
  final bool fromFirebase;
  final Map<String, String>? headers;
  @override
  List<Object?> get props => [url];
}

class GetPDFByPath extends PdfByUrlEvent {
  GetPDFByPath({required this.path, required this.filename});
  final String filename;
  final String path;
  @override
  List<Object?> get props => [path];
}

class SetPDFByUrlError extends PdfByUrlEvent {
  SetPDFByUrlError(this.message);
  final String message;
  @override
  List<Object> get props => [message];
}

class ResetState extends PdfByUrlEvent {}
