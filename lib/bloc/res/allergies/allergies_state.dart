import 'package:cliente_minha_unimed/models/res/allergy.model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResAllergyState extends Equatable {}

class ResAllergyInitialState extends ResAllergyState {
  @override
  List<Object> get props => [];
}

class LoadingResAllergyState extends ResAllergyState {
  @override
  List<Object> get props => [];
}

class NoDataResAllergyState extends ResAllergyState {
  @override
  List<Object> get props => [];
}

class ErrorResAllergyState extends ResAllergyState {
  final String message;
  ErrorResAllergyState({required this.message});

  @override
  List<Object?> get props => [];
}

class LoadedResAllergyState extends ResAllergyState {
  final List<ResAllergyModel> listAllergy;
  LoadedResAllergyState({required this.listAllergy});

  @override
  List<Object?> get props => [listAllergy];
}
