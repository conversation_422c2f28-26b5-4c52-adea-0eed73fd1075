// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/allergies/allergies_event.dart';
import 'package:cliente_minha_unimed/bloc/res/allergies/allergies_state.dart';
import 'package:cliente_minha_unimed/models/res/allergy.model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class ResAllergyBloc extends Bloc<ResAllergyEvent, ResAllergyState> {
  ResAllergyBloc() : super(ResAllergyInitialState());
  final logger = UnimedLogger(className: 'ResAllergyBloc');

  List<ResAllergyModel> _listAllergy = [];
  List<ResAllergyModel> get listAllergy => _listAllergy;

  @override
  Stream<ResAllergyState> mapEventToState(
    ResAllergyEvent event,
  ) async* {
    if (event is GetListResAllergyEvent) {
      try {
        yield LoadingResAllergyState();

        _listAllergy =
            await Locator.instance<ResApi>().resListAllergies(cpf: event.cpf);
        if (_listAllergy.isNotEmpty) {
          yield LoadedResAllergyState(
            listAllergy: _listAllergy,
          );
        } else {
          yield NoDataResAllergyState();
        }
      } catch (e) {
        logger.e('Error on RequestCurrentLocationEvent $e');
        yield ErrorResAllergyState(message: e.toString());
      }
    }
  }

  void searchCategoryAllergies({required List<String> categories}) async {
    try {
      emit(LoadingResAllergyState());

      if (categories.isEmpty) {
        emit(LoadedResAllergyState(listAllergy: _listAllergy));
        return;
      }

      if (categories.last == 'Todos') {
        emit(LoadedResAllergyState(listAllergy: _listAllergy));
        return;
      }

      final List<ResAllergyModel> filtredList = _listAllergy.where((element) {
        return categories.any((category) => element.categoria
            .toString()
            .toLowerCase()
            .contains(category.toLowerCase()));
      }).toList();
      emit(LoadedResAllergyState(listAllergy: filtredList));
    } catch (e) {
      emit(ErrorResAllergyState(message: e.toString()));
    }
  }
}
