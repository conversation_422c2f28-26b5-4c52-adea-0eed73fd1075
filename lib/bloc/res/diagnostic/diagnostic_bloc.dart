import 'package:cliente_minha_unimed/bloc/res/diagnostic/diagnostic_event.dart';
import 'package:cliente_minha_unimed/bloc/res/diagnostic/diagnostic_state.dart';
import 'package:cliente_minha_unimed/models/res/diagnostic/res_diagnostic.model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResDiagnosticBloc
    extends Bloc<ResBrazilDiagnosticEvent, ResBrazilDiagnosticState> {
  ResDiagnosticBloc() : super(InitialResDiagnosticState());

  List<ResBrazilDiagnosticModel> _listDiagnostics = [];
  List<ResBrazilDiagnosticModel> get listDiagnostics => _listDiagnostics;

  @override
  Stream<ResBrazilDiagnosticState> mapEventToState(
    ResBrazilDiagnosticEvent event,
  ) async* {
    if (event is GetListResBrazilDiagnosticEvent) {
      try {
        yield LoadingResDiagnosticState();

        _listDiagnostics = (await Locator.instance<ResApi>().resGetDiagnostics(
          card: event.card,
          dateTimeRange: event.dateTimeRange,
        ));

        if (_listDiagnostics.isNotEmpty) {
          yield LoadedResDiagnosticState(listDiagnostics: _listDiagnostics);
        } else {
          yield NoDataResDiagnosticState();
        }
      } catch (e) {
        yield ErrorResDiagnosticState(message: e.toString());
      }
    }
  }
}
