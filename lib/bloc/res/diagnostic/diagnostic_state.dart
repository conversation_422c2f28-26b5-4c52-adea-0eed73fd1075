

import 'package:cliente_minha_unimed/models/res/diagnostic/res_diagnostic.model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilDiagnosticState extends Equatable {
  const ResBrazilDiagnosticState();

  @override
  List<Object> get props => [];
}

class InitialResDiagnosticState extends ResBrazilDiagnosticState {}

class NoDataResDiagnosticState extends ResBrazilDiagnosticState {
  @override
  List<Object> get props => [];
}

class LoadingResDiagnosticState extends ResBrazilDiagnosticState {
  @override
  List<Object> get props => [];
}

class ErrorResDiagnosticState extends ResBrazilDiagnosticState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResDiagnosticState({required this.message});
}

class LoadedResDiagnosticState extends ResBrazilDiagnosticState {
  final List<ResBrazilDiagnosticModel> listDiagnostics;

  @override
  List<Object> get props => [listDiagnostics];

  const LoadedResDiagnosticState({required this.listDiagnostics});
}