import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResBrazilDiagnosticEvent extends Equatable {}

class GetListResBrazilDiagnosticEvent extends ResBrazilDiagnosticEvent {
  final String card;
final DateTimeRange? dateTimeRange;


  @override
  List<Object> get props => [card];

  GetListResBrazilDiagnosticEvent({
    required this.card,
    this.dateTimeRange,
  });
}
