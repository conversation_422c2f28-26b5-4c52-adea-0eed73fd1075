import 'package:equatable/equatable.dart';

abstract class ResConsentState extends Equatable {
  const ResConsentState();
}

class ResConsentInitial extends ResConsentState {
  @override
  List<Object> get props => [];
}

class ResConsentLoading extends ResConsentState {
  @override
  List<Object> get props => [];
}

class ResConsentNoData extends ResConsentState {
  @override
  List<Object> get props => [];
}

class ResConsentError extends ResConsentState {
  final String message;

  const ResConsentError({required this.message});

  @override
  List<Object> get props => [message];
}

class ResConsetLoaded extends ResConsentState {
  final String base64Report;
  @override
  List<Object> get props => [base64Report];

  const ResConsetLoaded(this.base64Report);
}

class ResConsnetTermsAccept extends ResConsentState {
  final String message;
  final bool isAccepted;
  @override
  List<Object> get props => [message];
  const ResConsnetTermsAccept({required this.message, required this.isAccepted});
}

class ResConsentTermsReject extends ResConsentState {
  @override
  List<Object> get props => [];
}

class ResConsentTermsVerifyAccept extends ResConsentState {
  final bool isAccepted;
  @override
  List<Object> get props => [isAccepted];
  const ResConsentTermsVerifyAccept({required this.isAccepted});
}
