import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_conent_terms_state.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_events.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class ResConsentBloc extends Bloc<ResConsentEvent, ResConsentState> {
  ResConsentBloc() : super(ResConsentInitial());
  final logger = UnimedLogger(className: 'ResConsentBloc');

  @override
  Stream<ResConsentState> mapEventToState(ResConsentEvent event) async* {
    if (event is GetpdfConsentTextEvent) {
      yield ResConsentLoading();
      try {
        final String base64Report =
            await Locator.instance<ResApi>().ResPdfConsent(cpf: event.cpf);
        yield ResConsetLoaded(base64Report);
      } catch (e) {
        logger.e('Error on ResConsentErrorEvent $e');
        yield ResConsentError(message: e.toString());
      }
    } else if (event is AnswerTermsConsentEvent) {
      yield ResConsentLoading();
      try {
        final String message = await Locator.instance<ResApi>()
            .ResConsetTermsMutation(cpf: event.cpf, value: event.value);
        yield ResConsnetTermsAccept(message: message, isAccepted: event.value);
      } catch (e) {
        logger.e('Error on GetTermsConsentAccept $e');
        yield ResConsentError(message: e.toString());
      }
    } else if (event is CheckTermsConsentAcceptEvent) {
      try {
        yield ResConsentLoading();
        final bool isAccepted = await Locator.instance<ResApi>()
            .ResConsetTermsVerifyAccept(cpf: event.cpf);
        yield ResConsentTermsVerifyAccept(
          isAccepted: isAccepted,
        );
      } catch (e) {
        logger.e('Error on CheckTermsConsentAcceptEvent $e');
        yield ResConsentError(message: e.toString());
      }
    }
  }
}
