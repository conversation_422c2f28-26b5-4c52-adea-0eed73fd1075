

import 'package:equatable/equatable.dart';

abstract class ResConsentEvent extends Equatable {
  const ResConsentEvent();

  @override
  List<Object> get props => [];
}

class GetpdfConsentTextEvent extends ResConsentEvent {
  final String cpf;

  @override
   List<Object> get props => [cpf];

   GetpdfConsentTextEvent({required this.cpf});
}

class AnswerTermsConsentEvent extends ResConsentEvent {
  final String cpf;
  final bool value;

  @override
  List<Object> get props => [cpf, value];

  AnswerTermsConsentEvent({required this.cpf, required this.value});
}


class CheckTermsConsentAcceptEvent extends ResConsentEvent {
  final String cpf;

  @override
  List<Object> get props => [cpf];

  CheckTermsConsentAcceptEvent({required this.cpf});
}



