

import 'package:cliente_minha_unimed/bloc/res/documentos/document_event.dart';
import 'package:cliente_minha_unimed/bloc/res/documentos/document_state.dart';
import 'package:cliente_minha_unimed/models/res/documents.model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResBrazilDocumentsBloc extends Bloc<ResBrazilDocumentsEvent, ResBrazilDocumentsState> {
  ResBrazilDocumentsBloc() : super(InitialResDocumentsState());


  @override
  Stream<ResBrazilDocumentsState> mapEventToState(
    ResBrazilDocumentsEvent event,
  ) async* {
    if (event is GetListResBrazilDocumentsEvent) {
      try {
        yield LoadingResDocumentsState();

        List<ResBrazilDocumentsModel> _listDocuments = (await Locator.instance<ResApi>().resGetDocuments(
          card: event.card,
          dateTimeRange: event.dateTimeRange,
        )) ;

        if (_listDocuments.isNotEmpty) {
          yield LoadedResDocumentsState(listDocuments: _listDocuments);
        } else {
          yield NoDataResDocumentsState();
          
        }
      } catch (e) {
        yield ErrorResDocumentsState(message: e.toString());
      }
    }
  }


}