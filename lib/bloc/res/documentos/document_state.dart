

import 'package:cliente_minha_unimed/models/res/documents.model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilDocumentsState extends Equatable {
  const ResBrazilDocumentsState();

  @override
  List<Object> get props => [];
}

class InitialResDocumentsState extends ResBrazilDocumentsState {}

class NoDataResDocumentsState extends ResBrazilDocumentsState {
  @override
  List<Object> get props => [];
}

class LoadingResDocumentsState extends ResBrazilDocumentsState {
  @override
  List<Object> get props => [];
}

class ErrorResDocumentsState extends ResBrazilDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResDocumentsState({required this.message});
}

class LoadedResDocumentsState extends ResBrazilDocumentsState {
  final List<ResBrazilDocumentsModel> listDocuments;

  @override
  List<Object> get props => [listDocuments];

  const LoadedResDocumentsState({required this.listDocuments});
}