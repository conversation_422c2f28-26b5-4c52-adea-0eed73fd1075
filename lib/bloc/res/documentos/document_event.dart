

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable 
abstract class ResBrazilDocumentsEvent extends Equatable {}

class GetListResBrazilDocumentsEvent extends ResBrazilDocumentsEvent {
  final String card;
  final DateTimeRange? dateTimeRange;

  @override
  List<Object> get props => [card];

  GetListResBrazilDocumentsEvent({
    required this.card,
    this.dateTimeRange,
  });
}