import 'package:cliente_minha_unimed/models/res/res_indicator_data.model.dart';
import 'package:equatable/equatable.dart';

abstract class ResIndicatorsDataState extends Equatable {
  const ResIndicatorsDataState();

  @override
  List<Object> get props => [];
}

class InitialResIndicatorsDataState extends ResIndicatorsDataState {}

class LoadingResIndicatorsDataState extends ResIndicatorsDataState {
  @override
  List<Object> get props => [];
}

class ErrorResIndicatorDataState extends ResIndicatorsDataState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResIndicatorDataState({required this.message});
}

class LoadedResIndicatorDataState extends ResIndicatorsDataState {
  final List<IndicatorDataModel> resIndicatorsData;

  @override
  List<Object> get props => [resIndicatorsData];

  const LoadedResIndicatorDataState({required this.resIndicatorsData});
}

class NoDataResIndicatorDataState extends ResIndicatorsDataState {
  @override
  List<Object> get props => [];
}
