import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResIndicatorsDataEvent extends Equatable {}

class GetResIndicatorsDataEvent extends ResIndicatorsDataEvent {
  final String cpf;
  final List<String> indicatorsId;

  @override
  List<Object> get props => [cpf, indicatorsId];

  GetResIndicatorsDataEvent({required this.cpf, required this.indicatorsId});
}

class UpdateDateRangeEvent extends ResIndicatorsDataEvent {
  final String cpf;
  final DateTimeRange dateRange;

  @override
  List<Object> get props => [cpf, dateRange];

  UpdateDateRangeEvent({required this.cpf, required this.dateRange});
}
