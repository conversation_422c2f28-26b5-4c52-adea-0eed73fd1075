import 'package:cliente_minha_unimed/bloc/res/alerts/alerts_event.dart';
import 'package:cliente_minha_unimed/bloc/res/alerts/alerts_state.dart';
import 'package:cliente_minha_unimed/models/res/res_alert_model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResAlertsBloc extends Bloc<ResAlertsEvent, ResBrazilAlertsState> {
  ResAlertsBloc() : super(InitialResAlertsState());

  List<ResAlertModel> _listAllerts = [];
  List<ResAlertModel> get listAllerts => _listAllerts;

  @override
  Stream<ResBrazilAlertsState> mapEventToState(
    ResAlertsEvent event,
  ) async* {
    if (event is GetListResAlertsEvent) {
      try {
        yield LoadingResAlertsState();

        _listAllerts = await Locator.instance<ResApi>().resGetAllerts(
          cpf: event.cpf,
          dateTimeRange: event.dateTimeRange,
        );

        if (_listAllerts.isNotEmpty) {
          yield LoadedResAlertsState(listAllerts: _listAllerts);
        } else {
          yield NoDataResAlertsState();
        }
      } catch (e) {
        yield ErrorResAlertsState(message: e.toString());
      }
    }
  }
}
