import 'package:cliente_minha_unimed/models/res/res_alert_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilAlertsState extends Equatable {
  const ResBrazilAlertsState();

  @override
  List<Object> get props => [];
}

class InitialResAlertsState extends ResBrazilAlertsState {}

class NoDataResAlertsState extends ResBrazilAlertsState {
  @override
  List<Object> get props => [];
}

class LoadingResAlertsState extends ResBrazilAlertsState {
  @override
  List<Object> get props => [];
}

class ErrorResAlertsState extends ResBrazilAlertsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAlertsState({required this.message});
}

class LoadedResAlertsState extends ResBrazilAlertsState {
  final List<ResAlertModel> listAllerts;

  @override
  List<Object> get props => [listAllerts];

  const LoadedResAlertsState({required this.listAllerts});
}
