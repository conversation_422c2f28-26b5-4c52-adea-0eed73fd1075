import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-emergency/checkin-data.model.dart';
import 'package:cliente_minha_unimed/models/checkin-surgery/checkin-surgery-data.model.dart';
import 'package:cliente_minha_unimed/models/checkin-surgery/checkin-surgery-sql.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-surgery/checkin_surgery.api.dart';
import 'package:cliente_minha_unimed/shared/utils/sqlite-manager/tables/item_checkin_surgery.table.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

part 'checkin_surgery_info_event.dart';
part 'checkin_surgery_info_state.dart';

const int HOURS_REFESH_ITENS = 6;

class CheckinSurgeryInfoBloc
    extends Bloc<CheckinSurgeryInfoEvent, CheckinSurgeryInfoState> {
  CheckinSurgeryInfoBloc() : super(CheckinInfoSurgeryInitial());

  List<CheckinSurgeryItemModel> professionList = [];
  setProfessionList(List<CheckinSurgeryItemModel> list) =>
      professionList = list;
  List<CheckinSurgeryItemModel> religionList = [];
  setReligionList(List<CheckinSurgeryItemModel> list) => religionList = list;
  List<CheckinSurgeryItemModel> schoolingList = [];
  setSchoolingList(List<CheckinSurgeryItemModel> list) => schoolingList = list;
  List<CheckinSurgeryItemModel> maritalStatusList = [];
  setMaritalStatusList(List<CheckinSurgeryItemModel> list) =>
      maritalStatusList = list;
  List<CheckinSurgeryItemModel> naturalnessList = [];
  setNaturalnessList(List<CheckinSurgeryItemModel> list) =>
      naturalnessList = list;

  String? _nationality;
  String get nationality => _nationality ?? "";

  setNationality(String nat) => _nationality = nat;

  String? _profSelected;
  String? get profSelected => _profSelected;

  String? _schoolingSelected;
  String? get schoolingSelected => _schoolingSelected;

  String? _religionSelected;
  String? get religionSelected => _religionSelected;

  String? _maritalStatusSelected;
  String? get maritalStatusSelected => _maritalStatusSelected;

  String? _naturalnessSelected;
  String? get naturalnessSelected => _naturalnessSelected;

  setProfessionDataModel(String profSelected) => _profSelected = profSelected;
  setReligionDataModel(String religionSelected) =>
      _religionSelected = religionSelected;
  setSchoolingDataModel(String schoolingSelected) =>
      _schoolingSelected = schoolingSelected;
  setMaritalStatusDataModel(String maritalStatusSelected) =>
      _maritalStatusSelected = maritalStatusSelected;
  setNaturalnessDataModel(String naturalnessSelected) =>
      _naturalnessSelected = naturalnessSelected;

  @override
  Stream<CheckinSurgeryInfoState> mapEventToState(
    CheckinSurgeryInfoEvent event,
  ) async* {
    if (event is GetCheckinSurgeryList) {
      yield LoadingCheckinSurgeryInfoState();

      try {
        final checkinSurgeryList = await Locator.instance
            .get<CheckinSurgeryApi>()
            .getCheckinSurgeryList(card: event.card);

        for (int i = 0; i < checkinSurgeryList.length; i++) {
          final protocol = await Locator.instance
              .get<CheckinSurgeryApi>()
              .getCheckinSurgeryProtocol(
                  card: event.card,
                  cdAgeCir: checkinSurgeryList[i].cdAgeCir ?? "");

          if (protocol.isNotEmpty) {
            checkinSurgeryList[i].numProtocoloAns = protocol;
            checkinSurgeryList[i].numProtocolFillMongo = protocol;
          }
        }

        yield LoadedCheckinSurgeryInfoState(
          checkinSugeryList: checkinSurgeryList,
        );
      } catch (e) {
        logger.e('GetCheckinSurgeryList -  Error -  $e');
        yield ErrorCheckinSurgeryInfoState(message: e.toString());
      }
    } else if (event is CleanSelectedEvent) {
      _profSelected = null;

      _religionSelected = null;

      _schoolingSelected = null;

      _maritalStatusSelected = null;

      _nationality = null;
    }
  }
}
