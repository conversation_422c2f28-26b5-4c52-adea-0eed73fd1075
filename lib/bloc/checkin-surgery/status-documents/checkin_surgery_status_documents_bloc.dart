import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-surgery/checkin-surgery-status-documents.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-surgery/checkin_surgery.api.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

part 'checkin_surgery_status_documents_event.dart';
part 'checkin_surgery_status_documents_state.dart';

const int HOURS_REFESH_ITENS = 6;

class CheckinSurgeryStatusDocumentsBloc extends Bloc<
    CheckinSurgeryStatusDocumentsEvent, CheckinSurgeryStatusDocumentsState> {
  CheckinSurgeryStatusDocumentsBloc() : super(CheckinInfoSurgeryInitial());

  @override
  Stream<CheckinSurgeryStatusDocumentsState> mapEventToState(
    CheckinSurgeryStatusDocumentsEvent event,
  ) async* {
    if (event is GetStatusDocumentsEvent) {
      yield LoadingCheckinSurgeryStatusDocumentsState();

      try {
        final statusDocuments = await Locator.instance
            .get<CheckinSurgeryApi>()
            .getStatusDocuments(cdAgeCir: event.cdAgeCir);

        yield LoadedCheckinSurgeryStatusDocumentsState(
          statusDocuments: statusDocuments,
        );
      } catch (e) {
        logger.e('GetCheckinSurgeryList -  Error -  $e');
        yield ErrorCheckinSurgeryStatusDocumentsState(message: e.toString());
      }
    }
  }
}
