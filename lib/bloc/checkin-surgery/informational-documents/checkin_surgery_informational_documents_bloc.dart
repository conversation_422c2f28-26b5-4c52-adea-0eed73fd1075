import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-surgery/checkin-surgery-document.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-surgery/checkin_surgery.api.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';

part 'checkin_surgery_informational_documents_event.dart';
part 'checkin_surgery_informational_documents_state.dart';

const int HOURS_REFESH_ITENS = 6;

class CheckinSurgeryInformationalDocumentsBloc extends Bloc<
    CheckinSurgeryInformationalDocumentsEvent,
    CheckinSurgeryInformationalDocumentsState> {
  CheckinSurgeryInformationalDocumentsBloc()
      : super(CheckinInfoSurgeryInitial());

  @override
  Stream<CheckinSurgeryInformationalDocumentsState> mapEventToState(
    CheckinSurgeryInformationalDocumentsEvent event,
  ) async* {
    if (event is GetInformationalDocumentsEvent) {
      yield LoadingCheckinSurgeryInformationalDocumentsState();

      try {
        final informationalDocuments = await Locator.instance
            .get<CheckinSurgeryApi>()
            .getInformationalDocuments();

        if (informationalDocuments.isEmpty) {
          yield NoDataCheckinSurgeryInformationalDocumentsState(
            message: MessageException.GENERIC_NO_DATA,
          );
        } else {
          yield LoadedCheckinSurgeryInformationalDocumentsState(
              informationalDocuments: informationalDocuments);
        }
      } catch (e) {
        logger.e('GetCheckinSurgeryList -  Error -  $e');
        yield ErrorCheckinSurgeryInformationalDocumentsState(
            message: e.toString());
      }
    }

    if (event is GetBase64InformationalDocumentEvent) {
      yield LoadingCheckinSurgeryDownloadInformationalDocumentState(
        index: event.index,
      );

      try {
        final informationalDocument = await Locator.instance
            .get<CheckinSurgeryApi>()
            .getBase64InformationalDocument(event.codeDocument);

        final _pathFile = await FileUtils.createFileFromString(
          base64String: informationalDocument.documentoBase64,
          extension: FileExtension.PDF,
        );

        debugPrint("======= DOCUMENT");
        debugPrint("======= DOCUMENT : ${event.codeDocument}");

        debugPrint("======= DOCUMENT : $_pathFile");

        yield DownloadedCheckinSurgeryInformationalDocumentsState(
          pathFile: [_pathFile],
          isViewer: event.isViewer,
        );
      } catch (e) {
        logger.e('GetCheckinSurgeryList -  Error -  $e');
        yield ErrorCheckinSurgeryInformationalDocumentsState(
          message: e.toString(),
        );
      }
    }

    if (event is GetAllBase64InformationalDocumentEvent) {
      try {
        List<String> paths = List.empty(growable: true);

        for (int index = 0; index < event.documents.length; index++) {
          yield LoadingCheckinSurgeryDownloadInformationalDocumentState(
              index: index);

          final informationalDocument = await Locator.instance
              .get<CheckinSurgeryApi>()
              .getBase64InformationalDocument(
                event.documents[index].chave!,
              );
          final _pathFile = await FileUtils.createFileFromString(
            base64String: informationalDocument.documentoBase64,
            extension: FileExtension.PDF,
          );

          paths.add(_pathFile);
        }

        yield DownloadedCheckinSurgeryInformationalDocumentsState(
            pathFile: paths, isViewer: false);
      } catch (e) {
        logger.e('GetCheckinSurgeryList -  Error -  $e');
        yield ErrorCheckinSurgeryInformationalDocumentsState(
          message: e.toString(),
        );
      }
    }
  }
}
