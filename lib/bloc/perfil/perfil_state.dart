import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class PerfilState extends Equatable {}

class InitialPerfilState extends PerfilState {
  @override
  List<Object> get props => [];
}

class LoadingPerfilState extends PerfilState {
  @override
  List<Object> get props => [];
}

class ErrorPerfilState extends PerfilState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorPerfilState(this.message);
}

class LoadedProfileState extends PerfilState {
  final Perfil profile;
  final List<ProfileRolesModel>? permissionList;

  @override
  List<Object?> get props => [profile, permissionList];

  LoadedProfileState(this.profile, this.permissionList);
}

class ErrorProfilesPermissionsState extends PerfilState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorProfilesPermissionsState(this.message);
}

class LoadingProfilesPermissionsState extends PerfilState {
  @override
  List<Object> get props => [];
}
