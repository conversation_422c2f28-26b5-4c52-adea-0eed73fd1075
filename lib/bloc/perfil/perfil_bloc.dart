import 'dart:async';

import 'package:cliente_minha_unimed/bloc/perfil/perfil_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/shared/api/profile_roles.api.dart';
import 'package:cliente_minha_unimed/shared/api/websocket.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/version.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class PerfilBloc extends Bloc<PerfilEvent, PerfilState> {
  PerfilBloc() : super(InitialPerfilState());

  late Perfil _perfil;
  Perfil get perfil => _perfil;

  List<ProfileRolesModel>? _permissionList;
  List<ProfileRolesModel>? get permissionList => _permissionList;

  final UnimedLogger logger = UnimedLogger(className: 'PerfilBloc');

  @override
  Stream<PerfilState> mapEventToState(
    PerfilEvent event,
  ) async* {
    if (event is ChangePerfil) {
      yield LoadingPerfilState();

      _perfil = event.user!.getPerfilByCarteira(event.carteira);
      if (_permissionList != null) {
        _perfil.permissions =
            getDefaultProfilePermissions(event.carteira!.carteiraNumero);
      }

      try {
        final version =
            await (Locator.instance.get<VersionService>().getInfo());
        final result = await Locator.instance
            .get<ProfileRolesApi>()
            .getProfilePermissions(event.carteira?.carteiraNumero,
                _permissionList, version.version);

        if (result == null) {
          throw ProfilesException('Falha ao carregar permissões do perfil');
        }

        if (result.permissions.length > 0) {
          _perfil.permissions = result.permissions;
        }

        // Change card in RemoteLog
        Locator.instance
            .get<RemoteLog>()
            .setCard(event.carteira!.carteiraNumero);

        Locator.instance.get<WebSocketApi>().connectWebsocket(_perfil);
        yield LoadedProfileState(_perfil, _permissionList);
      } on ProfilesException catch (e) {
        yield ErrorProfilesPermissionsState(e.toString());
      } catch (ex) {
        //
        logger.e(
            'ChangePerfil try remote permission: Permissão remota da carteira ${event.carteira?.carteiraNumero} não carregada. Usando a default do Login');
        logger.e('ChangePerfil try remote permission: ${ex.toString()}');
        yield ErrorProfilesPermissionsState(ex.toString());
      }
    } else if (event is InitializeProfilesPermissions) {
      try {
        yield LoadingPerfilState();

        _permissionList = event.permissionsList;

        _perfil = event.user!.getPerfilByCarteira(event.carteira);
        _perfil.permissions =
            getDefaultProfilePermissions(event.carteira!.carteiraNumero);

        Locator.instance
            .get<RemoteLog>()
            .setCard(event.carteira!.carteiraNumero);

        Locator.instance.get<WebSocketApi>().connectWebsocket(_perfil);

        yield LoadedProfileState(_perfil, _permissionList);
      } catch (e) {
        yield ErrorProfilesPermissionsState(e.toString());
      }
    } else if (event is InitStateEventPerfil) {
      yield InitialPerfilState();
    } else if (event is SetPermissionsList) {
      _permissionList = event.permissionsList;
    }
  }

  List<ProfileRoles2ScreenModel> getDefaultProfilePermissions(
      String cardNumber) {
    final profile =
        _permissionList!.firstWhere((element) => element.cardId == cardNumber);

    return profile.permissions;
  }
}
