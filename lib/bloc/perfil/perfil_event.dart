import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class PerfilEvent extends Equatable {}

class ChangePerfil extends PerfilEvent {
  final Carteira? carteira;
  final User? user;

  @override
  List<Object?> get props => [carteira, user];

  ChangePerfil({required this.carteira, required this.user});
}

class InitializeProfilesPermissions extends PerfilEvent {
  final Carteira? carteira;
  final User? user;
  final List<ProfileRolesModel>? permissionsList;

  @override
  List<Object> get props => [];

  InitializeProfilesPermissions(
      {required this.user,
      required this.carteira,
      required this.permissionsList});
}

class InitStateEventPerfil extends PerfilEvent {
  @override
  List<Object> get props => [];
}

class SetPermissionsList extends PerfilEvent {
  final List<ProfileRolesModel>? permissionsList;

  @override
  List<Object> get props => [];

  SetPermissionsList({this.permissionsList});
}
