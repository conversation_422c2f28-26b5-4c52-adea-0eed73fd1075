
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../models/general-config/general-config.model.dart';

@immutable
abstract class GeneralConfigState extends Equatable {}

class InitialGeneralConfigState extends GeneralConfigState {
  @override
  List<Object> get props => [];
}

class LoadingGeneralConfigState extends GeneralConfigState {
  @override
  List<Object> get props => [];
}

class ErrorGeneralConfigState extends GeneralConfigState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGeneralConfigState(this.message);
}

class DoneGeneralConfigState extends GeneralConfigState {
  final GeneralConfigModel generalConfigModel;

  @override
  List<Object?> get props => [generalConfigModel];

  DoneGeneralConfigState({required this.generalConfigModel});
}
