import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/general-config/general-config.model.dart';
import 'package:cliente_minha_unimed/shared/api/general-config/general-config.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

import 'general_config_event.dart';
import 'general_config_state.dart';

class GeneralConfigBloc extends Bloc<GeneralConfigEvent, GeneralConfigState> {
  GeneralConfigBloc() : super(InitialGeneralConfigState());

  GeneralConfigModel? _generalConfig;
  GeneralConfigModel? get generalConfigModel => _generalConfig;

  @override
  Stream<GeneralConfigState> mapEventToState(
    GeneralConfigEvent event,
  ) async* {
    if (event is GetGeneralConfigEvent) {
      yield LoadingGeneralConfigState();

      try {
        _generalConfig =
            await Locator.instance.get<GeneralConfigApi>().getGeneralConfig(
                  carteiraId: event.carteiraId,
                );

        yield DoneGeneralConfigState(generalConfigModel: _generalConfig!);
      } catch (err) {
        yield ErrorGeneralConfigState('$err');
      }
    }
  }
}
