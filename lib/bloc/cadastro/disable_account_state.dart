import 'package:cliente_minha_unimed/models/profile-data/send-token-model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class DisableAccountState extends Equatable {}

class InitialDisableAccountState extends DisableAccountState {
  @override
  List<Object> get props => [];
}

class LoadingDisableAccountState extends DisableAccountState {
  @override
  List<Object> get props => [];
}

class SendingTokenDisableAccountState extends DisableAccountState {
  @override
  List<Object> get props => [];
}

class ConfirmingTokenDisableAccountState extends DisableAccountState {
  @override
  List<Object> get props => [];
}

class ConfirmedDisableAccountState extends DisableAccountState {
  final String message;

  @override
  List<Object> get props => [message];

  ConfirmedDisableAccountState({required this.message});
}

class ErrorDisableAccountState extends DisableAccountState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorDisableAccountState(this.message);
}

class ErrorConfirmDisableAccountState extends DisableAccountState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorConfirmDisableAccountState(this.message);
}

class DoneDisableAccountState extends DisableAccountState {
  final SendTokenModel sendTokenModel;

  @override
  List<Object> get props => [sendTokenModel];

  DoneDisableAccountState({required this.sendTokenModel});
}
