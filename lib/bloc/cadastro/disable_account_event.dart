import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';

abstract class DisableAccountEvent extends Equatable {}

class SendTokenDisableAccountEvent extends DisableAccountEvent {
  final String cpf;
  final Perfil perfil;

  @override
  List<Object> get props => [perfil, cpf];

  SendTokenDisableAccountEvent({required this.perfil, required this.cpf});
}

class ConfirmTokenDisableAccountEvent extends DisableAccountEvent {
  final String cpf;
  final String idToken;
  final String token;

  @override
  List<Object> get props => [idToken, token, cpf];

  ConfirmTokenDisableAccountEvent({
    required this.idToken,
    required this.token,
    required this.cpf,
  });
}

class SetToInicialDisableAccountEvent extends DisableAccountEvent {
  SetToInicialDisableAccountEvent();

  @override
  List<Object> get props => [];
}
