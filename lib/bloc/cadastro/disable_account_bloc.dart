import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/cadastro/disable_account_event.dart';
import 'package:cliente_minha_unimed/bloc/cadastro/disable_account_state.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class DisableAccountBloc
    extends Bloc<DisableAccountEvent, DisableAccountState> {
  DisableAccountBloc() : super(InitialDisableAccountState());
  final logger = UnimedLogger(className: 'DisableAccountBloc');

  @override
  Stream<DisableAccountState> mapEventToState(
    DisableAccountEvent event,
  ) async* {
    if (event is SendTokenDisableAccountEvent) {
      yield SendingTokenDisableAccountState();

      final api = Locator.instance.get<BeneficiarioApi>();

      try {
        final sendTokenModel = await api.sendDisableAccountToken(
          perfil: event.perfil,
          cpf: event.cpf,
        );

        yield DoneDisableAccountState(sendTokenModel: sendTokenModel);
      } catch (e) {
        // logger.e('BLOC $e');
        if (e is SocketException || e is HandshakeException)
          yield ErrorDisableAccountState(
            MessageException.GENERAL,
          );
        if (e is SignupException) yield ErrorDisableAccountState(e.message);
        yield ErrorDisableAccountState("$e");
      }
    } else if (event is ConfirmTokenDisableAccountEvent) {
      yield ConfirmingTokenDisableAccountState();

      final api = Locator.instance.get<BeneficiarioApi>();

      try {
        final message = await api.confirmDisableAccountToken(
          token: event.token,
          idToken: event.idToken,
          cpf: event.cpf,
        );

        yield ConfirmedDisableAccountState(message: message);
      } catch (e) {
        if (e is SocketException || e is HandshakeException)
          yield ErrorConfirmDisableAccountState(MessageException.GENERAL);
        if (e is SignupException) yield ErrorDisableAccountState(e.message);
        yield ErrorConfirmDisableAccountState("$e");
      }
    } else if (event is SetToInicialDisableAccountEvent) {
      yield InitialDisableAccountState();
    }
  }
}
