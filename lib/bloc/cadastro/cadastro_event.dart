import 'package:cliente_minha_unimed/shared/api/cadastro.api.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class CadastroEvent extends Equatable {}

class ChangeCadastroEvent extends CadastroEvent {
  final EnvioCadastro dao;

  @override
  List<Object> get props => [dao];

  ChangeCadastroEvent(this.dao);
}

class InicioCadastroEvent extends CadastroEvent {
  @override
  List<Object> get props => [];
}

class GetPasswordRulesEvent extends CadastroEvent {
  @override
  List<Object> get props => [];

  GetPasswordRulesEvent();
}
