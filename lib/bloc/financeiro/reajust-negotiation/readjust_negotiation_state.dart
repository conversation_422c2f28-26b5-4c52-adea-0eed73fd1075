import 'package:cliente_minha_unimed/shared/api/vo/financeiro/readjust-negotiation.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ReadjustNegotiationState extends Equatable {}

class ReadjustNegotiationInitial extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class LoadingAllInfoState extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class ErrorAllInfoState extends ReadjustNegotiationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorAllInfoState(this.message);
}

class DoneAllInfoState extends ReadjustNegotiationState {
  final List<ResponseReadjustAnsVO>? list;

  @override
  List<Object?> get props => [list];

  DoneAllInfoState({this.list});
}

class LoadingGetBalanceNegotiationState extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class ErrorGetBalanceNegotiationState extends ReadjustNegotiationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGetBalanceNegotiationState(this.message);
}

class DoneGetBalanceNegotiationState extends ReadjustNegotiationState {
  final ResponseBalanceReadjustVO? balance;

  @override
  List<Object?> get props => [balance];

  DoneGetBalanceNegotiationState({this.balance});
}

class LoadingSendNegotiationState extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class ErrorSendNegotiationState extends ReadjustNegotiationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSendNegotiationState(this.message);
}

class DoneSendNegotiationState extends ReadjustNegotiationState {
  final ResponseReadjustNegotiation? readjustNegotiation;

  @override
  List<Object?> get props => [readjustNegotiation];

  DoneSendNegotiationState({this.readjustNegotiation});
}

class LoadingGetReadjustHistoryState extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class ErrorGetReadjustHistoryState extends ReadjustNegotiationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGetReadjustHistoryState(this.message);
}

class DoneGetReadjustHistoryState extends ReadjustNegotiationState {
  final List<ResponseReadjustHistoryVO>? list;

  @override
  List<Object?> get props => [list];

  DoneGetReadjustHistoryState({this.list});
}

class NoDataReadjustHistoryState extends ReadjustNegotiationState {
  final String message;
  @override
  List<Object?> get props => [message];
  NoDataReadjustHistoryState({required this.message});
}

class LoadingGetMessageReadjustState extends ReadjustNegotiationState {
  @override
  List<Object> get props => [];
}

class ErrorGetMessageReadjustState extends ReadjustNegotiationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGetMessageReadjustState(this.message);
}

class DoneGetMessageReadjustState extends ReadjustNegotiationState {
  final String? message;

  @override
  List<Object?> get props => [message];

  DoneGetMessageReadjustState({this.message});
}
