import 'package:cliente_minha_unimed/models/beneficiario-data.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/financeiro/readjust-negotiation.vo.dart';
import 'package:equatable/equatable.dart';

abstract class ReadjustNegotiationEvent extends Equatable {
  const ReadjustNegotiationEvent();

  @override
  List<Object?> get props => [];
}

class GetAllInfoReadjust extends ReadjustNegotiationEvent {
  final Perfil perfil;
  final int cpfLogado;

  @override
  List<Object> get props => [perfil, cpfLogado];

  GetAllInfoReadjust({
    required this.perfil,
    required this.cpfLogado,
  });
}

class GetBalanceNegotiation extends ReadjustNegotiationEvent {
  final Perfil? perfil;
  final BeneficiarioData? beneficiario;

  @override
  List<Object?> get props => [perfil, beneficiario];

  GetBalanceNegotiation({required this.perfil, required this.beneficiario});
}

class SendNewReadjust extends ReadjustNegotiationEvent {
  final Perfil? perfil;
  final BeneficiarioData? beneficiario;
  final int? qtdParcelas;
  final String? competencia;
  final ResponseBalanceReadjustVO balance;
  final String email;
  final String cpfLogado;
  final String? ip;
  final DateTime date;

  @override
  List<Object?> get props => [
        perfil,
        beneficiario,
        qtdParcelas,
        competencia,
        balance,
        email,
        cpfLogado,
        ip,
        date
      ];

  SendNewReadjust(
      {required this.perfil,
      required this.beneficiario,
      required this.qtdParcelas,
      required this.competencia,
      required this.balance,
      required this.cpfLogado,
      required this.email,
      required this.ip,
      required this.date});
}

class GetReadjustHistory extends ReadjustNegotiationEvent {
  final Perfil? perfil;

  @override
  List<Object?> get props => [perfil];

  GetReadjustHistory({required this.perfil});
}

class GetMessageReadjust extends ReadjustNegotiationEvent {
  final String carteira;

  @override
  List<Object> get props => [];

  GetMessageReadjust({required this.carteira});
}
