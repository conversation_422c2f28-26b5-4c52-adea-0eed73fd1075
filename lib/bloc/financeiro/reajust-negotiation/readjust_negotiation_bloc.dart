import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/reajust-negotiation/readjust_negotiation_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/reajust-negotiation/readjust_negotiation_state.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/readjust-negotiation.dart';
import 'package:cliente_minha_unimed/shared/api/general-messages/general_mesages.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';

class ReadjustNegotiationBloc extends Bloc<ReadjustNegotiationEvent, ReadjustNegotiationState> {
  ReadjustNegotiationBloc() : super(ReadjustNegotiationInitial());

  @override
  Stream<ReadjustNegotiationState> mapEventToState(
    ReadjustNegotiationEvent event,
  ) async* {
    if (event is GetMessageReadjust) {
      yield LoadingGetMessageReadjustState();

      try {
        final _message = await Locator.instance.get<GeneralMessagesApi>().getReajustMessage(carteira: event.carteira);
        yield DoneGetMessageReadjustState(message: _message);
      } catch (ex) {
        yield ErrorGetMessageReadjustState('$ex');
      }
    } else if (event is GetAllInfoReadjust) {
      yield LoadingAllInfoState();

    } else if (event is GetBalanceNegotiation) {
      yield LoadingGetBalanceNegotiationState();

      try {
        final _balance = await Locator.instance.get<ReadjustNegotiationApi>().getBalanceReadjust(perfil: event.perfil!, beneficiarioData: event.beneficiario!);

        yield DoneGetBalanceNegotiationState(balance: _balance);
      } catch (err) {
        yield ErrorGetBalanceNegotiationState('$err');
      }
    } else if (event is SendNewReadjust) {
      yield LoadingSendNegotiationState();

      try {
        final _response = await Locator.instance
            .get<ReadjustNegotiationApi>()
            .sendReadjustNegotiation(perfil: event.perfil!, beneficiario: event.beneficiario!, qtdParcelas: event.qtdParcelas, competencia: event.competencia, balance: event.balance, cpfLogado: event.cpfLogado, email: event.email, ip: event.ip, date: event.date);

        yield DoneSendNegotiationState(readjustNegotiation: _response);
      } catch (err) {
        yield ErrorSendNegotiationState('$err');
      }
    } else if (event is GetReadjustHistory) {
      yield LoadingGetReadjustHistoryState();

      try {
        final _response = await Locator.instance.get<ReadjustNegotiationApi>().listReadjustHistory(
              perfil: event.perfil!,
            );

        if (_response.length == 0) {
          yield NoDataReadjustHistoryState(message: MessageException.FINANCE_NO_DATA_NEGOTIATIONS);
        } else {
          yield DoneGetReadjustHistoryState(list: _response);
        }
      } catch (err) {
        yield ErrorGetReadjustHistoryState('$err');
      }
    }
  }
}
