import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';

@immutable
abstract class CodigoBarrasFaturaState extends Equatable {}

class InitialCodigoBarrasFaturaState extends CodigoBarrasFaturaState {
  @override
  List<Object> get props => [];
}

class LoadingCodigoBarrasFaturaState extends CodigoBarrasFaturaState {
  final FaturaLista fatura;

  @override
  List<Object> get props => [fatura];

  LoadingCodigoBarrasFaturaState({required this.fatura});
}

class ErrorCodigoBarrasFaturaState extends CodigoBarrasFaturaState {
  final String message;
  final FaturaLista fatura;

  @override
  List<Object> get props => [message, fatura];

  ErrorCodigoBarrasFaturaState({
    required this.message,
    required this.fatura,
  });
}

class DoneCodigoBarrasFaturaState extends CodigoBarrasFaturaState {
  final String codigoBarras;
  final FaturaLista fatura;

  @override
  List<Object> get props => [codigoBarras, fatura];

  DoneCodigoBarrasFaturaState({
    required this.codigoBarras,
    required this.fatura,
  });
}
