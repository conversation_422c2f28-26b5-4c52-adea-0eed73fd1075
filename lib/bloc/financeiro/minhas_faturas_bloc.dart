import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/minhas_faturas_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/minhas_faturas_state.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class MinhasFaturasBloc extends Bloc<MinhasFaturasEvent, MinhasFaturasState> {
  MinhasFaturasBloc() : super(InitialMinhasFaturasState());
  final logger = UnimedLogger(className: 'MinhasFaturasBloc');

  @override
  Stream<MinhasFaturasState> mapEventToState(MinhasFaturasEvent event) async* {
    if (event is GetAllFaturasEvent) {
      yield LoadingMinhasFaturasState();

      try {
        final _lista =
            await Locator.instance.get<FinanceiroApi>().getAllFaturas(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                );

        yield DoneMinhasFaturasState(faturas: _lista);
      } on AssertionError catch (ex) {
        logger.e('getAllFaturas try $ex');
        yield ErrorMinhasFaturasState(
            "Erro ao recuperar o número da fatura. Tente novamente mais tarde.");
      } on FinanceiroWarningException catch (err) {
        yield WarningMinhasFaturasState('$err');
      } catch (err) {
        yield ErrorMinhasFaturasState('$err');
      }
    }
  }
}
