import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class StatusNegociacoesEvent extends Equatable {}

class GetStatusNegociacoesEvent extends StatusNegociacoesEvent {
  final Perfil? perfil;
  final int cpfLogado;

  @override
  List<Object?> get props => [perfil, cpfLogado];

  GetStatusNegociacoesEvent({
    required this.perfil,
    required this.cpfLogado,
  });
}
