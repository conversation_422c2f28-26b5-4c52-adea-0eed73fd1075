import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/status-negociacao.dart';

@immutable
abstract class StatusNegociacoesState extends Equatable {}

class InitialStatusNegociacoesState extends StatusNegociacoesState {
  @override
  List<Object> get props => [];
}

class LoadingStatusNegociacoesState extends StatusNegociacoesState {
  @override
  List<Object> get props => [];
}

class ErrorStatusNegociacoesState extends StatusNegociacoesState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorStatusNegociacoesState(this.message);
}

class DoneStatusNegociacoesState extends StatusNegociacoesState {
  final List<StatusNegociacao>? listStatus;

  @override
  List<Object?> get props => [listStatus];

  DoneStatusNegociacoesState({this.listStatus});
}

class NoDataStatusState extends StatusNegociacoesState {
  @override
  List<Object> get props => [];
}
