import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/status-negociacao/status_negociacao_state.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/status-negociacao/status_negociacao_event.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class StatusNegociacoesBloc
    extends Bloc<StatusNegociacoesEvent, StatusNegociacoesState> {
  StatusNegociacoesBloc() : super(InitialStatusNegociacoesState());

  @override
  Stream<StatusNegociacoesState> mapEventToState(
    StatusNegociacoesEvent event,
  ) async* {
    if (event is GetStatusNegociacoesEvent) {
      yield LoadingStatusNegociacoesState();
      try {
        final _lista =
            await Locator.instance.get<FinanceiroApi>().getStatusNegociacoes(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                );
        if (_lista.length > 0) {
          yield DoneStatusNegociacoesState(listStatus: _lista);
        } else {
          yield NoDataStatusState();
        }
      } catch (err) {
        yield ErrorStatusNegociacoesState('$err');
      }
    }
  }
}
