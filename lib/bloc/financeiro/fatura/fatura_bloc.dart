import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

part 'fatura_event.dart';
part 'fatura_state.dart';

class FaturaBloc extends Bloc<FaturaEvent, FaturaState> {
  FaturaBloc() : super(FaturaInitial());
  final logger = UnimedLogger(className: 'FaturaBloc');

  FaturaLista? _fatura;

  FaturaLista? get fatura => _fatura;

  @override
  Stream<FaturaState> mapEventToState(
    FaturaEvent event,
  ) async* {
    try {
      if (event is GerarFaturaEvent) {
        _fatura = event.fatura;

        yield LoadingFatura(fatura: event.fatura);
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        final protocolNumber = await Locator.instance
            .get<AutorizacoesApi>()
            .getProtocolSolicitationNumber(
                perfil: event.perfil!,
                comunicaoInterna:
                    'Geração da fatura de ${event.fatura!.anoVencimento}/${event.fatura!.mesVencimento}',
                atendimentoId: event.atendimentoId,
                cpfLogin: event.cpfLogin,
                token: token);
        final _faturaPdf =
            await Locator.instance.get<FinanceiroApi>().getFaturaPdf(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                  anoFatura: event.fatura!.anoVencimento,
                  mesFatura: event.fatura!.mesVencimento,
                  protocolNumber: protocolNumber.protocolo,
                );

        final _pathFile = await FileUtils.createFileFromString(
            base64String: _faturaPdf, extension: FileExtension.PDF);

        logger.d(
            'base64 generated length ${_faturaPdf.length} => pdf file path $_pathFile');

        yield LoadedFatura(
            fatura: event.fatura,
            pathFile: _pathFile,
            protocolNumber: protocolNumber);
      }
      if (event is GetEmailRecebimento) {
        yield LoadingEmailRecebimento();
        try {
          final _email = await (Locator.instance
              .get<FinanceiroApi>()
              .buscaEmailRecebimento(perfil: event.perfil!));
          if (_email!.isNotEmpty) {
            yield LoadedEmailRecebimento(email: _email);
          } else {
            yield DontHaveEmailState();
          }
        } catch (ex) {
          yield LoadErrorEmailRecebimento(message: '$ex');
        }
      }
    } on FinanceiroException catch (e) {
      logger.d('FinanceiroException 1 => ${e.message}');
      yield LoadErrorFatura(message: e.message);
    } on UnimedException catch (e) {
      logger.d('UnimedException 2 => ${e.message}');
      yield LoadErrorFatura(message: e.message);
    } on Exception catch (e) {
      logger.d('UnimedException 3 => ${e.toString}');
      yield LoadErrorFatura(
          message: 'Não foi possível acessar as faturas no momento.');
    }
  }
}
