import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/negociacao-lista.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class SimularNegociacaoEvent extends Equatable {}

class GetSimulacaoEvent extends SimularNegociacaoEvent {
  final Perfil? perfil;
  final int cpfLogado;
  final List<NegociacaoLista>? negociacaoLista;

  @override
  List<Object?> get props => [perfil, cpfLogado];

  GetSimulacaoEvent(
      {required this.perfil,
      required this.cpfLogado,
      required this.negociacaoLista});
}
