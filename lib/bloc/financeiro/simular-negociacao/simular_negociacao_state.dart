import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/simular-negociacao-lista.dart';

@immutable
abstract class SimularNegociacaoState extends Equatable {}

class InitialSimularNegociacaoState extends SimularNegociacaoState {
  @override
  List<Object> get props => [];
}

class LoadingSimularNegociacaoState extends SimularNegociacaoState {
  @override
  List<Object> get props => [];
}

class ErrorSimularNegociacaoState extends SimularNegociacaoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSimularNegociacaoState(this.message);
}

class DoneSimularNegociacaoState extends SimularNegociacaoState {
  final SimularNegociacaoLista? negociacoes;

  @override
  List<Object?> get props => [negociacoes];

  DoneSimularNegociacaoState({this.negociacoes});
}
