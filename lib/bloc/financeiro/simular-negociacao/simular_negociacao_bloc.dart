import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/simular-negociacao/simular_negociacao_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/simular-negociacao/simular_negociacao_state.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class SimularNegociacaoBloc
    extends Bloc<SimularNegociacaoEvent, SimularNegociacaoState> {
  SimularNegociacaoBloc() : super(InitialSimularNegociacaoState());

  @override
  Stream<SimularNegociacaoState> mapEventToState(
    SimularNegociacaoEvent event,
  ) async* {
    if (event is GetSimulacaoEvent) {
      yield LoadingSimularNegociacaoState();

      try {
        final _lista = await Locator.instance
            .get<FinanceiroApi>()
            .simularNegociacao(
                perfil: event.perfil!,
                cpfLogado: event.cpfLogado,
                negociacao: event.negociacaoLista!);

        yield DoneSimularNegociacaoState(negociacoes: _lista);
      } catch (err) {
        yield ErrorSimularNegociacaoState('$err');
      }
    }
  }
}
