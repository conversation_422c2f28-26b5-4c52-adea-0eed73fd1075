import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class MinhasFaturasEvent extends Equatable {}

class GetAllFaturasEvent extends MinhasFaturasEvent {
  final Perfil? perfil;
  final int cpfLogado;

  @override
  List<Object?> get props => [perfil, cpfLogado];

  GetAllFaturasEvent({
    required this.perfil,
    required this.cpfLogado,
  });
}
