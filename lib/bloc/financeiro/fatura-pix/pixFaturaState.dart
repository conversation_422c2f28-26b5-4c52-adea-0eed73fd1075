



// Estados
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:equatable/equatable.dart';

abstract class PixFaturaState extends Equatable {
  const PixFaturaState();

  @override
  List<Object> get props => [];
}

class InitialPixFaturaState extends PixFaturaState {}

class LoadingPixFaturaState extends PixFaturaState {
  final FaturaLista fatura;

  const LoadingPixFaturaState({required this.fatura});

  @override
  List<Object> get props => [fatura];
}

class DonePixFaturaState extends PixFaturaState {
  final FaturaLista fatura;
  final String pixCopiaCola;
   final String pixQrCode;

  const DonePixFaturaState({
    required this.fatura,
    required this.pixCopiaCola,
    required this.pixQrCode,
  });

  @override
  List<Object> get props => [fatura, pixCopiaCola, pixQrCode];
}

class ErrorPixFaturaState extends PixFaturaState {
  final FaturaLista fatura;
  final String message;

  const ErrorPixFaturaState({
    required this.fatura,
    required this.message,
  });

  @override
  List<Object> get props => [fatura, message];
}