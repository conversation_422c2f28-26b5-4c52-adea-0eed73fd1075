import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';


abstract class PixFaturaEvent extends Equatable {
  const PixFaturaEvent();

  @override
  List<Object> get props => [];
}

class GeneratePixCode extends PixFaturaEvent {
  final String invoiceDate;
 final Perfil perfil;
  final FaturaLista fatura;

  const GeneratePixCode({
    required this.invoiceDate,
    required this.perfil,
    required this.fatura,
  });

  @override
  List<Object> get props => [invoiceDate, perfil, fatura];
}


