part of 'fatura_email_bloc.dart';

@immutable
abstract class FaturaEmailEvent {}

class EnviarFaturaEmailEvent extends FaturaEmailEvent {
  final Perfil? perfil;
  final int cpfLogado;
  final FaturaLista? fatura;
  final emailBeneficiario;
  final String? atendimentoId;
  final String? cpfLogin;

  EnviarFaturaEmailEvent(
      {required this.perfil,
      required this.cpfLogado,
      required this.emailBeneficiario,
      this.fatura,
      this.atendimentoId,
      this.cpfLogin});
}

class AlterarEmailEvent extends FaturaEmailEvent {
  final Perfil? perfil;
  final int cpfLogado;
  final String novoEmail;
  final String? atendimentoId;
  final String? cpfLogin;

  AlterarEmailEvent(
      {required this.perfil,
      required this.cpfLogado,
      required this.novoEmail,
      this.atendimentoId,
      this.cpfLogin});
}
