part of 'fatura_email_bloc.dart';

@immutable
abstract class FaturaEmailState {}

class FaturaEmailInitial extends FaturaEmailState {}

class LoadingFaturaEmail extends FaturaEmailState {
  final FaturaLista? fatura;

  LoadingFaturaEmail({this.fatura});
}

class LoadedFaturaEmail extends FaturaEmailState {
  final String message;
  final FaturaLista? fatura;

  LoadedFaturaEmail({
    required this.message,
    required this.fatura,
  });
}

class LoadErrorFaturaEmail extends FaturaEmailState {
  final message;

  LoadErrorFaturaEmail({this.message});
}

class LoadingAlterarEmail extends FaturaEmailState {}

class LoadedAlterarEmail extends FaturaEmailState {
  final String? message;

  LoadedAlterarEmail({this.message});
}

class LoadErrorAlterarEmail extends FaturaEmailState {
  final String? message;

  LoadErrorAlterarEmail({this.message});
}
