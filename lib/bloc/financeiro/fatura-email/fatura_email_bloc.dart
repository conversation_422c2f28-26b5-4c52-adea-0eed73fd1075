import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

part 'fatura_email_event.dart';
part 'fatura_email_state.dart';

class FaturaEmailBloc extends Bloc<FaturaEmailEvent, FaturaEmailState> {
  FaturaEmailBloc() : super(FaturaEmailInitial());
  final logger = UnimedLogger(className: 'FaturaBloc');

  @override
  Stream<FaturaEmailState> mapEventToState(
    FaturaEmailEvent event,
  ) async* {
    try {
      if (event is EnviarFaturaEmailEvent) {
        yield LoadingFaturaEmail(fatura: event.fatura);
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        final protocolNumber = await Locator.instance
            .get<AutorizacoesApi>()
            .getProtocolSolicitationNumber(
                perfil: event.perfil!,
                comunicaoInterna: 'Envio de fatura por email',
                atendimentoId: event.atendimentoId,
                cpfLogin: event.cpfLogin,
                token: token);

        final msgRetorno =
            await Locator.instance.get<FinanceiroApi>().sendFaturaEmail(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                  anoFatura: event.fatura!.anoVencimento,
                  mesFatura: event.fatura!.mesVencimento,
                  emailBeneficiario: event.emailBeneficiario,
                  protocolNumber: protocolNumber.protocolo,
                );

        yield LoadedFaturaEmail(
            message: '${protocolNumber.message}\n\n$msgRetorno',
            fatura: event.fatura);
      }
    } catch (e) {
      yield LoadErrorFaturaEmail(message: e.toString());
    }
    if (event is AlterarEmailEvent) {
      yield LoadingAlterarEmail();
      try {
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        final protocolNumber = await Locator.instance
            .get<AutorizacoesApi>()
            .getProtocolSolicitationNumber(
                perfil: event.perfil!,
                comunicaoInterna:
                    'Alteração de email de recebimento de fatura para: ${event.novoEmail}',
                atendimentoId: event.atendimentoId,
                cpfLogin: event.cpfLogin,
                token: token);

        final msgRetorno =
            await Locator.instance.get<FinanceiroApi>().alterarEmailRecebimento(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                  novoEmail: event.novoEmail,
                  protocolNumber: protocolNumber.protocolo,
                );

        yield LoadedAlterarEmail(
            message: '${protocolNumber.message}\n\n$msgRetorno');
      } on FinanceiroException catch (e) {
        yield LoadErrorAlterarEmail(message: e.message);
      }
    }
  }
}
