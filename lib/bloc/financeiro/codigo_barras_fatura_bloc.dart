import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/codigo_barras_fatura_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/codigo_barras_fatura_state.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';

class CodigoBarrasFaturaBloc
    extends Bloc<CodigoBarrasFaturaEvent, CodigoBarrasFaturaState> {
  final logger = UnimedLogger(className: 'CodigoBarrasFaturaBloc');

  CodigoBarrasFaturaBloc() : super(InitialCodigoBarrasFaturaState());

  @override
  Stream<CodigoBarrasFaturaState> mapEventToState(
      CodigoBarrasFaturaEvent event) async* {
    if (event is Generate) {
      yield LoadingCodigoBarrasFaturaState(fatura: event.fatura);

      try {
        final _codBarras =
            await Locator.instance.get<FinanceiroApi>().getFaturaCodigoBarras(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                  anoFatura: event.fatura.anoVencimento,
                  mesFatura: event.fatura.mesVencimento,
                );

        logger.i('codBarras gerado: ${StringUtils.onlyNumber(_codBarras!)}');

        yield DoneCodigoBarrasFaturaState(
          codigoBarras: StringUtils.onlyNumber(_codBarras),
          fatura: event.fatura,
        );
      } on FinanceiroException catch (err) {
        yield ErrorCodigoBarrasFaturaState(
          message: err.message,
          fatura: event.fatura,
        );
      }
    } else if (event is GoToInitStateFaturaEvent) {
      yield InitialCodigoBarrasFaturaState();
    }
  }
}
