import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/confirma-negociacao.model.dart';
import 'package:cliente_minha_unimed/models/retorno-pagamento.model.dart';

@immutable
abstract class PagamentoDebitoState extends Equatable {}

class InitialPagamentoDebitoState extends PagamentoDebitoState {
  @override
  List<Object> get props => [];
}

class LoadingPagamentoDebitoState extends PagamentoDebitoState {
  @override
  List<Object> get props => [];
}

class ErrorPagamentoDebitoState extends PagamentoDebitoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorPagamentoDebitoState(this.message);
}

class DonePagamentoDebitoState extends PagamentoDebitoState {
  final RetornoPagamento retornoPagamento;

  @override
  List<Object?> get props => [retornoPagamento];

  DonePagamentoDebitoState({required this.retornoPagamento});
}

class LoadingConfirmaNegociacaoState extends PagamentoDebitoState {
  @override
  List<Object> get props => [];
}

class ErrorConfirmaNegociacaoState extends PagamentoDebitoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorConfirmaNegociacaoState(this.message);
}

class DoneConfirmaNegociacaoState extends PagamentoDebitoState {
  final ConfirmaNegociacao? confirmaNegociacao;

  @override
  List<Object?> get props => [confirmaNegociacao];

  DoneConfirmaNegociacaoState({this.confirmaNegociacao});
}
