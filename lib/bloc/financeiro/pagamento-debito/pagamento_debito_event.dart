import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/getnet.model.dart';
import 'package:cliente_minha_unimed/models/negociacao-lista.model.dart';
import 'package:cliente_minha_unimed/models/simular-negociacao-lista.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class PagamentoDebitoEvent extends Equatable {}

class PagarNegociacaoDebito extends PagamentoDebitoEvent {
  final Perfil? perfil;
  final CardModel card;
  final int cpfLogado;
  final String? protocolo;
  final String valorDivida;
  final int? qtdParcelas;
  final int? codNegociacao;

  PagarNegociacaoDebito({
    required this.perfil,
    required this.card,
    required this.cpfLogado,
    required this.protocolo,
    required this.valorDivida,
    required this.qtdParcelas,
    required this.codNegociacao,
  });

  @override
  List<Object?> get props => [
        perfil,
        card,
        cpfLogado,
        protocolo,
        valorDivida,
        qtdParcelas,
        codNegociacao,
      ];
}

class GetConfirmacaoNegociacao extends PagamentoDebitoEvent {
  final Perfil? perfil;
  final int cpfLogado;
  final String? email;
  final String? celular;
  final String qtdParcela;
  final SimularNegociacaoLista? simularNegociacaoLista;
  final List<NegociacaoLista>? negociacaoLista;

  GetConfirmacaoNegociacao({
    required this.perfil,
    required this.cpfLogado,
    required this.email,
    required this.qtdParcela,
    required this.celular,
    required this.simularNegociacaoLista,
    required this.negociacaoLista,
  });

  @override
  List<Object?> get props => [
        perfil,
        cpfLogado,
        email,
        qtdParcela,
        celular,
        simularNegociacaoLista,
        negociacaoLista,
      ];
}
