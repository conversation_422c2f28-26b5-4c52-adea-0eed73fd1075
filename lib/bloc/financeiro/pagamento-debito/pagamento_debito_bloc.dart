import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/pagamento-debito/pagamento_debito_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/pagamento-debito/pagamento_debito_state.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/api/getnet.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';

class PagamentoDebitoBloc
    extends Bloc<PagamentoDebitoEvent, PagamentoDebitoState> {
  PagamentoDebitoBloc() : super(InitialPagamentoDebitoState());

  @override
  Stream<PagamentoDebitoState> mapEventToState(
    PagamentoDebitoEvent event,
  ) async* {
    if (event is PagarNegociacaoDebito) {
      yield LoadingPagamentoDebitoState();

      try {
        debugPrint("======== BLOC PARCELAS: ${event.qtdParcelas}");
        final respPagamento = await Locator.instance
            .get<GetNetApi>()
            .pagamentoView(
                perfil: event.perfil,
                card: event.card,
                protocolo: event.protocolo,
                valorDivida: event.valorDivida,
                qtdParcelas: event.qtdParcelas,
                cpfLogado: event.cpfLogado,
                codNegociacao: event.codNegociacao);

        yield DonePagamentoDebitoState(retornoPagamento: respPagamento);
      } catch (err) {
        yield ErrorPagamentoDebitoState('$err');
      }
    }
    if (event is GetConfirmacaoNegociacao) {
      yield LoadingConfirmaNegociacaoState();

      try {
        final _lista = await Locator.instance
            .get<FinanceiroApi>()
            .confirmaNegociacao(
                perfil: event.perfil!,
                cpfLogado: event.cpfLogado,
                email: event.email,
                celular: event.celular,
                qtdParcela: event.qtdParcela,
                simularNegociacaoLista: event.simularNegociacaoLista,
                negociacao: event.negociacaoLista!);

        yield DoneConfirmaNegociacaoState(confirmaNegociacao: _lista);
      } catch (err) {
        yield ErrorConfirmaNegociacaoState('$err');
      }
    }
  }
}
