import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';

@immutable
abstract class MinhasFaturasState extends Equatable {}

class InitialMinhasFaturasState extends MinhasFaturasState {
  @override
  List<Object> get props => [];
}

class LoadingMinhasFaturasState extends MinhasFaturasState {
  @override
  List<Object> get props => [];
}

class WarningMinhasFaturasState extends MinhasFaturasState {
  final String message;

  @override
  List<Object> get props => [message];

  WarningMinhasFaturasState(this.message);
}

class ErrorMinhasFaturasState extends MinhasFaturasState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorMinhasFaturasState(this.message);
}

class DoneMinhasFaturasState extends MinhasFaturasState {
  final List<FaturaLista>? faturas;

  @override
  List<Object?> get props => [faturas];

  DoneMinhasFaturasState({this.faturas});
}
