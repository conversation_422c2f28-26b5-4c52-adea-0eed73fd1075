import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class CodigoBarrasFaturaEvent extends Equatable {}

class Generate extends CodigoBarrasFaturaEvent {
  final Perfil? perfil;
  final int cpfLogado;
  final FaturaLista fatura;

  @override
  List<Object?> get props => [perfil, fatura];

  Generate({
    required this.perfil,
    required this.cpfLogado,
    required this.fatura,
  });
}

class GoToInitStateFaturaEvent extends CodigoBarrasFaturaEvent {
  @override
  List<Object> get props => [];
}
