import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/negociacao-debito/negociacoes_debitos_event.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/negociacao-debito/negociacoes_debitos_state.dart';
import 'package:cliente_minha_unimed/models/remote-config/erro-getnet-remote-config.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/api/getnet.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';

class NegociacoesDebitosBloc
    extends Bloc<NegociacoesDebitosEvent, NegociacoesDebitosState> {
  NegociacoesDebitosBloc() : super(InitialNegociacoesDebitosState());

  List<ErrorGetnetRemoteConfig> _listErrorsGetnet = List.empty(growable: true);
  List<ErrorGetnetRemoteConfig> get listErrorsGetNet => _listErrorsGetnet;

  @override
  Stream<NegociacoesDebitosState> mapEventToState(
    NegociacoesDebitosEvent event,
  ) async* {
    if (event is GetAllNegociacoesEvent) {
      yield LoadingNegociacoesDebitosState();

      try {
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        _listErrorsGetnet = await Locator.instance
            .get<GetNetApi>()
            .getErrorsGetNet(token: token);

        final _lista =
            await Locator.instance.get<FinanceiroApi>().getAllNegociacoes(
                  perfil: event.perfil!,
                  cpfLogado: event.cpfLogado,
                );
        if (_lista.length == 0) {
          yield NoDataNegociacoesDebitosState(
              message: MessageException.FINANCE_NEGOTIATION_NODATA_TITULOS);
        } else {
          yield DoneNegociacoesDebitosState(negociacoes: _lista);
        }
      } catch (err) {
        yield ErrorNegociacoesDebitosState('$err');
      }
    }
  }
}
