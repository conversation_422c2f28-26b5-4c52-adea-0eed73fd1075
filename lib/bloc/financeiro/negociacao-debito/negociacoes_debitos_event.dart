import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class NegociacoesDebitosEvent extends Equatable {}

class GetAllNegociacoesEvent extends NegociacoesDebitosEvent {
  final Perfil? perfil;
  final int cpfLogado;

  @override
  List<Object?> get props => [perfil, cpfLogado];

  GetAllNegociacoesEvent({
    required this.perfil,
    required this.cpfLogado,
  });
}
