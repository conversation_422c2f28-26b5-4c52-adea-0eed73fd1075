import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/negociacao-lista.model.dart';

@immutable
abstract class NegociacoesDebitosState extends Equatable {}

class InitialNegociacoesDebitosState extends NegociacoesDebitosState {
  @override
  List<Object> get props => [];
}

class LoadingNegociacoesDebitosState extends NegociacoesDebitosState {
  @override
  List<Object> get props => [];
}

class ErrorNegociacoesDebitosState extends NegociacoesDebitosState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorNegociacoesDebitosState(this.message);
}

class DoneNegociacoesDebitosState extends NegociacoesDebitosState {
  final List<NegociacaoLista>? negociacoes;

  @override
  List<Object?> get props => [negociacoes];

  DoneNegociacoesDebitosState({this.negociacoes});
}

class NoDataNegociacoesDebitosState extends NegociacoesDebitosState {
  final String message;

  @override
  List<Object?> get props => [message];

  NoDataNegociacoesDebitosState({required this.message});
}
