import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/constants.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class PdfFactoryEvent extends Equatable {}

class GetPdf extends PdfFactoryEvent {
  final Perfil? perfil;
  final PdfFactory? pdf;
  final num ano;
  final String? atendimentoId;
  final String? cpfLogin;

  @override
  List<Object?> get props => [perfil, pdf, ano, atendimentoId, cpfLogin];

  GetPdf({
    this.perfil,
    this.pdf,
    this.ano = 0,
    this.atendimentoId,
    this.cpfLogin,
  });
}

class Reset extends PdfFactoryEvent {
  @override
  List<Object> get props => [];
}
