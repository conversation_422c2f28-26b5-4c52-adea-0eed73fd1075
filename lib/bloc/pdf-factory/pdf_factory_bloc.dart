import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/constants.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/pdf_factory_event.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/pdf_factory_state.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/opcionais.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class PdfFactoryBloc extends Bloc<PdfFactoryEvent, PdfFactoryState> {
  PdfFactoryBloc() : super(InitialPdfFactoryState());
  final logger = UnimedLogger(className: 'PdfFactoryBloc');

  @override
  Stream<PdfFactoryState> mapEventToState(PdfFactoryEvent event) async* {
    if (event is GetPdf) {
      yield LoadingPdfFactoryState(pdf: event.pdf);

      logger.d('pdf is ${event.pdf}');

      try {
        BeneficiarioApi api = Locator.instance.get<BeneficiarioApi>();

        String? _stringResult;
        ProtocolNumberVO? protocolNumber;
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        switch (event.pdf) {
          case PdfFactory.TEMPO_CONTRATO:
            protocolNumber = await Locator.instance
                .get<AutorizacoesApi>()
                .getProtocolSolicitationNumber(
                    perfil: event.perfil!,
                    comunicaoInterna:
                        'Geração da declaração de tempo de contrato',
                    atendimentoId: event.atendimentoId,
                    cpfLogin: event.cpfLogin,
                    token: token);
            _stringResult = await api.getBase64TempoContrato(
                perfil: event.perfil!,
                protocolNumber: protocolNumber.protocolo);
            break;
          case PdfFactory.PIN_SS:
            protocolNumber = await Locator.instance
                .get<AutorizacoesApi>()
                .getProtocolSolicitationNumber(
                    perfil: event.perfil!,
                    comunicaoInterna:
                        'Geração da declaração de informações cadastrais PIN SS',
                    atendimentoId: event.atendimentoId,
                    cpfLogin: event.cpfLogin,
                    token: token);

            _stringResult = await api.getBase64PinSS(
                perfil: event.perfil!,
                protocolNumber: protocolNumber.protocolo);
            break;
          case PdfFactory.IR:
            protocolNumber = await Locator.instance
                .get<AutorizacoesApi>()
                .getProtocolSolicitationNumber(
                    perfil: event.perfil!,
                    comunicaoInterna:
                        'Geração demonstrativo de imposto de renda',
                    atendimentoId: event.atendimentoId,
                    cpfLogin: event.cpfLogin,
                    token: token);

            _stringResult = await api.getImpostoRenda(
                perfil: event.perfil!,
                anoFatura: event.ano as int,
                protocolNumber: protocolNumber.protocolo);
            break;
          case PdfFactory.TERMO_OPCIONAL:
            _stringResult =
                await Locator.instance.get<OpcionaisApi>().getTermoOpcional();
            break;
          default:
            _stringResult = null;
        }

        if (event.pdf != PdfFactory.TERMO_OPCIONAL) {
          if (_stringResult == null) {
            logger.e('${event.pdf} => base64 is null');
            yield ErrorPdfFactoryState(
                pdf: event.pdf, message: 'Base64 is null');
          } else {
            logger.d(
                '${event.pdf} => base64 generated length ${_stringResult.length}');

            final _pathFile = await FileUtils.createFileFromString(
                base64String: _stringResult, extension: FileExtension.PDF);

            logger.d('${event.pdf} => pdf file path $_pathFile');

            yield DonePdfFactoryState(
                pathPdf: _pathFile,
                pdf: event.pdf,
                protocolNumber: protocolNumber);
          }
        } else {
          logger.d('${event.pdf} => pdf file path $_stringResult');
          yield DonePdfFactoryState(pathPdf: _stringResult!, pdf: event.pdf);
        }
      } on BeneficiarioWarningException catch (err) {
        yield WarningPdfFactoryState(pdf: event.pdf, message: err.message);
      } on BeneficiarioException catch (err) {
        yield ErrorPdfFactoryState(pdf: event.pdf, message: err.message);
      } catch (err) {
        yield ErrorPdfFactoryState(
            pdf: event.pdf, message: MessageException.GENERAL);
      }
    } else {
      logger.d('go to initial state');
      yield InitialPdfFactoryState();
    }
  }
}
