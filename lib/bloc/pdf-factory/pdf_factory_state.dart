import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/constants.dart';

@immutable
abstract class PdfFactoryState extends Equatable {}

class InitialPdfFactoryState extends PdfFactoryState {
  @override
  List<Object> get props => [];
}

class LoadingPdfFactoryState extends PdfFactoryState {
  final PdfFactory? pdf;

  @override
  List<Object?> get props => [pdf];

  LoadingPdfFactoryState({required this.pdf});
}

class WarningPdfFactoryState extends PdfFactoryState {
  final String message;
  final PdfFactory? pdf;

  @override
  List<Object?> get props => [message, pdf];

  WarningPdfFactoryState({required this.message, required this.pdf});
}

class ErrorPdfFactoryState extends PdfFactoryState {
  final String message;
  final PdfFactory? pdf;

  @override
  List<Object?> get props => [message, pdf];

  ErrorPdfFactoryState({required this.message, required this.pdf});
}

class DonePdfFactoryState extends PdfFactoryState {
  final String pathPdf;
  final PdfFactory? pdf;
  final ProtocolNumberVO? protocolNumber;

  @override
  List<Object?> get props => [pathPdf, pdf];

  DonePdfFactoryState(
      {required this.pathPdf, required this.pdf, this.protocolNumber});
}
