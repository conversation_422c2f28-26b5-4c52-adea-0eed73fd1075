import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class EvaluationState extends Equatable {}

class InitialState extends EvaluationState {
  @override
  List<Object> get props => [];
}

class LoadingState extends EvaluationState {
  @override
  List<Object> get props => [];
}

class NeededShowEvaluationState extends EvaluationState {
  @override
  List<Object> get props => [];

  NeededShowEvaluationState();
}

class NoNeedShowEvaluationState extends EvaluationState {
  @override
  List<Object> get props => [];

  NoNeedShowEvaluationState();
}
