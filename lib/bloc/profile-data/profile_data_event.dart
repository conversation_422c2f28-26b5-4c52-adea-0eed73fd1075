part of 'profile_data_bloc.dart';

abstract class ProfileDataEvent extends Equatable {
  const ProfileDataEvent();

  @override
  List<Object> get props => [];
}

class GetContactData extends ProfileDataEvent {
  GetContactData({required this.profile, this.atendimentoId, this.cpfLogin});
  final Perfil? profile;
  final String? atendimentoId;
  final String? cpfLogin;
}

class GetProfileContactData extends ProfileDataEvent {
  GetProfileContactData({required this.profile});
  final Perfil? profile;
}

class UpdateContactData extends ProfileDataEvent {
  UpdateContactData(
      {this.phoneNumber,
      this.email,
      this.addressCode,
      this.profile,
      this.atendimentoId,
      this.cpfLogin});
  final String? phoneNumber;
  final String? email;
  final String? addressCode;
  final Perfil? profile;
  final String? atendimentoId;
  final String? cpfLogin;
}
