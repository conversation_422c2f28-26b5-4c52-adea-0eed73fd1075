import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/profile-data/confirmation-token-model.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'change_profile_data_event.dart';
part 'change_profile_data_state.dart';

class ChangeProfileDataBloc
    extends Bloc<ChangeProfileDataEvent, ChangeProfileDataState> {
  ChangeProfileDataBloc({required this.beneficiaryApi})
      : super(ChangeProfileDataInitial());
  final BeneficiarioApi beneficiaryApi;
  ConfirmationTokenModel? _confirmationTokenModel;

  ConfirmationTokenModel? get confirmationTokenModel => _confirmationTokenModel;

  final UnimedLogger logger = UnimedLogger(className: 'ChangeProfileDataBloc');

  @override
  Stream<ChangeProfileDataState> mapEventToState(
    ChangeProfileDataEvent event,
  ) async* {
    if (event is ChangeContactData) {
      yield ChangeProfileDataLoading();
      try {
        _confirmationTokenModel =
            await beneficiaryApi.sendConfirmationProfileDataToken(
                card: event.card, data: event.data, type: event.type);

        yield ChangeProfileDataWaitingCode(
            confirmationTokenModel: confirmationTokenModel!);
      } catch (e) {
        logger.e('Error on ChangeContactData $e');
        yield ChangeProfileDataError("${e.toString()}");
      }
    } else if (event is VerifyCodeContactData) {
      yield ChangeProfileDataLoading();
      try {
        final message = await beneficiaryApi.verifyProfileDataToken(
            idToken: event.idToken, token: event.token);

        yield ChangeProfileDataDone(message: message);
      } catch (e) {
        logger.e('Error on ChangeContactData $e');
        yield ChangeProfileDataError(e.toString());
      }
    } else if (event is DeleteCacheToken) {
      await beneficiaryApi.deleteProfileDataTokenSharedPreferences(event.card);

      yield ChangeProfileDataInitial();
    } else if (event is UpdateConfirmationTokenModel) {
      _confirmationTokenModel = event.confirmationTokenModel;
    }
  }
}
