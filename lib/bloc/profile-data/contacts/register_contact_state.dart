part of 'register_contact_bloc.dart';

abstract class <PERSON><PERSON>ontactState extends Equatable {
  @override
  List<Object?> get props => [];
}

class InitialRegisterContactState extends RegisterContactState {}

class LoadingRegisterContactState extends RegisterContactState {}

class EmptyTypesContactState extends RegisterContactState {}

class DoneGetContactTypesState extends RegisterContactState {
  DoneGetContactTypesState({
    required this.typesContact,
  });
  final List<TypeContact> typesContact;

  @override
  List<Object?> get props => [];
}

class DoneSaveContactState extends RegisterContactState {}

class DoneUpdateContactState extends Register<PERSON>ontactState {}

class ErrorRegisterContactState extends Register<PERSON><PERSON>actState {
  ErrorRegisterContactState({required this.message});
  final String message;

  @override
  List<Object?> get props => [];
}
