import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/profile-data/contact_uses_response.model.dart';
import 'package:cliente_minha_unimed/models/profile-data/contacts_response.model.dart';
import 'package:cliente_minha_unimed/models/profile-data/grouped_contacts.model.dart';
import 'package:cliente_minha_unimed/models/profile-data/types_contact_response.model.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'contacts_event.dart';
part 'contacts_state.dart';

class ContactsBloc extends Bloc<ContactsEvent, ContactsState> {
  ContactsBloc({required this.beneficiaryApi}) : super(InitialContactsState());
  final BeneficiarioApi beneficiaryApi;

  final UnimedLogger logger = UnimedLogger(className: 'ContactsBloc');

  @override
  Stream<ContactsState> mapEventToState(
    ContactsEvent event,
  ) async* {
    if (event is GetAllContactsEvent) {
      yield LoadingContactsState();
      try {
        //Pegar contatos
        final contactsModel = await beneficiaryApi.getAllContacts(
          codBenef: event.codBeneficiario,
        );
        //pegar tipos de contato
        final typesContact = await beneficiaryApi.getTypesContact();
        //pegar utilizacoes
        final uses = await beneficiaryApi.getContactUses();

        if ((contactsModel.retorno ?? []).isEmpty) {
          yield EmptyContactsState(
            typesContact: typesContact.retorno ?? [],
            contactUses: uses.retorno ?? [],
          );
        } else {
          if ((typesContact.retorno ?? []).isEmpty) {
            yield (EmptyTypeContactState());
          } else {
            yield DoneGetContactsState(
              groupedContacts:
                  _groupContacts(contactsResponseModel: contactsModel),
              typesContact: typesContact.retorno ?? [],
              contactUses: uses.retorno ?? [],
            );
          }
        }
      } catch (e) {
        logger.e('Error on GetAllContacts $e');
        yield ErrorContactsState(message: "${e.toString()}");
      }
    } else if (event is DeleteContactEvent) {
      yield LoadingContactsState();
      try {
        await beneficiaryApi.deleteContact(codContact: event.codContact);
        yield DoneDeleteContactState();
      } catch (e) {
        logger.e('Error on GetAllContacts $e');
        yield ErrorDeleteContactState(message: "${e.toString()}");
      }
    } else if (event is UpdatePreferentialContactEvent) {
      yield LoadingContactsState();
      try {
        await beneficiaryApi.updateContact(
          codContact: event.codContact,
          contact: event.contact,
          codUnimed: event.codUnimed,
          codBenef: event.codBenef,
          isPreferentialContact: event.isPreferentialContact,
          uses: event.uses,
        );
        yield DoneUpdatePreferentialContactState();
      } catch (e) {
        logger.e('Error on UpdateContactEvent $e');
        yield ErrorContactsState(message: "${e.toString()}");
      }
    }
  }

  GroupedContactsModel _groupContacts(
      {required ContactsResponseModel contactsResponseModel}) {
    //Agrupando a lista pelo tipo de objeto
    Map<String, dynamic> finalJson = {
      "contacts_with_type": [],
    };

    for (final contact in contactsResponseModel.retorno!) {
      // Procura um item correspondente pelo tipo de contato
      var existingGroup = finalJson["contacts_with_type"].firstWhere(
        (group) => group["type_contact_value"] == contact.tipoContato,
        orElse: () => null,
      );

      // Se não encontrar um grupo existente, cria um novo
      if (existingGroup == null) {
        existingGroup = {
          "type_contact_value": contact.tipoContato,
          "contacts": [],
        };
        finalJson["contacts_with_type"].add(existingGroup);
      }

      // Adiciona o contato ao grupo existente
      existingGroup["contacts"].add(contact.toJson());
    }

    return GroupedContactsModel.fromJson(finalJson);
  }
}
