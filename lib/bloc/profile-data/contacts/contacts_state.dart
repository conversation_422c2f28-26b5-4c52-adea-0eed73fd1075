part of 'contacts_bloc.dart';

abstract class ContactsState extends Equatable {
  @override
  List<Object?> get props => [];
}

class InitialContactsState extends ContactsState {}

class LoadingContactsState extends ContactsState {}

class EmptyContactsState extends ContactsState {
  final List<TypeContact> typesContact;
  final List<ContactUse> contactUses;
  EmptyContactsState({
    required this.typesContact,
    required this.contactUses,
  });
  @override
  List<Object?> get props => [];
}

class EmptyTypeContactState extends ContactsState {}

class DoneDeleteContactState extends ContactsState {}

class DoneUpdatePreferentialContactState extends ContactsState {}

class DoneGetContactsState extends ContactsState {
  DoneGetContactsState({
    required this.groupedContacts,
    required this.typesContact,
    required this.contactUses,
  });
  final GroupedContactsModel groupedContacts;
  final List<TypeContact> typesContact;
  final List<ContactUse> contactUses;

  @override
  List<Object?> get props => [];
}

class ErrorContactsState extends ContactsState {
  ErrorContactsState({required this.message});
  final String message;

  @override
  List<Object?> get props => [];
}

class ErrorDeleteContactState extends ContactsState {
  ErrorDeleteContactState({required this.message});
  final String message;

  @override
  List<Object?> get props => [];
}
