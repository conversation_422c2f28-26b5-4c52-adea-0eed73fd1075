part of 'register_contact_bloc.dart';

abstract class RegisterContactEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class GetContactTypesEvent extends RegisterContactEvent {
  @override
  List<Object> get props => [];
}

class SaveContactEvent extends RegisterContactEvent {
  SaveContactEvent({
    required this.contact,
    required this.uses,
    required this.typeContact,
    required this.codUnimed,
    required this.codBenef,
    required this.isPreferentialContact,
  });
  final String contact;
  final List<int> uses;
  final int typeContact;
  final int codUnimed;
  final int codBenef;
  final bool isPreferentialContact;
  @override
  List<Object> get props => [];
}

class UpdateContactEvent extends RegisterContactEvent {
  UpdateContactEvent({
    required this.codContact,
    required this.uses,
    required this.contact,
    required this.codUnimed,
    required this.codBenef,
    required this.isPreferentialContact,
  });
  final int codContact;
  final List<int> uses;
  final String contact;
  final int codUnimed;
  final int codBenef;
  final bool isPreferentialContact;
  @override
  List<Object> get props => [];
}

class SetToInitialStateEvent extends RegisterContactEvent {
  @override
  List<Object> get props => [];
}
