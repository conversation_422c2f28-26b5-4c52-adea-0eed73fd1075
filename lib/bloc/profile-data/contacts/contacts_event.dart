part of 'contacts_bloc.dart';

abstract class ContactsEvent extends Equatable {
  const ContactsEvent();

  @override
  List<Object> get props => [];
}

class GetAllContactsEvent extends ContactsEvent {
  GetAllContactsEvent({
    required this.codBeneficiario,
  });

  final int codBeneficiario;
}

class DeleteContactEvent extends ContactsEvent {
  DeleteContactEvent({
    required this.codContact,
  });

  final int codContact;
}

class UpdatePreferentialContactEvent extends ContactsEvent {
  UpdatePreferentialContactEvent({
    required this.codContact,
    required this.contact,
    required this.codUnimed,
    required this.codBenef,
    required this.isPreferentialContact,
    required this.uses,
  });
  final int codContact;
  final String contact;
  final int codUnimed;
  final int codBenef;
  final bool isPreferentialContact;
  final List<int> uses;
  @override
  List<Object> get props => [];
}
