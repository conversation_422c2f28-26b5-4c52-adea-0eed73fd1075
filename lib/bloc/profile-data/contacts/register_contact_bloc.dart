import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/profile-data/types_contact_response.model.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'register_contact_event.dart';
part 'register_contact_state.dart';

class RegisterContactBloc
    extends Bloc<RegisterContactEvent, RegisterContactState> {
  RegisterContactBloc({required this.beneficiaryApi})
      : super(InitialRegisterContactState());
  final BeneficiarioApi beneficiaryApi;

  final UnimedLogger logger = UnimedLogger(className: 'RegisterContactBloc');

  @override
  Stream<RegisterContactState> mapEventToState(
    RegisterContactEvent event,
  ) async* {
    if (event is GetContactTypesEvent) {
      yield LoadingRegisterContactState();
      try {
        final typesContactModel = await beneficiaryApi.getTypesContact();
        if ((typesContactModel.retorno ?? []).isEmpty) {
          yield EmptyTypesContactState();
        } else {
          yield DoneGetContactTypesState(
            typesContact: typesContactModel.retorno ?? [],
          );
        }
      } catch (e) {
        logger.e('Error on GetContactTypesEvent $e');
        yield ErrorRegisterContactState(message: "${e.toString()}");
      }
    } else if (event is SaveContactEvent) {
      yield LoadingRegisterContactState();
      try {
        final codContact = await beneficiaryApi.saveContact(
          contact: event.contact,
          typeContact: event.typeContact,
          codUnimed: event.codUnimed,
          codBenef: event.codBenef,
          uses: event.uses,
        );

        if (event.isPreferentialContact && codContact != null) {
          await beneficiaryApi.updateContact(
            codContact: codContact,
            contact: event.contact,
            codUnimed: event.codUnimed,
            codBenef: event.codBenef,
            isPreferentialContact: event.isPreferentialContact,
            uses: event.uses,
          );
        }

        yield DoneSaveContactState();
      } catch (e) {
        logger.e('Error on SaveContactEvent $e');
        yield ErrorRegisterContactState(message: "${e.toString()}");
      }
    } else if (event is UpdateContactEvent) {
      yield LoadingRegisterContactState();
      try {
        await beneficiaryApi.updateContact(
          codContact: event.codContact,
          contact: event.contact,
          codUnimed: event.codUnimed,
          codBenef: event.codBenef,
          isPreferentialContact: event.isPreferentialContact,
          uses: event.uses,
        );
        yield DoneUpdateContactState();
      } catch (e) {
        logger.e('Error on UpdateContactEvent $e');
        yield ErrorRegisterContactState(message: "${e.toString()}");
      }
    } else if (event is SetToInitialStateEvent) {
      yield InitialRegisterContactState();
    }
  }
}
