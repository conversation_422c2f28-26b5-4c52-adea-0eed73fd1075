import 'dart:async';
import 'dart:convert' show utf8;

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/beneficiario-data.dart';
import 'package:cliente_minha_unimed/models/profile-data/confirmation-token-model.dart';
import 'package:cliente_minha_unimed/models/profile-data/contacts_response.model.dart';
import 'package:cliente_minha_unimed/models/profile-data/profile-data.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:cliente_minha_unimed/shared/constants.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'profile_data_event.dart';
part 'profile_data_state.dart';

class ProfileDataBloc extends Bloc<ProfileDataEvent, ProfileDataState> {
  ProfileDataBloc({required this.beneficiaryApi}) : super(ProfileDataInitial());
  final BeneficiarioApi beneficiaryApi;
  final UnimedLogger logger = UnimedLogger(className: 'ProfileDataBloc');

  ProfileContactDataModel? _profileContactData;

  ContactsResponseModel? _contactsModel;
  ContactsResponseModel? get contactsModel => _contactsModel;

  BeneficiarioData? _profileData;
  String? _cpf;

  @override
  Stream<ProfileDataState> mapEventToState(
    ProfileDataEvent event,
  ) async* {
    if (event is GetContactData) {
      yield* _handleGetContactData(event);
    } else if (event is GetProfileContactData) {
      yield ProfileDataLoading();
      try {
        // _profileContactData = await beneficiaryApi
        //     .getProfileData(event.profile!.contratoBeneficiario.carteira!);

        _contactsModel = await Locator.instance
            .get<BeneficiarioApi>()
            .getAllContacts(
                codBenef:
                    event.profile!.contratoBeneficiario.beneficiario!.codBenef);

        yield ProfileContactDataDone(
            email:
                _contactsModel!.getPreferencial(CONTACT_TYPE_EMAIL)?.contato ??
                    "",
            phone:
                _contactsModel!.getPreferencial(CONTACT_TYPE_CELL)?.contato ??
                    "");
      } catch (e) {
        logger.e('Error on getProfileContactData $e');

        yield ProfileContactDataDone(email: "", phone: "");
      }
    } else if (event is UpdateContactData) {
      yield* _handleUpdateContactData(event);
    }
  }

  Stream<ProfileDataState> _handleGetContactData(GetContactData event) async* {
    try {
      yield ProfileDataLoading();
      _cpf = event.profile!.contratoBeneficiario.beneficiario!.cpf11Digits;
      _profileData = await beneficiaryApi.getData(perfil: event.profile!);
      _profileContactData = await beneficiaryApi
          .getProfileData(event.profile!.contratoBeneficiario.carteira!);

      final _contactsModel = await Locator.instance
          .get<BeneficiarioApi>()
          .getAllContacts(
              codBenef:
                  event.profile!.contratoBeneficiario.beneficiario!.codBenef);

      final email =
          _contactsModel.getPreferencial(CONTACT_TYPE_EMAIL)?.contato ?? "";
      final phone =
          _contactsModel.getPreferencial(CONTACT_TYPE_CELL)?.contato ??
              _contactsModel.getPreferencial(CONTACT_TYPE_PHONE)?.contato ??
              "";

      final confirmationTokenModel =
          await beneficiaryApi.getConfirmationProfileDataTokenId(
        event.profile!.contratoBeneficiario.carteira!.carteiraNumero,
      );

      yield ProfileDataDone(
        profileData: _profileData,
        profileContactData: _profileContactData,
        doc: _cpf,
        confirmationTokenModel: confirmationTokenModel,
        email: email,
        phone: phone,
      );
    } catch (e) {
      final encoded = utf8.encode(e.toString());
      yield ProfileDataError(utf8.decode(encoded));
    }
  }

  Stream<ProfileDataState> _handleUpdateContactData(
      UpdateContactData event) async* {
    try {
      yield ProfileDataLoading();
      final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
      final protocolNumber = await Locator.instance
          .get<AutorizacoesApi>()
          .getProtocolSolicitationNumber(
              perfil: event.profile!,
              comunicaoInterna:
                  'Alteração de Meus Dados - email: ${event.email ?? _profileContactData?.email}, telefone: ${event.phoneNumber ?? _profileContactData?.phoneNumber}',
              atendimentoId: event.atendimentoId,
              cpfLogin: event.cpfLogin,
              token: token);

      final _successMessage = await beneficiaryApi.updateContactProfileData(
          addressCode:
              event.addressCode ?? '${_profileContactData?.addressCode ?? ''}',
          phoneNumber:
              event.phoneNumber ?? _profileContactData?.phoneNumber ?? '',
          email: event.email ?? _profileContactData?.email ?? '',
          protocolNumber: protocolNumber.protocolo);
      yield ProfileDataReload(
          message: _successMessage, protocolNumber: protocolNumber);
    } on UnimedException catch (ex) {
      yield ProfileDataReload(message: ex.message);
    } catch (e) {
      yield ProfileDataReload(
          message: 'Não foi possível atualizar os dados do perfil');
    }
  }
}
