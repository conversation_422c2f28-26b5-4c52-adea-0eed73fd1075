part of 'change_profile_data_bloc.dart';

abstract class ChangeProfileDataEvent extends Equatable {
  const ChangeProfileDataEvent();

  @override
  List<Object> get props => [];
}

class VerifyCodeContactData extends ChangeProfileDataEvent {
  VerifyCodeContactData({required this.idToken, required this.token});
  final String idToken;
  final String token;
}

class ChangeContactData extends ChangeProfileDataEvent {
  ChangeContactData(
      {required this.card, required this.data, required this.type});
  final String card;
  final String data;
  final String type;
}

class DeleteCacheToken extends ChangeProfileDataEvent {
  DeleteCacheToken({required this.card});
  final String card;
}

class UpdateConfirmationTokenModel extends ChangeProfileDataEvent {
  UpdateConfirmationTokenModel({required this.confirmationTokenModel});
  final ConfirmationTokenModel confirmationTokenModel;
}
