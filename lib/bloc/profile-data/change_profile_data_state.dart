part of 'change_profile_data_bloc.dart';

abstract class ChangeProfileDataState extends Equatable {
  const ChangeProfileDataState();

  @override
  List<Object?> get props => [];
}

class ChangeProfileDataInitial extends ChangeProfileDataState {}

class ChangeProfileDataLoading extends ChangeProfileDataState {}

class ChangeProfileDataSending extends ChangeProfileDataState {}

class ChangeProfileDataWaitingCode extends ChangeProfileDataState {
  final ConfirmationTokenModel confirmationTokenModel;
  ChangeProfileDataWaitingCode({required this.confirmationTokenModel});
}

class ChangeProfileDataReload extends ChangeProfileDataState {
  ChangeProfileDataReload({this.message, this.protocolNumber});
  final String? message;
  final ProtocolNumberVO? protocolNumber;
}

class ChangeProfileDataError extends ChangeProfileDataState {
  ChangeProfileDataError(this.message);
  final String? message;

  @override
  List<Object?> get props => [message];
}

class ChangeProfileDataDone extends ChangeProfileDataState {
  ChangeProfileDataDone({required this.message});
  final String? message;

  @override
  List<Object?> get props => [message];
}
