part of 'profile_data_bloc.dart';

abstract class ProfileDataState extends Equatable {
  const ProfileDataState();

  @override
  List<Object?> get props => [];
}

class ProfileDataInitial extends ProfileDataState {}

class ProfileDataLoading extends ProfileDataState {}

class ProfileDataReload extends ProfileDataState {
  ProfileDataReload({this.message, this.protocolNumber});
  final String? message;
  final ProtocolNumberVO? protocolNumber;
}

class ProfileDataDone extends ProfileDataState {
  ProfileDataDone(
      {required this.profileData,
      required this.profileContactData,
      required this.doc,
      required this.confirmationTokenModel,
      required this.email,
      required this.phone});
  final BeneficiarioData? profileData;
  final ProfileContactDataModel? profileContactData;
  final String? doc;
  final ConfirmationTokenModel confirmationTokenModel;
  final String email;
  final String phone;

  @override
  List<Object?> get props => [doc];
}

class ProfileContactDataDone extends ProfileDataState {
  final String email;
  final String phone;

  ProfileContactDataDone({
    required this.email,
    required this.phone,
  });

  @override
  List<Object?> get props => [];
}

class ProfileDataError extends ProfileDataState {
  ProfileDataError(this.message);
  final String? message;

  @override
  List<Object?> get props => [message];
}
