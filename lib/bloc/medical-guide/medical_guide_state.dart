import 'package:cliente_minha_unimed/models/medical-guide/provider-type.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/search-specialty.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/specialty-city.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class MedicalGuideState extends Equatable {}

class InitialSearchState extends MedicalGuideState {
  @override
  List<Object> get props => [];
}

class LoadingSearchState extends MedicalGuideState {
  @override
  List<Object> get props => [];
}

class ErrorSearchState extends MedicalGuideState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSearchState(this.message);
}

class LoadedSearchState extends MedicalGuideState {
  final List<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestadores];

  LoadedSearchState(this.prestadores);
}

class FilterState extends MedicalGuideState {
  final String textFilter;

  @override
  List<Object> get props => [textFilter];

  FilterState(this.textFilter);
}

class SelectedPrestadorState extends MedicalGuideState {
  final PrestadorSelecionado? prestador;
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestador, prestadores];

  SelectedPrestadorState(this.prestador, this.prestadores);
}

class LoadedProvidersTypeState extends MedicalGuideState {
  final List<ProviderTypeModel>? providers;

  @override
  List<Object?> get props => [providers];

  LoadedProvidersTypeState({this.providers});
}

class LoadedSpecialtiesState extends MedicalGuideState {
  final List<SearchSpecialtyModel>? specialties;

  @override
  List<Object?> get props => [specialties];

  LoadedSpecialtiesState({this.specialties});
}

class LoadedPlacesState extends MedicalGuideState {
  final List<SpecialtyCityModel>? cities;

  @override
  List<Object?> get props => [cities];

  LoadedPlacesState({this.cities});
}

class ScrollToElementState extends MedicalGuideState {
  final int? index;

  @override
  List<Object?> get props => [index];

  ScrollToElementState({this.index});
}
