import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:flutter/foundation.dart';

@immutable
abstract class TabEvent extends Equatable {}

class AtualizaPrestadoresEvent extends TabEvent {
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestadores];

  AtualizaPrestadoresEvent({required this.prestadores});
}

class SelecionarPrestadoresEvent extends TabEvent {
  final PrestadorSelecionado? prestador;
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestador, prestadores];

  SelecionarPrestadoresEvent({
    required this.prestador,
    required this.prestadores,
  });
}
