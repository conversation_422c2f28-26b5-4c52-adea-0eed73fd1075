import 'dart:collection';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/pin_map_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/pin_map_state.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/maps.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class PinMapGuiaMedicoBloc
    extends Bloc<PinMapGuiaMedicoEvent, PinMapGuiaMedicoState> {
  PinMapGuiaMedicoBloc() : super(InitialPinMapState());
  final logger = UnimedLogger(className: 'PinMapGuiaMedicoBloc');

  Map<String, BitmapDescriptor?> _pinMaps = HashMap();

  Map<String, BitmapDescriptor?> get pinsMap => _pinMaps;

  @override
  Stream<PinMapGuiaMedicoState> mapEventToState(
    PinMapGuiaMedicoEvent event,
  ) async* {
    if (event is LoadPins) {
      yield LoadingPinMapState();

      if (!_pinMaps.containsKey('clinicas')) {
        _pinMaps['clinicas'] =
            await MapsUtil.getBitmapDescriptorPrestador('clinicas', 100);
      }

      if (!_pinMaps.containsKey('clinicasUnimed')) {
        _pinMaps['clinicasUnimed'] =
            await MapsUtil.getBitmapDescriptorPrestador('clinicasUnimed', 100);
      }

      if (!_pinMaps.containsKey('clinicaUnimed')) {
        _pinMaps['clinicaUnimed'] =
            await MapsUtil.getBitmapDescriptorPrestador('clinicaUnimed', 100);
      }

      if (!_pinMaps.containsKey('clinicaAis')) {
        _pinMaps['clinicaAis'] =
            await MapsUtil.getBitmapDescriptorPrestador('clinicaUnimed', 100);
      }

      _pinMaps['hospitais'] =
          await MapsUtil.getBitmapDescriptorPrestador('hospitais', 100);

      _pinMaps['medicos'] =
          await MapsUtil.getBitmapDescriptorPrestador('medicos', 100);

      _pinMaps['laboratorios'] =
          await MapsUtil.getBitmapDescriptorPrestador('laboratorios', 100);

      _pinMaps['banco_sangue'] =
          await MapsUtil.getBitmapDescriptorPrestador('banco_sangue', 100);

      logger.d('Loaded Pins Guia Médico');

      yield LoadedPinMapState(_pinMaps);
    }
  }
}
