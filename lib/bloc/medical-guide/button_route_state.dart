import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ButtonRouteState extends Equatable {}

class InitialState extends ButtonRouteState {
  @override
  List<Object> get props => [];
}

class LoadingState extends ButtonRouteState {
  @override
  List<Object> get props => [];
}

class ErrorState extends ButtonRouteState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends ButtonRouteState {
  final double distance;
  @override
  List<Object> get props => [distance];
  DoneState(this.distance);
}
