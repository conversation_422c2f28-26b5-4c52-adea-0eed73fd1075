import 'package:cliente_minha_unimed/models/medical-guide/legend-guide-medical.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class LegendMedicalState extends Equatable {}

class InitialLegendMedicalState extends LegendMedicalState {
  @override
  List<Object> get props => [];
}

class LoadingLegendMedicalState extends LegendMedicalState {
  @override
  List<Object> get props => [];
}

class ErrorLegendMedicalState extends LegendMedicalState {
  final String message;

  @override
  List<Object> get props => [message];
  ErrorLegendMedicalState(this.message);
}

class DoneLegendMedicalState extends LegendMedicalState {
  final List<LegendMedicalGuideModel> legendMedical;

  @override
  List<Object> get props => [];
   DoneLegendMedicalState({required this.legendMedical});
}
