import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:meta/meta.dart';

@immutable
abstract class PinMapGuiaMedicoState extends Equatable {}

class InitialPinMapState extends PinMapGuiaMedicoState {
  @override
  List<Object> get props => [];
}

class LoadingPinMapState extends PinMapGuiaMedicoState {
  @override
  List<Object> get props => [];
}

class ErrorPinMapState extends PinMapGuiaMedicoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorPinMapState(this.message);
}

class LoadedPinMapState extends PinMapGuiaMedicoState {
  final Map<String, BitmapDescriptor?> pinsMap;

  @override
  List<Object> get props => [pinsMap];

  LoadedPinMapState(this.pinsMap);
}
