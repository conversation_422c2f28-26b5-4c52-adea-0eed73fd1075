import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/button_route_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/button_route_state.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/geolocation.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:google_map_polyline/google_map_polyline.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location_permissions/location_permissions.dart';

class ButtonRouteBloc extends Bloc<ButtonRouteEvent, ButtonRouteState> {
  ButtonRouteBloc() : super(InitialState());
  final logger = UnimedLogger(className: 'ButtonRouteBloc');

  List<LatLng>? _routeCoords;
  double? _distance;

  double? get distance => _distance;
  List<LatLng>? get routeCoords => _routeCoords;

  @override
  Stream<ButtonRouteState> mapEventToState(ButtonRouteEvent event) async* {
    if (event is SearchButtonRouteEvent) {
      yield LoadingState();
      try {
        PermissionStatus permission =
            await LocationPermissions().checkPermissionStatus();

        if (permission == PermissionStatus.denied) {
          await LocationPermissions().requestPermissions();
        } else {
          var currentLocation = await Locator.instance
              .get<GeolocationService>()
              .getCurrentPosition();

          final GoogleMapPolyline googleMapPolyline = new GoogleMapPolyline(
            // apiKey: "8Qjl76SLYB_bPyQNJktRSwWWhk4hp9N-iMB9Pg7rA3Y",
            apiKey: "AteZr6ftFaxXQNLmrOyYhlezL0CFbW5t0pyqdg_kLJo",
          );

          _routeCoords = await googleMapPolyline.getCoordinatesWithLocationHere(
              origin:
                  LatLng(currentLocation!.latitude, currentLocation.longitude),
              destination: event.latLng,
              mode: RouteMode.driving);

          _distance = googleMapPolyline.distance;
        }
        yield DoneState(distance ?? 0.0);
      } on UnimedException catch (error) {
        logger.e('ButtonRouteBloc UnimedException $error');
        yield ErrorState(error.message);
      } catch (e) {
        logger.e('ButtonRouteBloc Exception $e');
        _distance = 0.0;
        final _message = 'Não foi possível acessar a localização';
        yield ErrorState(_message);
      }
    } else if (event is InicialButtonRouteEvent) {
      _distance = 0.0;
      yield InitialState();
    } else if (event is DoneButtonRouteEvent) {
      _distance = 0.0;
      yield DoneState(0.0);
    }
  }
}
