import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ButtonRouteEvent extends Equatable {}

class SearchButtonRouteEvent extends ButtonRouteEvent {
  final LatLng latLng;
  @override
  List<Object> get props => [latLng];
  SearchButtonRouteEvent(this.latLng);
}

class DoneButtonRouteEvent extends ButtonRouteEvent {
  @override
  List<Object> get props => [];
}

class InicialButtonRouteEvent extends ButtonRouteEvent {
  @override
  List<Object> get props => [];
}
