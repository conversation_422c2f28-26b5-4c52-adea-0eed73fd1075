import 'package:cliente_minha_unimed/models/medical-guide/medical-guide-search.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/search-specialty.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class MedicalGuideEvent extends Equatable {}

class Search extends MedicalGuideEvent {
  final Perfil? perfil;
  final SearchMedicalGuideModel? searchParams;
  final bool isAdvancedSearch;

  @override
  List<Object?> get props => [perfil, searchParams, isAdvancedSearch];

  Search(
      {required this.perfil,
      required this.searchParams,
      required this.isAdvancedSearch});
}

class ClearSearch extends MedicalGuideEvent {
  @override
  List<Object> get props => [];
}

class ClearCacheResults extends MedicalGuideEvent {
  @override
  List<Object> get props => [];
}

class SetPrestador extends MedicalGuideEvent {
  final PrestadorSelecionado prestador;

  @override
  List<Object> get props => [prestador];

  SetPrestador(this.prestador);
}

class ClearPrestador extends MedicalGuideEvent {
  @override
  List<Object> get props => [];
}

class FilterEvent extends MedicalGuideEvent {
  final String textFilter;

  @override
  List<Object> get props => [textFilter];

  FilterEvent(this.textFilter);
}

class SetToInitialState extends MedicalGuideEvent {
  @override
  List<Object> get props => [];
}

class OpenFavorites extends MedicalGuideEvent {
  final Perfil? perfil;
  final List<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestadores];

  OpenFavorites({this.perfil, this.prestadores});
}

class GetProvidersTypes extends MedicalGuideEvent {
  final Perfil? profile;

  @override
  List<Object?> get props => [profile];

  GetProvidersTypes({this.profile});
}

class GetSpecialties extends MedicalGuideEvent {
  final Perfil? profile;

  @override
  List<Object?> get props => [profile];

  GetSpecialties({this.profile});
}

class GetPlaces extends MedicalGuideEvent {
  final Perfil? profile;
  final SearchSpecialtyModel specialty;
  final String? providerTypeName;

  @override
  List<Object?> get props => [profile, specialty, providerTypeName];

  GetPlaces({
    required this.profile,
    required this.specialty,
    required this.providerTypeName,
  });
}

class UpdateProvidersEvent extends MedicalGuideEvent {
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestadores];

  UpdateProvidersEvent({required this.prestadores});
}

class ScrollToElementEvent extends MedicalGuideEvent {
  final int index;

  @override
  List<Object> get props => [index];

  ScrollToElementEvent({required this.index});
}
