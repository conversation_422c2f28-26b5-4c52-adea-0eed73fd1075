import 'package:cliente_minha_unimed/bloc/medical-guide/legend_medical_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/legend_medical_state.dart';
import 'package:cliente_minha_unimed/shared/api/guia-medico-legend/guia-medico-legend.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LegendMedicalBloc extends Bloc<LegendMedicalEvent, LegendMedicalState> {
  LegendMedicalBloc() : super(InitialLegendMedicalState());

  @override
  Stream<LegendMedicalState> mapEventToState(LegendMedicalEvent event) async* {
    if (event is GetLegendMedicalEvent) {
      yield LoadingLegendMedicalState();

      try {
        final _legendMedicalApi = await Locator.instance.get<LegendMedicalApi>();
        final legendMedicalData = await _legendMedicalApi.listLegendMedical();

        yield DoneLegendMedicalState(legendMedical: legendMedicalData);
      } catch (e) {
        yield ErrorLegendMedicalState(e.toString());
      }
    }
  }
}
