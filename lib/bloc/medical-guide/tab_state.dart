import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:flutter/foundation.dart';

@immutable
abstract class TabState extends Equatable {}

class InitialTabState extends TabState {
  @override
  List<Object> get props => [];
}

class LoadedTabState extends TabState {
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestadores];

  LoadedTabState({required this.prestadores});
}

class LoadingTabState extends TabState {
  @override
  List<Object> get props => [];
}

class SelectedPrestadorTabState extends TabState {
  final PrestadorSelecionado? prestador;
  final Iterable<ProviderModel>? prestadores;

  @override
  List<Object?> get props => [prestador, prestadores];

  SelectedPrestadorTabState(
      {required this.prestador, required this.prestadores});
}
