import 'package:cliente_minha_unimed/bloc/medical-guide/tab_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/tab_state.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TabBloc extends Bloc<TabEvent, TabState> {
  TabBloc() : super(InitialTabState());
  final logger = UnimedLogger(className: 'TabBloc');

  Iterable<ProviderModel>? _prestadores;

  Iterable<ProviderModel>? get prestadores => _prestadores;

  PrestadorSelecionado? _prestador;
  PrestadorSelecionado? get prestador => _prestador;

  @override
  Stream<TabState> mapEventToState(
    TabEvent event,
  ) async* {
    if (event is AtualizaPrestadoresEvent) {
      yield LoadingTabState();
      yield LoadedTabState(prestadores: event.prestadores);
    } else if (event is SelecionarPrestadoresEvent) {
      _prestador = event.prestador;
      yield LoadingTabState();
      yield SelectedPrestadorTabState(
          prestador: event.prestador, prestadores: event.prestadores);
    }
  }
}
