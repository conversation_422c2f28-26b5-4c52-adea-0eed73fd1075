import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_state.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/guia-medico.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:flutter/material.dart';

class MedicalGuideBloc extends Bloc<MedicalGuideEvent, MedicalGuideState> {
  MedicalGuideBloc() : super(InitialSearchState());
  final logger = UnimedLogger(className: 'GuiaMedicoBloc');

  String? _textSearch = '';
  String? get textSearch => _textSearch;
  setTextSearch({text}) {
    _textSearch = text;
  }

  List<ProviderModel>? _providers;
  List<ProviderModel>? _filteredProviders;
  PrestadorSelecionado? _provider;

  List<ProviderModel>? get providers => _filteredProviders;
  PrestadorSelecionado? get provider => _provider;

  @override
  Stream<MedicalGuideState> mapEventToState(
    MedicalGuideEvent event,
  ) async* {
    if (event is Search) {
      yield LoadingSearchState();

      logger.d(
        'INIT event Search perfil ${event.perfil!.contratoBeneficiario.beneficiario!.nome}',
      );

      try {
        _providers = await Locator.instance.get<GuiaMedicoApi>().listProviders(
              perfil: event.perfil,
              searchParams: event.searchParams,
              isAdvancedSearch: event.isAdvancedSearch,
            );

        _filteredProviders = _providers;

        logger.d('END event Search');

        yield LoadedSearchState(providers);
      } catch (e) {
        logger.e('BLOC $e');
        _providers = List<ProviderModel>.empty(growable: true);
        _filteredProviders = List<ProviderModel>.empty(growable: true);
        yield ErrorSearchState(
            'Não foi possível obter resultados do guia médico no momento, tente novamente mais tarde');
      }
    } else if (event is ClearSearch) {
      yield InitialSearchState();
    } else if (event is ClearCacheResults) {
      _providers = List<ProviderModel>.empty(growable: true);
      _filteredProviders = List<ProviderModel>.empty(growable: true);

      yield InitialSearchState();
    } else if (event is SetPrestador) {
      _provider = event.prestador;
      yield SelectedPrestadorState(_provider, providers);
    } else if (event is ClearPrestador) {
      _provider = null;
      yield LoadedSearchState(providers);
    } else if (event is FilterEvent) {
      _filteredProviders = List<ProviderModel>.empty(growable: true);
      if (event.textFilter.isEmpty) {
        _filteredProviders = _providers;
      } else {
        _providers!.forEach((item) {
          if (StringUtils.compareNames(item.providerName, event.textFilter)) {
            _filteredProviders!.add(item);
          }
        });
      }
      yield LoadedSearchState(_filteredProviders!.toList());
    } else if (event is SetToInitialState) {
      debugPrint('Voltando ao estado Inicial');
      yield InitialSearchState();
    } else if (event is OpenFavorites) {
      yield* _handleOpenFavorites(event);
    } else if (event is UpdateProvidersEvent) {
      yield LoadingSearchState();
      yield LoadedSearchState(event.prestadores as List<ProviderModel>?);
    } else if (event is GetProvidersTypes) {
      try {
        yield LoadingSearchState();
        final _providers = await Locator.instance
            .get<AgendamentoApi>()
            .getProvidersType(profile: event.profile!);
        yield LoadedProvidersTypeState(providers: _providers);
      } catch (err) {
        yield ErrorSearchState(err.toString());
      }
    } else if (event is GetSpecialties) {
      try {
        yield LoadingSearchState();
        final _specialties = await Locator.instance
            .get<AgendamentoApi>()
            .getSpecialties(profile: event.profile!);
        yield LoadedSpecialtiesState(specialties: _specialties);
      } catch (err) {
        yield ErrorSearchState(err.toString());
      }
    } else if (event is GetPlaces) {
      try {
        yield LoadingSearchState();
        final _places =
            await Locator.instance.get<AgendamentoApi>().listPlacesBySpecialty(
                  profile: event.profile!,
                  specialty: event.specialty,
                  providerTypeName: event.providerTypeName,
                );
        yield LoadedPlacesState(cities: _places);
      } catch (err) {
        yield ErrorSearchState(err.toString());
      }
    } else if (event is ScrollToElementEvent) {
      yield LoadingSearchState();
      yield ScrollToElementState(index: event.index);
    }
  }

  Stream<MedicalGuideState> _handleOpenFavorites(OpenFavorites event) async* {
    try {
      yield LoadingSearchState();

      List<String?> favoritesCodes = List<String?>.empty(growable: true);
      event.prestadores!.forEach((element) {
        favoritesCodes.add(element.providerCode);
      });

      _providers =
          await Locator.instance.get<GuiaMedicoApi>().listProvidersByCodes(
                perfil: event.perfil!,
                providersCodes: favoritesCodes,
              );

      _filteredProviders = _providers;

      yield LoadedSearchState(providers);
    } on UnimedException catch (ex) {
      yield ErrorSearchState(ex.message);
    } catch (e) {
      yield ErrorSearchState('Não foi possível listar os favoritos.');
    }
  }
}
