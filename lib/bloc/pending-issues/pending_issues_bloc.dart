import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_event.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_state.dart';
import 'package:cliente_minha_unimed/shared/api/pending-issues/pending_issues.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class PendingIssuesBloc extends Bloc<PendingIssuesEvent, PendingIssuesState> {
  PendingIssuesBloc() : super(InitialPendingIssuesState());
  final logger = UnimedLogger(className: 'PendingIssuesBloc');

  @override
  Stream<PendingIssuesState> mapEventToState(
    PendingIssuesEvent event,
  ) async* {
    if (event is GetUpcomingInvoicesEvent) {
      try {
        yield LoadingPendingIssuesState();

        final upcomingInvoices = await Locator.instance.get<PendingIssuesApi>().getUpcomingInvoices(
              perfil: event.perfil,
              cpfLoggedIn: event.cpfLoggedIn,
              dueInDays: event.dueInDays,
            );

        yield DoneGetUpcomingInvoicesState(upcomingInvoices: upcomingInvoices);
      } catch (e) {
        logger.e('Error on GetUpcomingInvoicesEvent $e');
        yield ErrorPendingIssuesState(message: e.toString());
      }
    } else if(event is SetToInitialStateEvent) {
      yield InitialPendingIssuesState();
    }
  }
}
