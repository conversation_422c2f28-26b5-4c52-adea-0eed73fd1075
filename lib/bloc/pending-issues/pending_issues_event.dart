import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class PendingIssuesEvent extends Equatable {}

class GetUpcomingInvoicesEvent extends PendingIssuesEvent {
  final Perfil perfil;
  final int cpfLoggedIn;
  final int dueInDays;

  @override
  List<Object> get props => [];

  GetUpcomingInvoicesEvent({
    required this.perfil,
    required this.cpfLoggedIn,
    required this.dueInDays,
  });
}

class SetToInitialStateEvent extends PendingIssuesEvent {
  @override
  List<Object> get props => [];
}
