import 'package:cliente_minha_unimed/models/pending-issues/upcoming_invoices.model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class PendingIssuesState extends Equatable {}

class InitialPendingIssuesState extends PendingIssuesState {
  @override
  List<Object> get props => [];
}

class LoadingPendingIssuesState extends PendingIssuesState {
  @override
  List<Object> get props => [];
}

class DoneGetUpcomingInvoicesState extends PendingIssuesState {
  final UpcomingInvoicesModel upcomingInvoices;

  @override
  List<Object> get props => [];

  DoneGetUpcomingInvoicesState({required this.upcomingInvoices});
}

class ErrorPendingIssuesState extends PendingIssuesState {
  final String message;

  @override
  List<Object?> get props => [];

  ErrorPendingIssuesState({required this.message});
}
