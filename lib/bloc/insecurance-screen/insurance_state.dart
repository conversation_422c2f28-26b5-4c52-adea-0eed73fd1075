import 'package:equatable/equatable.dart';

abstract class InsuranceState extends Equatable {
  const InsuranceState();

  @override
  List<Object?> get props => [];
}

class InsuranceInitial extends InsuranceState {}

class InsuranceLoading extends InsuranceState {}

class InsuranceLoaded extends InsuranceState {
  final String url;

  const InsuranceLoaded(this.url);

  @override
  List<Object?> get props => [url];
}

class InsuranceError extends InsuranceState {
  final String message;

  const InsuranceError(this.message);

  @override
  List<Object?> get props => [message];
}
