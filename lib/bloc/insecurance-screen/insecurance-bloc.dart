import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/insecurance-screen/insurance_event.dart';
import 'package:cliente_minha_unimed/bloc/insecurance-screen/insurance_state.dart';
import 'package:cliente_minha_unimed/shared/api/insecurance-screen/insecuranse-screen-api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class InsuranceBloc extends Bloc<InsuranceEvent, InsuranceState> {
  InsuranceBloc() : super(InsuranceInitial());

  @override
  Stream<InsuranceState> mapEventToState(
    InsuranceEvent event,
  ) async* {
    if (event is LoadInsuranceUrlEvent) {
      yield InsuranceLoading();

      try {
        final plataformaSegurosURL =
            await Locator.instance.get<InsecuranceApi>().getInsecuranceUrl();

        if (plataformaSegurosURL == null) {
          yield InsuranceError(MessageException.GENERIC_TIMEOUT);
          return;
        }
        yield InsuranceLoaded(plataformaSegurosURL);
      } catch (e) {
        yield InsuranceError('Erro ao carregar a URL: $e');
      }
    }
  }
}
