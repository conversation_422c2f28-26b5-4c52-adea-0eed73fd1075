part of 'privacy_policy_bloc.dart';

abstract class PrivacyPolicyEvent extends Equatable {
  const PrivacyPolicyEvent();

  @override
  List<Object?> get props => [];
}

class ShowAlertPrivacyEvent extends PrivacyPolicyEvent {
  final User? user;

  @override
  List<Object?> get props => [user];

  ShowAlertPrivacyEvent({this.user});
}

class VerifyAlertEvent extends PrivacyPolicyEvent {
  final String? cpfLogado;

  @override
  List<Object?> get props => [cpfLogado];

  VerifyAlertEvent({this.cpfLogado});
}

class SendAnswerPrivacyEvent extends PrivacyPolicyEvent {
  final String cpfLogado;
  final Perfil perfil;

  @override
  List<Object?> get props => [cpfLogado, perfil];

  SendAnswerPrivacyEvent({required this.cpfLogado, required this.perfil});
}