part of 'privacy_policy_bloc.dart';

abstract class PrivacyPolicyState extends Equatable {
  const PrivacyPolicyState();

  @override
  List<Object> get props => [];
}

class PrivacyPolicyInitial extends PrivacyPolicyState {}

class ShowAlertState extends PrivacyPolicyState {
  @override
  List<Object> get props => [];
}

class ErrorShowAlertState extends PrivacyPolicyState {
  @override
  List<Object> get props => [];
}

class ConfirmShowAlertState extends PrivacyPolicyState {
  @override
  List<Object> get props => [];
}
class DontShowAlertState extends PrivacyPolicyState {
  @override
  List<Object> get props => [];
}
