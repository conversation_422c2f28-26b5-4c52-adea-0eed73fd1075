import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/privacy-policy.service.dart';
import 'package:equatable/equatable.dart';

part 'privacy_policy_event.dart';
part 'privacy_policy_state.dart';

class PrivacyPolicyBloc extends Bloc<PrivacyPolicyEvent, PrivacyPolicyState> {
  PrivacyPolicyBloc() : super(PrivacyPolicyInitial());

  @override
  Stream<PrivacyPolicyState> mapEventToState(
    PrivacyPolicyEvent event,
  ) async* {
    if (event is ShowAlertPrivacyEvent) {
      final _user = event.user;

      if (_user!.config.privacyPolicy!.showAlert!) {
        yield ShowAlertState();
      } else {
        yield ErrorShowAlertState();
      }
    } else if (event is VerifyAlertEvent) {
      try {
        final _ppVO = await Locator.instance
            .get<PrivacyPolicyService>()
            .verifyPrivacyPolicy(cpfLogado: event.cpfLogado);
        if (!_ppVO.accepted!) {
          yield ConfirmShowAlertState();
        }
      } catch (ex) {
        yield DontShowAlertState();
      }
    } else if (event is SendAnswerPrivacyEvent) {
      try {
        await Locator.instance.get<PrivacyPolicyService>().savePrivacyPolicy(
            cpfLogado: event.cpfLogado, perfil: event.perfil);

        yield PrivacyPolicyInitial();
      } catch (ex) {
        yield ErrorShowAlertState();
      }
    }
  }
}
