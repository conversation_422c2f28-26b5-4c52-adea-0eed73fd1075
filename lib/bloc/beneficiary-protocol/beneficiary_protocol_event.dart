part of 'beneficiary_protocol_bloc.dart';

abstract class BeneficiaryProtocolEvent extends Equatable {
  const BeneficiaryProtocolEvent();

  @override
  List<Object> get props => [];
}

class GetBeneficiaryProtocol extends BeneficiaryProtocolEvent {
  final Perfil profile;
  final DateTimeRange? dateTimeRange;

  GetBeneficiaryProtocol({required this.profile, this.dateTimeRange});
}

class FilterBeneficiaryProtocol extends BeneficiaryProtocolEvent {
  final List<String> tags;
  final List<String> status;

  @override
  List<Object> get props => [tags, status];

  FilterBeneficiaryProtocol({required this.tags, required this.status});
}
