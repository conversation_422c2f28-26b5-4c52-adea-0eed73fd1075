import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/protocols.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'beneficiary_protocol_event.dart';
part 'beneficiary_protocol_state.dart';

class BeneficiaryProtocolBloc
    extends Bloc<BeneficiaryProtocolEvent, BeneficiaryProtocolState> {
  BeneficiaryProtocolBloc() : super(BeneficiaryProtocolInitial());

  List<String> _status = List.empty(growable: true);
  List<String> _tags = List.empty(growable: true);

  List<BeneficiaryProtocolModel> _protocols = List.empty(growable: true);
  List<BeneficiaryProtocolModel> _filteredProtocols =
      List.empty(growable: true);

  @override
  Stream<BeneficiaryProtocolState> mapEventToState(
    BeneficiaryProtocolEvent event,
  ) async* {
    if (event is GetBeneficiaryProtocol) {
      yield LoadingBeneficiaryProtocolState();

      try {
        _protocols = await Locator.instance
            .get<BeneficiaryProtocolApi>()
            .getBeneficiaryProtocols(
              profile: event.profile,
              dateTimeRange: event.dateTimeRange,
            );
        if (_protocols.length > 0) {
          _handleStatusAndTags(_protocols);
          yield LoadedBeneficiaryProtocolState(
            protocols: _protocols,
            listStatus: _status,
            listTags: _tags,
          );
        } else {
          yield NoDataBeneficiaryProtocolState();
        }
      } catch (ex) {
        yield ErrorBeneficiaryProtocolState(message: ex.toString());
      }
    } else if (event is FilterBeneficiaryProtocol) {
      yield LoadingBeneficiaryProtocolState();

      _filteredProtocols = _protocols;

      _filteredProtocols = _protocols.where((protocol) {
        final matchesStatus =
            event.status.isEmpty || event.status.contains(protocol.status);
        final matchesTags =
            event.tags.isEmpty || event.tags.contains(protocol.protocolType);

        return matchesStatus && matchesTags;
      }).toList();
    }

    yield LoadedBeneficiaryProtocolState(
      protocols: _filteredProtocols,
      listStatus: _status,
      listTags: _tags,
    );
  }

  _handleStatusAndTags(List<BeneficiaryProtocolModel> protocols) {
    _status = List.empty(growable: true);
    _tags = List.empty(growable: true);

    for (final protocol in protocols) {
      if (!_status.contains(protocol.status)) {
        _status.add(protocol.status);
      }

      if (!_tags.contains(protocol.protocolType)) {
        _tags.add(protocol.protocolType);
      }
    }

    _status.sort((a, b) => a.compareTo(b));
    _tags.sort((a, b) => a.compareTo(b));
  }
}
