part of 'beneficiary_protocol_bloc.dart';

abstract class BeneficiaryProtocolState extends Equatable {
  const BeneficiaryProtocolState();

  @override
  List<Object> get props => [];
}

class BeneficiaryProtocolInitial extends BeneficiaryProtocolState {}

class ErrorBeneficiaryProtocolState extends BeneficiaryProtocolState {
  final String message;

  ErrorBeneficiaryProtocolState({required this.message});
}

class LoadedBeneficiaryProtocolState extends BeneficiaryProtocolState {
  final List<BeneficiaryProtocolModel> protocols;
  final List<String> listStatus;
  final List<String> listTags;

  LoadedBeneficiaryProtocolState(
      {required this.protocols,
      required this.listStatus,
      required this.listTags});
}

class LoadingBeneficiaryProtocolState extends BeneficiaryProtocolState {}

class NoDataBeneficiaryProtocolState extends BeneficiaryProtocolState {}
