import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/redefinir-senha/redefinir_senha_event.dart';
import 'package:cliente_minha_unimed/bloc/redefinir-senha/redefinir_senha_state.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/redefinir_senha.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';

class RedefinirSenhaBloc
    extends Bloc<RedefinirSenhaEvent, RedefinirSenhaState> {
  RedefinirSenhaBloc() : super(InitialState());

  @override
  Stream<RedefinirSenhaState> mapEventToState(
      RedefinirSenhaEvent event) async* {
    if (event is GetPasswordRulesEvent) {
      yield LoadingTokenState();
      try {
        final api = Locator.instance.get<RedefinirSenhaApi>();

        final result = await api.getPasswordRules();

        yield DoneGetPasswordRulesState(rules: result ?? []);
      } on UnimedException catch (e) {
        yield ErrorRulesState(e.message);
      } on Exception catch (e) {
        if (e is SocketException || e is HandshakeException)
          yield ErrorRulesState(
              "Não foi possível conectar, tente novamente mais tarde.");
      }
    } else if (event is ChangeEnvioTokenEvent) {
      yield LoadingTokenState();
      try {
        final login = event.login.replaceAll(RegExp(r'\D'), '');
        _validateFormToken(login);
        final api = Locator.instance.get<RedefinirSenhaApi>();

        var message =
            await api.sendToken(UserCredentials(cpf: login, password: null));

        message =
            message!.replaceAll(new RegExp(r'\<strong\>|\<\/strong\>'), '[b]');

        yield DoneTokenState(message);
      } on UnimedException catch (e) {
        yield ErrorTokenState(e.message);
      } on Exception catch (e) {
        if (e is SocketException || e is HandshakeException)
          yield ErrorTokenState(
              "Não foi possível conectar, tente novamente mais tarde.");
      }
    } else if (event is ChangeEnvioSenhaEvent) {
      yield LoadingSenhaState();

      try {
        _validateFormSenha(event.envioNovaSenha);
        final api = Locator.instance.get<RedefinirSenhaApi>();

        String? message = await api.sendRedefinirSenha(event.envioNovaSenha);

        yield DoneSenhaState(message, event.envioNovaSenha);
      } on UnimedException catch (e) {
        yield ErrorSenhaState(e.message);
      } on SocketException catch (_) {
        yield ErrorSenhaState(
            "Não foi possível conectar, tente novamente mais tarde.");
      } on HandshakeException catch (_) {
        yield ErrorSenhaState(
            "Não foi possível conectar, tente novamente mais tarde.");
      } on Exception catch (e) {
        yield ErrorSenhaState("$e");
      }
    } else if (event is InicioEnvioTokenEvent) {
      yield InitialState();
    } else if (event is InicioEnvioSenhaEvent) {
      yield InitialSenhaState();
    } else if (event is InicioConfirmaSenhaEvent) {
      yield DoneGetPasswordRulesState(rules: event.rules!);
    } else if (event is NewPasswordSavedInternalEvent) {
      yield NewPasswordSavedInternalState(
          newPasswordInternal: event.newPasswordInternal);
    }
  }

  void _validateFormToken(String login) {
    if (login.isEmpty)
      throw SendTokenException('O campo cpf não pode ser vazio!');
    if (!StringUtils.validateCpf(login))
      throw SendTokenException('Digite um cpf válido');
  }

  void _validateFormSenha(EnvioNovaSenha dao) {
    if (dao.hash.isEmpty)
      throw SendTokenException(
          'O campo Código de redefinição não pode ser vazio!');
    if (dao.senha.isEmpty)
      throw SendTokenException('O campo senha não pode ser vazio!');
    if (dao.senha.compareTo(dao.confirmaSenha) < 0)
      throw SendTokenException('Campo Senha deve ser igual ao Confirmar senha');
  }
}
