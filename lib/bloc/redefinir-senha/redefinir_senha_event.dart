import 'package:cliente_minha_unimed/models/password_rules.model.dart';
import 'package:cliente_minha_unimed/shared/api/redefinir_senha.api.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class RedefinirSenhaEvent extends Equatable {}

class ChangeEnvioTokenEvent extends RedefinirSenhaEvent {
  final String login;

  @override
  List<Object> get props => [login];

  ChangeEnvioTokenEvent(this.login);
}

class ChangeEnvioSenhaEvent extends RedefinirSenhaEvent {
  final EnvioNovaSenha envioNovaSenha;

  @override
  List<Object> get props => [envioNovaSenha];

  ChangeEnvioSenhaEvent(this.envioNovaSenha);
}

class InicioEnvioTokenEvent extends RedefinirSenhaEvent {
  @override
  List<Object> get props => [];
}

class InicioEnvioSenhaEvent extends RedefinirSenhaEvent {
  @override
  List<Object> get props => [];
}

class InicioConfirmaSenhaEvent extends RedefinirSenhaEvent {
  final List<PasswordRulesModel>? rules;

  @override
  List<Object> get props => [];

  InicioConfirmaSenhaEvent({required this.rules});
}

class GetPasswordRulesEvent extends RedefinirSenhaEvent {
  @override
  List<Object> get props => [];

  GetPasswordRulesEvent();
}

class NewPasswordSavedInternalEvent extends RedefinirSenhaEvent {
  final String newPasswordInternal;

  NewPasswordSavedInternalEvent({required this.newPasswordInternal});

  @override
  List<Object> get props => [];
}
