import 'package:cliente_minha_unimed/models/password_rules.model.dart';
import 'package:cliente_minha_unimed/shared/api/redefinir_senha.api.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class RedefinirSenhaState extends Equatable {}

class InitialState extends RedefinirSenhaState {
  @override
  List<Object> get props => [];
}

class InitialSenhaState extends RedefinirSenhaState {
  @override
  List<Object> get props => [];
}

class LoadingTokenState extends RedefinirSenhaState {
  @override
  List<Object> get props => [];
}

class LoadingSenhaState extends RedefinirSenhaState {
  @override
  List<Object> get props => [];
}

class ErrorTokenState extends RedefinirSenhaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorTokenState(this.message);
}

class ErrorSenhaState extends RedefinirSenhaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSenhaState(this.message);
}

class ErrorRulesState extends RedefinirSenhaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorRulesState(this.message);
}

class DoneTokenState extends RedefinirSenhaState {
  final String retorno;

  @override
  List<Object> get props => [retorno];

  DoneTokenState(this.retorno);
}

class DoneSenhaState extends RedefinirSenhaState {
  final String? retorno;
  final EnvioNovaSenha? envioNovaSenha;

  @override
  List<Object?> get props => [retorno];

  DoneSenhaState(this.retorno, this.envioNovaSenha);
}

class NewPasswordSavedInternalState extends RedefinirSenhaState {
  final String newPasswordInternal;

 @override
  List<Object?> get props => [newPasswordInternal];
  NewPasswordSavedInternalState({EnvioNovaSenha? envioNovaSenha, required this.newPasswordInternal});
}


class DoneGetPasswordRulesState extends RedefinirSenhaState {
  final List<PasswordRulesModel> rules;

  @override
  List<Object?> get props => [];

  DoneGetPasswordRulesState({required this.rules});
}
