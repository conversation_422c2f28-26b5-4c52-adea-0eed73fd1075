import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/opcionais.vo.dart';

@immutable
abstract class OpcionalEvent extends Equatable {}

class ListarOpcionaisEvent extends OpcionalEvent {
  final Perfil? perfil;

  @override
  List<Object?> get props => [perfil];

  ListarOpcionaisEvent({required this.perfil});
}

class IncluirOpcionalEvent extends OpcionalEvent {
  final Perfil? perfil;
  final OpcionalVO opcional;
  final String? atendimentoId;
  final String? cpfLogin;

  @override
  List<Object?> get props => [perfil, opcional, atendimentoId, cpfLogin];

  IncluirOpcionalEvent({required this.perfil, required this.opcional, this.atendimentoId, this.cpfLogin});
}

class CarregarFullBannerEvent extends OpcionalEvent {
  final Perfil? perfil;
  final double screenWidth;
  final double screenHeight;

  @override
  List<Object?> get props => [perfil, screenWidth, screenHeight];

  CarregarFullBannerEvent({required this.perfil, required this.screenWidth, required this.screenHeight});
}

class FecharFullBannerEvent extends OpcionalEvent {
  final Perfil? perfil;
  final OpcionalVO? opcional;

  @override
  List<Object?> get props => [perfil, opcional];

  FecharFullBannerEvent({required this.perfil, required this.opcional});
}

class CarregarListBannersEvent extends OpcionalEvent {
  final Iterable<OpcionalVO>? opcionais;
  final double screenWidth;
  final double screenHeight;
  final Perfil? perfil;

  @override
  List<Object?> get props => [opcionais, screenWidth, screenHeight];

  CarregarListBannersEvent({
    required this.opcionais,
    required this.screenWidth,
    required this.screenHeight,
    required this.perfil,
  });
}

class CarregarSaibaMaisBannerEvent extends OpcionalEvent {
  final OpcionalVO opcional;
  final double screenWidth;
  final double screenHeight;

  @override
  List<Object> get props => [opcional, screenWidth, screenHeight];

  CarregarSaibaMaisBannerEvent({required this.opcional, required this.screenWidth, required this.screenHeight});
}

class MostrarSaibaMaisBannerEvent extends OpcionalEvent {
  final OpcionalVO? opcional;

  @override
  List<Object?> get props => [opcional];

  MostrarSaibaMaisBannerEvent({required this.opcional});
}

class FecharSaibaMaisBannerEvent extends OpcionalEvent {
  @override
  List<Object> get props => [];
}

class ChecarOpcionaisEvent extends OpcionalEvent {
  final Perfil? perfil;

  @override
  List<Object?> get props => [perfil];

  ChecarOpcionaisEvent({required this.perfil});
}
