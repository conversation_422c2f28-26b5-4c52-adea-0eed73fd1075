import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/opcionais.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/opcionais.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

import 'opcional_event.dart';
import 'opcional_state.dart';

class OpcionalBloc extends Bloc<OpcionalEvent, OpcionalState> {
  OpcionalBloc() : super(OpcionalInitialState());
  Iterable<OpcionalVO>? _opcionais;
  Perfil? _perfil;

  Iterable<OpcionalVO>? get opcionais => _opcionais;

  @override
  Stream<OpcionalState> mapEventToState(OpcionalEvent event) async* {
    if (event is ListarOpcionaisEvent) {
      _perfil = event.perfil;
      yield ListarOpcionaisLoadingState();

      try {
        _opcionais = await Locator.instance.get<OpcionaisApi>().getOpcionais(event.perfil!);

        yield ListarOpcionaisSuccessState(_opcionais);
      } on OpcionaisException catch (e) {
        yield ListarOpcionaisErrorState(e.message);
      } on Exception catch (e) {
        yield ListarOpcionaisErrorState("$e");
      }
    } else if (event is IncluirOpcionalEvent) {
      yield IncluirOpcionalLoadingState(event.opcional);

      try {
        final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
        final protocolNumber = await Locator.instance.get<AutorizacoesApi>().getProtocolSolicitationNumber(
            perfil: event.perfil!,
            comunicaoInterna: 'Contratação de opcionais - ${event.opcional.descricao}',
            atendimentoId: event.atendimentoId,
            cpfLogin: event.cpfLogin,
            token: token);

        await Locator.instance
            .get<OpcionaisApi>()
            .incluirOpcional(perfil: event.perfil!, opcional: event.opcional, protocolNumber: protocolNumber.protocolo);

        await Locator.instance
            .get<OpcionaisApi>()
            .saveSoldOptional(event.opcional, event.perfil!.contratoBeneficiario.beneficiario!.codBenef);

        event.opcional.possuiItem = 'SIM';
        yield IncluirOpcionalSuccessState(event.opcional, protocolNumber);
      } on OpcionaisException catch (e) {
        yield IncluirOpcionalErrorState(e.message);
      } on Exception catch (e) {
        yield IncluirOpcionalErrorState("$e");
      }
    } else if (event is CarregarFullBannerEvent) {
      yield CarregarFullBannerLoadingState();

      try {
        if (event.perfil != null) {
          final Iterable<OpcionalVO> _opcionais =
              await Locator.instance.get<OpcionaisApi>().getOpcionais(event.perfil!);

          for (OpcionalVO opcional in _opcionais) {
            if (!opcional.jaFoiAdquirido) {
              final bool _wasSold = await Locator.instance
                  .get<OpcionaisApi>()
                  .wasSold(opcional, event.perfil!.contratoBeneficiario.beneficiario!.codBenef);

              if (!_wasSold) {
                final bool _permit =
                    await Locator.instance.get<OpcionaisApi>().permitOpenFullBanner(event.perfil!, opcional);

                if (_permit) {
                  final BannerDataVO _bannerData = await Locator.instance
                      .get<OpcionaisApi>()
                      .getFullBanner(opcional, event.screenWidth, event.screenHeight, event.perfil!);

                  opcional.fullBannerData = _bannerData;
                  yield CarregarFullBannerSuccessState(opcional);

                  return;
                }
              }
            }
          }
        }

        yield CarregarFullBannerNoDataState();
      } catch (e) {
        yield CarregarFullBannerNoDataState();
      }
    } else if (event is FecharFullBannerEvent) {
      yield FecharFullBannerState();

      await Locator.instance.get<OpcionaisApi>().saveLastOpenedTimeFullBanner(event.perfil!, event.opcional!);
    } else if (event is CarregarListBannersEvent) {
      yield CarregarListBannersLoadingState();

      for (OpcionalVO opcional in event.opcionais!) {
        try {
          final BannerDataVO _bannerData = await Locator.instance.get<OpcionaisApi>().getListBanner(
                opcional,
                event.screenWidth,
                event.screenHeight,
                _perfil!,
              );

          opcional.listBannerData = _bannerData;
          yield CarregarListBannersSuccessState(opcional);
        } catch (e) {
          yield CarregarListBannersNoDataState(opcional, "Não foi possível carregar a imagem do banner no momento");
        }
      }
    } else if (event is CarregarSaibaMaisBannerEvent) {
      yield CarregarSaibaMaisBannerLoadingState(opcional: event.opcional);

      try {
        final BannerDataVO _bannerData = await Locator.instance.get<OpcionaisApi>().getSaibaMaisBanner(
              event.opcional,
              event.screenWidth,
              event.screenHeight,
              _perfil!,
            );

        event.opcional.saibaMaisBannerData = _bannerData;
        yield CarregarSaibaMaisBannerSuccessState(event.opcional);
      } catch (e) {
        yield CarregarSaibaMaisBannerNoDataState(event.opcional);
      }
    } else if (event is MostrarSaibaMaisBannerEvent) {
      yield MostrarSaibaMaisBannerState(event.opcional);
    } else if (event is FecharSaibaMaisBannerEvent) {
      yield FecharSaibaMaisBannerState();
    } else if (event is ChecarOpcionaisEvent) {
      if (event.perfil!.pessoaFisica!) {
        yield ShowOpcionaisState();
      } else {
        yield HideOpcionaisState();
      }
    }
  }
}
