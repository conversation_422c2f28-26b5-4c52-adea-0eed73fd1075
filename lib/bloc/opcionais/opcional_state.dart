import 'package:cliente_minha_unimed/shared/api/vo/protocol-number.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/api/vo/opcionais.vo.dart';

@immutable
abstract class OpcionalState extends Equatable {}

class OpcionalInitialState extends OpcionalState {
  @override
  List<Object> get props => [];
}

// Estados do Evento ListarOpcionaisEvent

class ListarOpcionaisLoadingState extends OpcionalState {
  @override
  List<Object> get props => [];
}

class ListarOpcionaisErrorState extends OpcionalState {
  final String message;

  @override
  List<Object> get props => [message];

  ListarOpcionaisErrorState(this.message);
}

class ListarOpcionaisSuccessState extends OpcionalState {
  final Iterable<OpcionalVO>? opcionais;

  @override
  List<Object?> get props => [opcionais];

  ListarOpcionaisSuccessState(this.opcionais);
}

// Estados do Evento IncluirOpcionalEvent

class IncluirOpcionalLoadingState extends OpcionalState {
  final OpcionalVO opcional;

  @override
  List<Object> get props => [opcional];

  IncluirOpcionalLoadingState(this.opcional);
}

class IncluirOpcionalErrorState extends OpcionalState {
  final String message;

  @override
  List<Object> get props => [message];

  IncluirOpcionalErrorState(this.message);
}

class IncluirOpcionalSuccessState extends OpcionalState {
  final OpcionalVO opcional;
  final ProtocolNumberVO protocolNumber;

  @override
  List<Object> get props => [opcional, protocolNumber];

  IncluirOpcionalSuccessState(this.opcional, this.protocolNumber);
}

// Estados do Evento CarregarFullBannerEvent

class CarregarFullBannerLoadingState extends OpcionalState {
  @override
  List<Object> get props => [];
}

class CarregarFullBannerNoDataState extends OpcionalState {
  @override
  List<Object> get props => [];
}

class CarregarFullBannerSuccessState extends OpcionalState {
  final OpcionalVO opcional;

  @override
  List<Object> get props => [opcional];

  CarregarFullBannerSuccessState(this.opcional);
}

// Estados do Evento FecharFullBannerEvent

class FecharFullBannerState extends OpcionalState {
  @override
  List<Object> get props => [];
}

// Estados do Evento CarregarListBannersEvent

class CarregarListBannersLoadingState extends OpcionalState {
  @override
  List<Object> get props => [];
}

class CarregarListBannersNoDataState extends OpcionalState {
  final OpcionalVO opcional;
  final String message;

  @override
  List<Object> get props => [opcional, message];

  CarregarListBannersNoDataState(this.opcional, this.message);
}

class CarregarListBannersSuccessState extends OpcionalState {
  final OpcionalVO opcional;

  @override
  List<Object> get props => [opcional];

  CarregarListBannersSuccessState(this.opcional);
}

// Estados do Evento CarregarSaibaMaisBannersEvent

class CarregarSaibaMaisBannerLoadingState extends OpcionalState {
  final OpcionalVO opcional;
  @override
  List<Object> get props => [];
    CarregarSaibaMaisBannerLoadingState({required this.opcional});

}

class CarregarSaibaMaisBannerNoDataState extends OpcionalState {
  final OpcionalVO opcional;

  @override
  List<Object> get props => [opcional];

  CarregarSaibaMaisBannerNoDataState(this.opcional);
}

class CarregarSaibaMaisBannerSuccessState extends OpcionalState {
  final OpcionalVO opcional;

  @override
  List<Object> get props => [opcional];

  CarregarSaibaMaisBannerSuccessState(this.opcional);
}

// Estados do Evento MostrarSaibaMaisBannerEvent

class MostrarSaibaMaisBannerState extends OpcionalState {
  final OpcionalVO? opcional;

  @override
  List<Object?> get props => [opcional];

  MostrarSaibaMaisBannerState(this.opcional);
}

// Estados do Evento FecharSaibaMaisBannerEvent

class FecharSaibaMaisBannerState extends OpcionalState {
  @override
  List<Object> get props => [];
}

// Estados do Evento ChecarOpcionaisEvent

class ShowOpcionaisState extends OpcionalState {
  @override
  List<Object> get props => [];
}

class HideOpcionaisState extends OpcionalState {
  @override
  List<Object> get props => [];
}
