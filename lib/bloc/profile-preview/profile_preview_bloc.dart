import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-preview/profile_preview_event.dart';
import 'package:cliente_minha_unimed/bloc/profile-preview/profile_preview_state.dart';
import 'package:cliente_minha_unimed/models/profile-preview/profile_preview.model.dart';
import 'package:cliente_minha_unimed/shared/api/profile_preview.graphql.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:flutter/material.dart';

const PERIOD_IN_DAYS = 1095; //3 anos

class ProfilePreviewBloc
    extends Bloc<ProfilePreviewEvent, ProfilePreviewState> {
  ProfilePreviewBloc() : super(InitialProfilePreviewState());

  List<ProfilePreviewModel> _listProfilePreview = [];
  List<ProfilePreviewModel> get listProfilePreview => _listProfilePreview;

  List<String> _cards = [];
  List<String> get cards => _cards;

  @override
  Stream<ProfilePreviewState> mapEventToState(
      ProfilePreviewEvent event) async* {
    if (event is ListProfilePreview) {
      yield LoadingProfilePreviewState();

      try {
        _listProfilePreview = await Locator.instance
            .get<ProfilePreviewApi>()
            .listProfilePreview(cpf: event.cpf, onlyActive: event.onlyActive);

        yield LoadedProfilePreviewState(
            listProfilePreview: _listProfilePreview);
      } on UnimedException catch (e) {
        yield ErrorProfilePreviewState(e.message);
      } catch (e) {
        debugPrint(e.toString());
        yield ErrorProfilePreviewState(MessageException.GENERAL);
      }
    } else if (event is RegisterProfilePreviewEvent) {
      try {
        yield CreatingProfilePreviewState();

        final message = await Locator.instance
            .get<ProfilePreviewApi>()
            .registerProfilePreview(
                cpfLogin: event.cpfLogin,
                authorizer: event.authorizer,
                cards: event.cards);

        yield CreatedProfilePreviewState(message: message);
      } on UnimedException catch (e) {
        yield ErrorProfilePreviewState(e.message);
      } catch (e) {
        debugPrint(e.toString());
        yield ErrorProfilePreviewState(MessageException.GENERAL);
      }
    }
  }
}
