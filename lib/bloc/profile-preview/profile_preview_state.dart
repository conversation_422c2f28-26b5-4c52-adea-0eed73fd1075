import 'package:cliente_minha_unimed/models/profile-preview/profile_preview.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ProfilePreviewState extends Equatable {}

class InitialProfilePreviewState extends ProfilePreviewState {
  @override
  List<Object> get props => [];
}

class LoadingProfilePreviewState extends ProfilePreviewState {
  @override
  List<Object> get props => [];
}

class NoDataProfilePreviewState extends ProfilePreviewState {
  @override
  List<Object> get props => [];
}

class CreatingProfilePreviewState extends ProfilePreviewState {
  @override
  List<Object> get props => [];
}

class ErrorProfilePreviewState extends ProfilePreviewState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorProfilePreviewState(this.message);
}

class CreatedProfilePreviewState extends ProfilePreviewState {
  final String message;

  @override
  List<Object> get props => [message];

  CreatedProfilePreviewState({required this.message});
}

class LoadedProfilePreviewState extends ProfilePreviewState {
  final List<ProfilePreviewModel> listProfilePreview;

  @override
  List<Object> get props => [listProfilePreview];

  LoadedProfilePreviewState({required this.listProfilePreview});
}
