import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ProfilePreviewEvent extends Equatable {}

class ListProfilePreview extends ProfilePreviewEvent {
  final String cpf;
  final bool onlyActive;

  @override
  List<Object?> get props => [cpf, onlyActive];

  ListProfilePreview({required this.cpf, this.onlyActive = true});
}

class RegisterProfilePreviewEvent extends ProfilePreviewEvent {
  final String cpfLogin;
  final String authorizer;
  final List<String> cards;

  @override
  List<Object?> get props => [cpfLogin, authorizer, cards];

  RegisterProfilePreviewEvent(
      {required this.cpfLogin, required this.authorizer, required this.cards});
}

class UpdateCardsProfilePreview extends ProfilePreviewEvent {
  final List<String> cards;

  @override
  List<Object?> get props => [cards];

  UpdateCardsProfilePreview({required this.cards});
}
