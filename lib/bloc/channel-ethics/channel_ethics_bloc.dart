


// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'package:cliente_minha_unimed/bloc/channel-ethics/channel_ethics_event.dart';
import 'package:cliente_minha_unimed/bloc/channel-ethics/channel_ethics_state.dart';
import 'package:cliente_minha_unimed/shared/api/channel-ethics.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

class ChannelEthicsBloc extends Bloc<ChannelEthicsEvent,ChannelEthicsState> {
  ChannelEthicsBloc() : super(InitialChannelEthicsState());

  Future<void> getChannelEthics() async {
    try {
      final servicesApi = Locator.instance<ChannelEthicsApi>();
      final response = await servicesApi.getChannelEthics();
      emit(DoneChannelEthicsState(channelEthicsModel: response));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorChannelEthicsState('$ex'));
    }
  }


}