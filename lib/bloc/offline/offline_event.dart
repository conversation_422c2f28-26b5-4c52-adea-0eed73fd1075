import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';

abstract class OfflineEvent extends Equatable {
  const OfflineEvent();

  @override
  List<Object?> get props => [];
}

class GetPerfilModelOfflineEvent extends OfflineEvent {
  final String cpf;

  GetPerfilModelOfflineEvent({required this.cpf});

  @override
  List<Object?> get props => [];
}

class GetCurrentPerfilOfflineEvent extends OfflineEvent {
  final String carteiraFormatada;
  GetCurrentPerfilOfflineEvent({required this.carteiraFormatada});

  @override
  List<Object?> get props => [];
}

class SaveCurrentCarteiraFormatadaEvent extends OfflineEvent {
  final String carteiraFormatada;
  SaveCurrentCarteiraFormatadaEvent({required this.carteiraFormatada});

  @override
  List<Object?> get props => [];
}

class ClearCachedDataEvent extends OfflineEvent {
  @override
  List<Object?> get props => [];
}

class SetCurrentOfflinePerfil extends OfflineEvent {
  final Perfil perfil;

  @override
  List<Object?> get props => [];

  SetCurrentOfflinePerfil({required this.perfil});
}
