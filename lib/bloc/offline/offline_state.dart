import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';

import '../../models/perfil_offline.model.dart';

abstract class OfflineState extends Equatable {
  const OfflineState();

  @override
  List<Object?> get props => [];
}

class OfflineInitialState extends OfflineState {}

class OfflineLoadingState extends OfflineState {}

class DoneGetPerfilModelOfflineState extends OfflineState {
  DoneGetPerfilModelOfflineState(this.perfilOfflineModel);
  final PerfilOfflineModel? perfilOfflineModel;
  @override
  List<Object?> get props => [perfilOfflineModel];
}

class DoneGetCurrentPerfilOfflineState extends OfflineState {
  DoneGetCurrentPerfilOfflineState(this.perfilOffline);
  final Perfil? perfilOffline;
  @override
  List<Object?> get props => [perfilOffline];
}

class ErrorGetPerfilOffline extends OfflineState {
  ErrorGetPerfilOffline(this.message);
  final String? message;
  @override
  List<Object?> get props => [message];
}
