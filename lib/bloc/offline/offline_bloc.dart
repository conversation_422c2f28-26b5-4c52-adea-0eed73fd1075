import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/perfil_offline.model.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:collection/collection.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'offline_event.dart';
import 'offline_state.dart';

class OfflineBloc extends Bloc<OfflineEvent, OfflineState> {
  OfflineBloc() : super(OfflineInitialState());
  final logger = UnimedLogger(className: 'OfflineBloc');
  Perfil? _currentPerfilOffline;
  Perfil? get currentPerfilOffline => _currentPerfilOffline;

  @override
  Stream<OfflineState> mapEventToState(
    OfflineEvent event,
  ) async* {
    if (event is SaveCurrentCarteiraFormatadaEvent) {
      try {
        final _prefs = await SharedPreferences.getInstance();
        final _sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';
        final _key = 'currentCarteiraFormatada$_sufix';
        await _prefs.remove(_key);
        await _prefs.setString(_key, event.carteiraFormatada);
      } catch (e) {
        logger.d('Exception in GetPerfilOfflineEvent $e');
        yield ErrorGetPerfilOffline(MessageException.GENERAL);
      }
    } else if (event is GetPerfilModelOfflineEvent) {
      yield OfflineLoadingState();
      try {
        final _prefs = await SharedPreferences.getInstance();
        final _sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';
        final _key = 'perfisOffline_$_sufix';
        final _perfilOfflineModel = PerfilOfflineModel.fromJson(
            jsonDecode(_prefs.getString(_key) ?? ''));

        final _perfil = _perfilOfflineModel.list?.firstWhereOrNull((perfil) =>
            perfil.contratoBeneficiario.beneficiario?.cpf11Digits == event.cpf);

        if (_perfil != null) {
          _currentPerfilOffline = _perfil;
          yield DoneGetPerfilModelOfflineState(_perfilOfflineModel);
        } else {
          yield ErrorGetPerfilOffline(MessageException.GENERAL);
        }
      } catch (e) {
        logger.d('Exception in GetPerfilOfflineEvent $e');
        yield ErrorGetPerfilOffline(MessageException.GENERAL);
      }
    } else if (event is GetCurrentPerfilOfflineEvent) {
      yield OfflineLoadingState();
      try {
        String _carteira = '';
        final _prefs = await SharedPreferences.getInstance();
        final _sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';
        _carteira = event.carteiraFormatada.isEmpty
            ? _prefs.getString('currentCarteiraFormatada$_sufix') ?? ''
            : event.carteiraFormatada;

        final _key = 'perfisOffline_$_sufix';
        final _perfilOfflineModel = PerfilOfflineModel.fromJson(
            jsonDecode(_prefs.getString(_key) ?? ''));

        final _perfil = _perfilOfflineModel.list?.firstWhereOrNull(
            (perfil) => perfil.carteira?.carteiraFormatada == _carteira);

        if (_perfil != null) {
          _currentPerfilOffline = _perfil;
          yield DoneGetCurrentPerfilOfflineState(_perfil);
        } else {
          yield ErrorGetPerfilOffline('Nenhum perfil offline encontrado!');
        }
      } catch (e) {
        logger.d('Exception in GetPerfilOfflineEvent $e');
        yield ErrorGetPerfilOffline(MessageException.GENERAL);
      }
    } else if (event is ClearCachedDataEvent) {
      try {
        final _prefs = await SharedPreferences.getInstance();
        final _sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';
        await _prefs.remove('currentCarteiraFormatada$_sufix');
        await _prefs.remove('perfisOffline_$_sufix');
        yield OfflineInitialState();
      } catch (e) {
        logger.d('Exception in ClearCachedDataEvent $e');
      }
    } else if (event is SetCurrentOfflinePerfil) {
      yield OfflineLoadingState();
      _currentPerfilOffline = event.perfil;
      yield DoneGetCurrentPerfilOfflineState(_currentPerfilOffline);
    }
  }
}
