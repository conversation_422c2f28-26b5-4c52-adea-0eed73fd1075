part of 'full_health_attention_bloc.dart';

abstract class FullHealthAttentionEvent extends Equatable {
  const FullHealthAttentionEvent();

  @override
  List<Object?> get props => [];
}

class GetFullHealthAttentionSchedule extends FullHealthAttentionEvent {
  final String? loadingMessage;
  final String? defaultErrorMessage;
  final Perfil? perfil;
  final DateRange? rangeDate;
  GetFullHealthAttentionSchedule({
    this.loadingMessage,
    this.defaultErrorMessage,
    this.perfil,
    this.rangeDate,
  });
  @override
  List<Object?> get props => [loadingMessage, perfil];
}

class GetDoctorsByDay extends FullHealthAttentionEvent {
  final String? loadingMessage;
  final String? defaultErrorMessage;
  final Perfil? perfil;
  final DateTime? selectedDay;
  GetDoctorsByDay({
    this.loadingMessage,
    this.defaultErrorMessage,
    this.perfil,
    this.selectedDay,
  });
  @override
  List<Object?> get props => [loadingMessage, perfil];
}

class CreateFHAConsultation extends FullHealthAttentionEvent {
  final String loadingMessage;
  final String defaultErrorMessage;
  final FHAConsultationParams params;
  CreateFHAConsultation({
    required this.loadingMessage,
    required this.defaultErrorMessage,
    required this.params,
  });
  @override
  List<Object> get props => [loadingMessage];
}

class ResetFullHealthAttention extends FullHealthAttentionEvent {
  const ResetFullHealthAttention();

  @override
  List<Object> get props => [];
}
