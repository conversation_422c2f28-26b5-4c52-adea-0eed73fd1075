import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/conecta-saude/consultation-params.model.dart';
import 'package:cliente_minha_unimed/models/conecta-saude/create_consultation.model.dart';
import 'package:cliente_minha_unimed/models/conecta-saude/fha-schedule.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/consulta-virtual.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/date_utils.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'full_health_attention_event.dart';
part 'full_health_attention_state.dart';

class FullHealthAttentionBloc
    extends Bloc<FullHealthAttentionEvent, FullHealthAttentionState> {
  final ConsultaVirtualAgendamentoApi conectaSaudeApi;
  FullHealthAttentionBloc({required this.conectaSaudeApi})
      : super(FullHealthAttentionInitial());

  final logger = UnimedLogger(className: 'FullHealthAttentionBloc');
  


  @override
  Stream<FullHealthAttentionState> mapEventToState(
    FullHealthAttentionEvent event,
  ) async* {
    if (event is GetFullHealthAttentionSchedule) {
      yield* _handleGetFullHealthAttentionSchedule(event);
    } else if (event is GetDoctorsByDay) {
      yield* _handleGetDoctorsByDay(event);
    } else if (event is CreateFHAConsultation) {
      yield* _handleCreateConsultation(event);
    } else if (event is ResetFullHealthAttention) {
      yield FullHealthAttentionInitial();
    }
  }

  Stream<FullHealthAttentionState> _handleGetFullHealthAttentionSchedule(
      GetFullHealthAttentionSchedule event) async* {
    try {
      yield FullHealthAttentionLoading(message: event.loadingMessage);

      final schedule = await conectaSaudeApi.getAvailableAgendaByMonth(
          event.perfil!, event.rangeDate!);

      yield ScheduleLoaded(schedule);
    } catch (e) {
      logger.e('Exception in GetFullHealthAttentionSchedule $e');
      yield FullHealthAttentionError(message: event.defaultErrorMessage);
    }
  }

  Stream<FullHealthAttentionState> _handleGetDoctorsByDay(
    GetDoctorsByDay event) async* {
  try {
    yield FullHealthAttentionLoading(message: event.loadingMessage);

    final Map<String, List<FHAProviderModel>> providers = await conectaSaudeApi
        .getProvidersByDay(event.selectedDay!, event.perfil!)
        .timeout(Duration(seconds: 30),
            onTimeout: (() =>
                throw UnimedException(event.defaultErrorMessage as String)));

    yield DoctorsByDayLoaded(
      providers['doctors'] ?? [],
      providers['nurses'] ?? [],
      event.selectedDay,
    );
  } catch (e) {
    logger.e('Exception in GetDoctorsByDay $e');
    final message =
        e is UnimedException ? e.message : event.defaultErrorMessage;
    yield FullHealthAttentionError(message: message);
  }
}

  Stream<FullHealthAttentionState> _handleCreateConsultation(
      CreateFHAConsultation event) async* {
    try {
      yield FullHealthAttentionLoading(message: event.loadingMessage);
      const String CONSULTATION_TYPE = 'PRIMEIRA CONSULTA';

      final result = await conectaSaudeApi.createConsultation(
          perfil: event.params.perfil,
          createConsultation: CreateFHAConsultationModel(
              id: event.params.chosenSchedule.chosenSchedule!.id,
              type: CONSULTATION_TYPE,
              specialtycode:
                  event.params.chosenSchedule.chosenSchedule!.code.toString(),
              beneficiaryCard: event
                  .params.perfil.contratoBeneficiario.carteira!.carteiraNumero,
              beneficiaryDoc: event
                  .params.perfil.contratoBeneficiario.beneficiario!.cpf
                  .toString(),
              contact: FHAContactModel(
                email: event.params.contact.email,
                phone: event.params.contact.telefone,
                optInWhatsapp: event.params.contact.agreeWhatsapp,
              )));
      yield ConsultationCreated(event.params.copyWith(
          protocolNumber: result.protocolNumber, type: CONSULTATION_TYPE));
    } catch (e) {
      logger.e('Exception in CreateFHAConsultation $e');
      yield FullHealthAttentionError(message: e.toString());
    }
  }
}
