part of 'full_health_attention_bloc.dart';

abstract class FullHealthAttentionState extends Equatable {
  const FullHealthAttentionState();

  @override
  List<Object?> get props => [];
}

class FullHealthAttentionInitial extends FullHealthAttentionState {}

class FullHealthAttentionLoading extends FullHealthAttentionState {
  final String? message;
  FullHealthAttentionLoading({
    this.message,
  });
  @override
  List<Object?> get props => [message];
}

class FullHealthAttentionError extends FullHealthAttentionState {
  final String? message;
  FullHealthAttentionError({
    this.message,
  });
  @override
  List<Object?> get props => [message];
}

class ScheduleLoaded extends FullHealthAttentionState {
  final List<DateTime> availableDays;
  ScheduleLoaded(this.availableDays);
  @override
  List<Object> get props => [availableDays];
}

class DoctorsByDayLoaded extends FullHealthAttentionState {
  final List<FHAProviderModel> doctors;
  final List<FHAProviderModel> nurses;
  final DateTime? date;
  DoctorsByDayLoaded(this.doctors, this.nurses, this.date);
  @override
  List<Object?> get props => [doctors, nurses, date];
}

class ConsultationCreated extends FullHealthAttentionState {
  final FHAConsultationParams params;
  ConsultationCreated(this.params);
  @override
  List<Object> get props => [params];
}
