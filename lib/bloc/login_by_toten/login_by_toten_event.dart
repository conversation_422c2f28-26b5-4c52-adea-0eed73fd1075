import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';

@immutable
abstract class LoginByTotenEvent extends Equatable {}

class GetToken extends LoginByTotenEvent {
  final String qrCode;

  GetToken({
    required this.qrCode,
  });

  @override
  List<Object> get props => [qrCode];
}

class SetToInitialStateToten extends LoginByTotenEvent {
  SetToInitialStateToten();

  @override
  List<Object?> get props => [];
}

class RequestCameraPermission extends LoginByTotenEvent {
  RequestCameraPermission();

  @override
  List<Object?> get props => [];
}
