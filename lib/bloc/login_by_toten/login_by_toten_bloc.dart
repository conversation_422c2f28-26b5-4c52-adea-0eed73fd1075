import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/login_by_toten/login_by_toten_event.dart';
import 'package:cliente_minha_unimed/bloc/login_by_toten/login_by_toten_state.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class LoginByTotenBloc extends Bloc<LoginByTotenEvent, LoginByTotenState> {
  LoginByTotenBloc() : super(InitialLoginByTotenState());

  @override
  Stream<LoginByTotenState> mapEventToState(
    LoginByTotenEvent event,
  ) async* {
    if (event is GetToken) {
      yield LoadingLoginByTotenState();

      try {
        final logginNoPasswordVO = await Locator.instance
            .get<AuthApi>()
            .getLoginToken(qrCode: event.qrCode);

        yield DoneLoginByTotenState(logginNoPasswordVO: logginNoPasswordVO);
      } catch (err) {
        yield ErrorLoginByTotenState('$err');
      }
    } else if (event is SetToInitialStateToten) {
      yield InitialLoginByTotenState();
    } else if (event is RequestCameraPermission) {
      yield LoadingRequestCameraState();

      // final cameraStatus = await Permission.camera.status;
      // if (cameraStatus == PermissionStatus.granted) {
      // } else {
      //   HandleCamera.requestCameraPermission(
      //       context, 'Precisamos acessar sua câmera para ler o QR Code');
      // }
    }
  }
}
