import 'package:cliente_minha_unimed/shared/api/vo/loggin_no_password.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class LoginByTotenState extends Equatable {}

class InitialLoginByTotenState extends LoginByTotenState {
  @override
  List<Object> get props => [];
}

class LoadingLoginByTotenState extends LoginByTotenState {
  @override
  List<Object> get props => [];
}

class LoadingRequestCameraState extends LoginByTotenState {
  @override
  List<Object> get props => [];
}

class ErrorLoginByTotenState extends LoginByTotenState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorLoginByTotenState(this.message);
}

class DoneLoginByTotenState extends LoginByTotenState {
  final LogginNoPasswordVO logginNoPasswordVO;

  @override
  List<Object?> get props => [logginNoPasswordVO];

  DoneLoginByTotenState({required this.logginNoPasswordVO});
}
