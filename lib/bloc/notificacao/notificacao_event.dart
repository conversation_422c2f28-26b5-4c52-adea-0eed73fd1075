import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/utils/sqlite-manager/tables/notificacoes.table.dart';

@immutable
abstract class NotificacaoEvent extends Equatable {}

class MostrarPopup extends NotificacaoEvent {
  final NotificacaoRecordSQLite? notification;
  final bool isHome;

  @override
  List<Object?> get props => [notification];

  MostrarPopup({this.notification, this.isHome = false});
}

class ListarNotificacoes extends NotificacaoEvent {
  final Perfil perfil;
  final bool isApply;

  @override
  List<Object> get props => [];

  ListarNotificacoes({required this.perfil, this.isApply = false});
}

class ReadNotification extends NotificacaoEvent {
  final Perfil perfil;
  final String notificationId;

  @override
  List<Object> get props => [];

  ReadNotification({required this.perfil, required this.notificationId});
}

class MarkUnreadNotification extends NotificacaoEvent {
  final Perfil perfil;
  final String notificationId;

  @override
  List<Object> get props => [];

  MarkUnreadNotification({required this.perfil, required this.notificationId});
}

class PinNotification extends NotificacaoEvent {
  final Perfil perfil;
  final String notificationId;

  @override
  List<Object> get props => [];

  PinNotification({required this.perfil, required this.notificationId});
}

class UnpinNotification extends NotificacaoEvent {
  final Perfil perfil;
  final String notificationId;

  @override
  List<Object> get props => [];

  UnpinNotification({required this.perfil, required this.notificationId});
}

class DeleteNotification extends NotificacaoEvent {
  final Perfil perfil;
  final String notificationId;

  @override
  List<Object> get props => [];

  DeleteNotification({required this.perfil, required this.notificationId});
}
