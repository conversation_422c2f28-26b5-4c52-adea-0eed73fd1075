import 'package:cliente_minha_unimed/models/notification.model.dart';
import 'package:cliente_minha_unimed/shared/utils/sqlite-manager/tables/notificacoes.table.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class NotificacaoState extends Equatable {}

class InitialNotificacaoState extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class NotificacaoConfirmacaoState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoConfirmacaoState({required this.notificacao});
}

class NotificacaoLembreteState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoLembreteState({required this.notificacao});
}

class NotificacaoLembreteSalaState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoLembreteSalaState({required this.notificacao});
}

class NotificacaoPendenciaResolvidaState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoPendenciaResolvidaState({required this.notificacao});
}

class NotificacaoInvalidaState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoInvalidaState({this.notificacao});
}

class NotificacaoCoparticipacaoState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoCoparticipacaoState({required this.notificacao});
}

class NotificacaoAutorizacaoSucessState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoAutorizacaoSucessState({required this.notificacao});
}

class NotificacaoAutorizacaoFailedState extends NotificacaoState {
  final NotificacaoRecordSQLite? notificacao;

  @override
  List<Object?> get props => [notificacao];

  NotificacaoAutorizacaoFailedState({required this.notificacao});
}

class VESNotificationState extends NotificacaoState {
  final NotificacaoRecordSQLite? notification;
  final bool isHome;
  final bool isPediatric;

  @override
  List<Object?> get props => [notification];

  VESNotificationState({required this.notification, required this.isHome, required this.isPediatric});
}

class ListaNotificacaoDone extends NotificacaoState {
  final List<NotificationModel?> list;

  @override
  List<Object> get props => [list];

  ListaNotificacaoDone({required this.list});
}

class ListarNotificacoesLoading extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class ApplyNotificationsLoading extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class ListarNotificacoesNoData extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class ApplyNotificationsState extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class ErrorNotifications extends NotificacaoState {
  final String message;

  @override
  List<Object> get props => [];

  ErrorNotifications({required this.message});
}

class ErrorApplyNotifications extends NotificacaoState {
  final String message;

  @override
  List<Object> get props => [];

  ErrorApplyNotifications({required this.message});
}
