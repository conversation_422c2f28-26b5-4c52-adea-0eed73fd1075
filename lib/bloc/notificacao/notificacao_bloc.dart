import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_event.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_state.dart';
import 'package:cliente_minha_unimed/models/notificacao.model.dart';
import 'package:cliente_minha_unimed/models/notification.model.dart';
import 'package:cliente_minha_unimed/shared/api/notificacao/notification.api.dart';
import 'package:cliente_minha_unimed/shared/constants.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';

class NotificacaoBloc extends Bloc<NotificacaoEvent, NotificacaoState> {
  NotificacaoBloc() : super(InitialNotificacaoState());
  final logger = UnimedLogger(className: 'NotificacaoBloc');

  final _controller = StreamController<List<NotificationModel?>>.broadcast();
  Stream<List<NotificationModel?>> get streamNotifications => _controller.stream;

  @override
  Stream<NotificacaoState> mapEventToState(NotificacaoEvent event) async* {
    if (event is MostrarPopup) {
      if (event.notification?.additionalData == null) return;
      final notification = AdditionalNotificacao.fromJson(event.notification!.additionalData!);
      debugPrint("========== categoria: ${notification.categoria}");
      if (notification.categoria == PUSH_CONFIRMAR) {
        yield NotificacaoConfirmacaoState(
          notificacao: event.notification,
        );
      } else if (notification.categoria == PUSH_LEMBRETE) {
        yield NotificacaoLembreteState(notificacao: event.notification);
      } else if (notification.categoria == PUSH_LEMBRETE_SALA) {
        yield NotificacaoLembreteSalaState(notificacao: event.notification);
      } else if (notification.categoria == PUSH_PENDENCIA_RESOLVIDA) {
        yield NotificacaoPendenciaResolvidaState(notificacao: event.notification);
      } else if (notification.categoria == PUSH_COPARTICIPACAO) {
        yield NotificacaoCoparticipacaoState(notificacao: event.notification);
      } else if (notification.categoria == AUTORIZACAO_ENVIO_SUCESSO) {
        yield NotificacaoAutorizacaoSucessState(notificacao: event.notification);
      } else if (notification.categoria == AUTORIZACAO_ENVIO_FALHA) {
        yield NotificacaoAutorizacaoFailedState(notificacao: event.notification);
      } else if (notification.categoria == VES_NOTIFICATION) {
        yield VESNotificationState(
          notification: event.notification,
          isHome: event.isHome,
          isPediatric: false,
        );
      } else if (notification.categoria == VES_CHILD_NOTIFICATION) {
        yield VESNotificationState(
          notification: event.notification,
          isHome: event.isHome,
          isPediatric: true,
        );
      } else {
        yield NotificacaoInvalidaState(notificacao: event.notification);
      }
    } else if (event is ListarNotificacoes) {
      try {
        if (!event.isApply) {
          yield ListarNotificacoesLoading();
        }
        final _notifications = await Locator.instance.get<NotificationApi>().getNotificationsList(perfil: event.perfil);
        if (_notifications.isEmpty) {
          yield ListarNotificacoesNoData();
        } else {
          _notifications.sort((a, b) {
            return DateTime.parse(b!.sentAt).compareTo(DateTime.parse(a!.sentAt));
          });
          _notifications.sort((a, b) {
            if (a?.pinned == b?.pinned) {
              return DateTime.parse(b!.sentAt).compareTo(DateTime.parse(a!.sentAt));
            }
            return a!.pinned! ? -1 : 1;
          });

          _controller.add(_notifications);

          yield ListaNotificacaoDone(list: _notifications);
        }
      } catch (e) {
        logger.e('error ListarNotificacoes => $e');
        yield ErrorNotifications(message: e.toString());
      }
    } else if (event is ReadNotification) {
      try {
        yield ApplyNotificationsLoading();
        await Locator.instance.get<NotificationApi>().readNotification(perfil: event.perfil, notificationId: event.notificationId);
        yield ApplyNotificationsState();
      } catch (e) {
        logger.e('error ReadNotification => $e');
        yield ErrorApplyNotifications(message: e.toString());
      }
    } else if (event is MarkUnreadNotification) {
      try {
        yield ApplyNotificationsLoading();
        await Locator.instance.get<NotificationApi>().markUnreadNotification(perfil: event.perfil, notificationId: event.notificationId);
        yield ApplyNotificationsState();
      } catch (e) {
        logger.e('error MarkUnreadNotification => $e');
        yield ErrorApplyNotifications(message: e.toString());
      }
    } else if (event is PinNotification) {
      try {
        yield ApplyNotificationsLoading();
        await Locator.instance.get<NotificationApi>().pinNotification(perfil: event.perfil, notificationId: event.notificationId);
        yield ApplyNotificationsState();
      } catch (e) {
        logger.e('error PinNotification => $e');
        yield ErrorApplyNotifications(message: e.toString());
      }
    } else if (event is UnpinNotification) {
      try {
        yield ApplyNotificationsLoading();
        await Locator.instance.get<NotificationApi>().unpinNotification(perfil: event.perfil, notificationId: event.notificationId);
        yield ApplyNotificationsState();
      } catch (e) {
        logger.e('error UnpinNotification => $e');
        yield ErrorApplyNotifications(message: e.toString());
      }
    } else if (event is DeleteNotification) {
      try {
        yield ApplyNotificationsLoading();
        await Locator.instance.get<NotificationApi>().deleteNotification(perfil: event.perfil, notificationId: event.notificationId);
        yield ApplyNotificationsState();
      } catch (e) {
        logger.e('error DeleteNotification => $e');
        yield ErrorApplyNotifications(message: e.toString());
      }
    }
  }
}
