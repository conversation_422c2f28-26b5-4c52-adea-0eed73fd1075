import 'package:cliente_minha_unimed/models/permissions/login_permissions.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class LoginPermissionState extends Equatable {}

class InitialLoginPermissionState extends LoginPermissionState {
  @override
  List<Object> get props => [];
}

class LoadingLoginPermissionState extends LoginPermissionState {
  @override
  List<Object> get props => [];
}

class ErrorLoginPermissionState extends LoginPermissionState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorLoginPermissionState(this.message);
}

class DoneLoginPermissionState extends LoginPermissionState {
  final LoginPermissionsModel loginPermissions;

  @override
  List<Object?> get props => [loginPermissions];

  DoneLoginPermissionState({required this.loginPermissions});
}
