import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/login_permission/login_permission_event.dart';
import 'package:cliente_minha_unimed/bloc/login_permission/login_permission_state.dart';
import 'package:cliente_minha_unimed/models/permissions/login_permissions.model.dart';
import 'package:cliente_minha_unimed/shared/api/profile_roles.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class LoginPermissionBloc
    extends Bloc<LoginPermissionEvent, LoginPermissionState> {
  LoginPermissionBloc() : super(InitialLoginPermissionState());

  LoginPermissionsModel? _loginPermissions;
  LoginPermissionsModel? get loginPermissions => _loginPermissions;

  @override
  Stream<LoginPermissionState> mapEventToState(
    LoginPermissionEvent event,
  ) async* {
    if (event is GetLoginPermission) {
      yield LoadingLoginPermissionState();

      try {
        _loginPermissions =
            await Locator.instance.get<ProfileRolesApi>().loginPermissions();

        yield DoneLoginPermissionState(loginPermissions: _loginPermissions!);
      } catch (err) {
        yield ErrorLoginPermissionState('$err');
      }
    } else if (event is SetToInitialState) {
      yield InitialLoginPermissionState();
    }
  }
}
