import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/virtual-emergency-service/ve-consultation.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'list_ve_consultations_event.dart';
part 'list_ve_consultations_state.dart';

const GENERIC_MESSAGE_ERROR = 'Não foi possível listar os Pronto Atendimentos';

class ListVeConsultationsBloc
    extends Bloc<ListVeConsultationsEvent, ListVeConsultationsState> {
  ListVeConsultationsBloc({required this.agendamentoApi})
      : super(ListVeConsultationsInitial());
  final AgendamentoApi agendamentoApi;

  final logger = UnimedLogger(className: 'ListVEConsultationBloc');
  Iterable<VEConsultationModel> _consultations = [];

  @override
  Stream<ListVeConsultationsState> mapEventToState(
    ListVeConsultationsEvent event,
  ) async* {
    if (event is GetVEList) {
      yield* _handleGetVEList(event);
    } else if (event is ResetState) {
      _consultations = [];
      yield ListVeConsultationsInitial();
    }
  }

  Stream<ListVeConsultationsState> _handleGetVEList(GetVEList event) async* {
    try {
      yield ListVeConsultationsLoading();
      if (_consultations.isNotEmpty && !event.forceReload) {
        logger
            .d('GetVEList ListVeConsultationsReady - ${_consultations.length}');

        yield ListVeConsultationsReady(
            _consultations as List<VEConsultationModel>);

        return;
      }

      _consultations =
          await agendamentoApi.getAllVEConsultations(event.perfil!);

      if (_consultations.length == 0) {
        logger
            .d('GetVEList ListVeConsultationsNoData - _consultations is empty');

        yield ListVeConsultationsNoData();

        return;
      }

      logger.d('GetVEList ListVeConsultationsReady - $_consultations');
      yield ListVeConsultationsReady(
          _consultations as List<VEConsultationModel>);
    } catch (ex) {
      logger.e('GetVEList ListVeConsultationsError - $ex');
      final String? message =
          ex is UnimedException ? ex.message : GENERIC_MESSAGE_ERROR;
      yield ListVeConsultationsError(message);
    }
  }
}
