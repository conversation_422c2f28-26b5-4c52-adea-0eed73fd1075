part of 'list_ve_consultations_bloc.dart';

abstract class ListVeConsultationsState extends Equatable {
  const ListVeConsultationsState();

  @override
  List<Object> get props => [];
}

class ListVeConsultationsInitial extends ListVeConsultationsState {}

class ListVeConsultationsReady extends ListVeConsultationsState {
  ListVeConsultationsReady(this.veConsultations);
  final List<VEConsultationModel> veConsultations;
}

class ListVeConsultationsLoading extends ListVeConsultationsState {}

class ListVeConsultationsNoData extends ListVeConsultationsState {}

class ListVeConsultationsError extends ListVeConsultationsState {
  ListVeConsultationsError(this.message);
  final String? message;
}
