part of 'pa_virtual_bloc.dart';

abstract class PAVirtualEvent extends Equatable {
  const PAVirtualEvent();

  @override
  List<Object?> get props => [];
}

class VerifyTimePA extends PAVirtualEvent {
  final Car<PERSON><PERSON> carteira;

  @override
  List<Object> get props => [carteira];

  VerifyTimePA(this.carteira);
}

class PAAdvanceStep extends PAVirtualEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  PAAdvanceStep(this.indexPage);
}

class PABackStep extends PAVirtualEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  PABackStep(this.indexPage);
}

class UpdateVeServiceModel extends PAVirtualEvent {
  final VEServiceModel? veServiceModel;

  @override
  List<Object?> get props => [veServiceModel];

  UpdateVeServiceModel(this.veServiceModel);
}
