import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';

import 'package:cliente_minha_unimed/models/virtual-emergency-service/virtual-emergency-service.model.dart';

part 'pa_virtual_event.dart';
part 'pa_virtual_state.dart';

class PAVirtualBloc extends Bloc<PAVirtualEvent, PAVirtualState> {
  PAVirtualBloc() : super(PAVirtualInitial());

  int _pageIndex = 0;
  int get pageIndex => _pageIndex;

  VEServiceModel? _veServiceModel;
  VEServiceModel? get veServiceModel => _veServiceModel;

  @override
  Stream<PAVirtualState> mapEventToState(
    PAVirtualEvent event,
  ) async* {
    if (event is PAAdvanceStep) {
      event.indexPage < 0 ? _pageIndex++ : _pageIndex = event.indexPage;
      //_pageIndex = event.indexPage;

      yield PAAdvancedStepState(toIndex: _pageIndex);
      yield PAVirtualInitial();
    } else if (event is PABackStep) {
      event.indexPage < 0 ? _pageIndex-- : _pageIndex = event.indexPage;
      //_pageIndex = event.indexPage;
      yield PABackedStepState();
      yield PAVirtualInitial();
    } else if (event is UpdateVeServiceModel) {
      _veServiceModel = event.veServiceModel;
    }
  }
}
