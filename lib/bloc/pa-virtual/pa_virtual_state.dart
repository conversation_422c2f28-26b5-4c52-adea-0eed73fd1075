part of 'pa_virtual_bloc.dart';

abstract class PAVirtualState extends Equatable {
  const PAVirtualState();

  @override
  List<Object?> get props => [];
}

class PAVirtualInitial extends PAVirtualState {}

class LoadingPAVirtualState extends PAVirtualState {}

class ErrorPAVirtualState extends PAVirtualState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorPAVirtualState(this.message);
}

class LoadedPAVirtualState extends PAVirtualState {
  final VEServiceModel? veServiceModel;

  @override
  List<Object?> get props => [veServiceModel];

  LoadedPAVirtualState({this.veServiceModel});
}

class PAAdvancedStepState extends PAVirtualState {
  final int toIndex;

  @override
  List<Object?> get props => [toIndex];

  PAAdvancedStepState({required this.toIndex});
}

class PABackedStepState extends PAVirtualState {}

class PALoadingState extends PAVirtualState {}
