import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-emergency-service/verification.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/version.service.dart';
import 'package:equatable/equatable.dart';

part 'verify_time_pa_event.dart';
part 'verify_time_pa_state.dart';

class VerifyTimePaBloc extends Bloc<VerifyTimePaEvent, VerifyTimePaState> {
  VerifyTimePaBloc() : super(VerifyTimePaInitial());

  @override
  Stream<VerifyTimePaState> mapEventToState(
    VerifyTimePaEvent event,
  ) async* {
    if (event is VerifyTimePA) {
      yield LoadingVerificationState();
      try {
        final versionInfo =
            await Locator.instance.get<VersionService>().getInfo();

        // o Verbo HTTP define se é sucesso ou não, sem precisar pegar o response
        String? message = await Locator.instance
            .get<VerificationApi>()
            .verifyTimePA(
                version: versionInfo.version,
                carteira: event.carteira,
                isPediatric: event.isPediatric);
        //Caso retorne um string, siginifica que e um novo pa e exibira uma mensagem do serviço
        if (message != null && message.isNotEmpty) {
          yield ShowMessageNewPAState(message);
        } else
          yield SuccessVerificationState(isPediatric: event.isPediatric);
      } catch (ex) {
        yield ErrorVerificationState('$ex');
      }
    }
  }
}
