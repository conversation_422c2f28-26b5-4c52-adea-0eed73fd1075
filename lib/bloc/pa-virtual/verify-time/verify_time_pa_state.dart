part of 'verify_time_pa_bloc.dart';

abstract class VerifyTimePaState extends Equatable {
  const VerifyTimePaState();

  @override
  List<Object> get props => [];
}

class VerifyTimePaInitial extends VerifyTimePaState {}

class LoadingVerificationState extends VerifyTimePaState {}

class ErrorVerificationState extends VerifyTimePaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorVerificationState(this.message);
}

class SuccessVerificationState extends VerifyTimePaState {
  final bool isPediatric;

  SuccessVerificationState({required this.isPediatric});
}

class ShowMessageNewPAState extends VerifyTimePaState {
  final String message;

  @override
  List<Object> get props => [message];

  ShowMessageNewPAState(this.message);
}
