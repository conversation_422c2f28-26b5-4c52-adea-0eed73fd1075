import 'package:cliente_minha_unimed/models/permissions/login_permissions.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class UserState extends Equatable {}

class InitialUserState extends UserState {
  @override
  List<Object> get props => [];
}

class LoadingUserState extends UserState {
  @override
  List<Object> get props => [];
}

class ErrorUserState extends UserState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorUserState(this.message);
}

class LoadedUserState extends UserState {
  final User? user;

  @override
  List<Object?> get props => [user];

  LoadedUserState(this.user);
}

class InitialLogoutState extends UserState {
  @override
  List<Object> get props => [];
}

class LoadingLogoutUserState extends UserState {
  @override
  List<Object> get props => [];
}

class SuccessLogoutUserState extends UserState {
  @override
  List<Object> get props => [];
}

class LoadingLoginPermissionsState extends UserState {
  @override
  List<Object> get props => [];
}

class LoadedLoginPermissionState extends UserState {
  final LoginPermissionsModel loginPermissions;

  @override
  List<Object> get props => [loginPermissions];

  LoadedLoginPermissionState(this.loginPermissions);
}
