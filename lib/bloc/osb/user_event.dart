import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/loggin_no_password.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class UserEvent extends Equatable {}

class Authenticate extends UserEvent {
  final UserCredentials credentials;

  @override
  List<Object> get props => [credentials];

  Authenticate({required this.credentials});
}

class AuthenticateByToken extends UserEvent {
  final LogginNoPasswordVO logginNoPasswordVO;

  @override
  List<Object> get props => [logginNoPasswordVO];

  AuthenticateByToken({required this.logginNoPasswordVO});
}

class Reset extends UserEvent {
  @override
  List<Object> get props => [];
}

class Logout extends UserEvent {
  @override
  List<Object> get props => [];
}

class ResetLogout extends UserEvent {
  @override
  List<Object> get props => [];
}

class GetLoginPermissions extends UserEvent {
  @override
  List<Object> get props => [];
}
