import 'package:cliente_minha_unimed/bloc/osb/user_event.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_state.dart';
import 'package:cliente_minha_unimed/models/general-config/general-config.model.dart';
import 'package:cliente_minha_unimed/models/permissions/login_permissions.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/general-config/general-config.api.dart';
import 'package:cliente_minha_unimed/shared/api/profile_roles.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pa_virtual/shared/services/background_service.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  UserBloc() : super(InitialUserState());
  final logger = UnimedLogger(className: 'UserBloc');

  late User _user;
  late UserCredentials _userCredentials;

  User get user => _user;
  UserCredentials get credentials {
    return _userCredentials;
  }

  LoginPermissionsModel? _loginPermissions;
  LoginPermissionsModel? get loginPermissions => _loginPermissions;

  @override
  Stream<UserState> mapEventToState(
    UserEvent event,
  ) async* {
    if (event is Authenticate) {
      yield LoadingUserState();
      _userCredentials = event.credentials;
      try {
        _user = await Locator.instance.get<AuthApi>().login(_userCredentials);

        final carteira = _user.getPerfilByCpf(_user.login).contratoBeneficiario.carteira!;
        final _generalConfig = await Locator.instance.get<GeneralConfigApi>().getGeneralConfig(carteiraId: carteira.carteiraNumero);
        _setGeneralConfig(_generalConfig);

        yield LoadedUserState(_user);
      } on AuthException catch (e) {
        yield ErrorUserState(e.message);
      } catch (err) {
        logger.e('Authenticate event error $err');
        yield ErrorUserState(MessageException.GENERAL);
      }
    } else if (event is AuthenticateByToken) {
      yield LoadingUserState();
      _userCredentials = UserCredentials(cpf: event.logginNoPasswordVO.cpf);
      try {
        _user = await Locator.instance.get<AuthApi>().loginByToken(event.logginNoPasswordVO);

        final carteira = _user.getPerfilByCpf(_user.login).contratoBeneficiario.carteira!;
        final _generalConfig = await Locator.instance.get<GeneralConfigApi>().getGeneralConfig(carteiraId: carteira.carteiraNumero);
        _setGeneralConfig(_generalConfig);

        yield LoadedUserState(_user);
      } on AuthException catch (e) {
        yield ErrorUserState(e.message);
      } catch (err) {
        logger.e('Authenticate event error $err');
        yield ErrorUserState(MessageException.GENERAL);
      }
    } else if (event is Reset) {
      yield InitialUserState();
    } else if (event is ResetLogout) {
      yield InitialLogoutState();
    } else if (event is GetLoginPermissions) {
      yield LoadingLoginPermissionsState();

      try {
        _loginPermissions = await Locator.instance.get<ProfileRolesApi>().loginPermissions();
        if (_loginPermissions?.canSignUp ?? false) {
          yield LoadedLoginPermissionState(_loginPermissions!);
        } else {
          yield ErrorUserState(_loginPermissions?.messages?.canSingUp ?? MessageException.GENERAL);
        }
      } catch (err) {
        logger.e('GetLoginPermissions event error $err');
        yield ErrorUserState(MessageException.GENERAL);
      }
    } else if (event is Logout) {
      yield LoadingLogoutUserState();

      //_user = null;
      await Locator.instance.get<AuthApi>().logout(_userCredentials, _user.FCMUserId);
      //_userCredentials = null;

      BackgroundServicePa.stopService();

      yield SuccessLogoutUserState();
    }
  }
}

_setGeneralConfig(GeneralConfigModel generalConfig) {
  if (Locator.instance.isRegistered<GeneralConfigModel>()) {
    Locator.instance.unregister<GeneralConfigModel>();
  }

  Locator.instance.registerSingleton<GeneralConfigModel>(generalConfig);
}
