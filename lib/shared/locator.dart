import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/consulta-virtual.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/consulta.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/guia-medico.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/schedule_exams.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/teleconsulta.api.dart';
import 'package:cliente_minha_unimed/shared/api/assets.graphql.dart';
import 'package:cliente_minha_unimed/shared/api/auth-token.api.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/banner.api.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/cadastro.api.dart';
import 'package:cliente_minha_unimed/shared/api/channel-ethics.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-emergency/checkin_emercency.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/checkin-lab.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/guide.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-surgery/checkin_surgery.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/qrcode.api.dart';
import 'package:cliente_minha_unimed/shared/api/eva.api.dart';
import 'package:cliente_minha_unimed/shared/api/exams/list_exams.api.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/financeiro.api.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro/readjust-negotiation.dart';
import 'package:cliente_minha_unimed/shared/api/general-messages/general_mesages.api.dart';
import 'package:cliente_minha_unimed/shared/api/getnet.api.dart';
import 'package:cliente_minha_unimed/shared/api/graphql.api.dart';
import 'package:cliente_minha_unimed/shared/api/guia-medico-legend/guia-medico-legend.api.dart';
import 'package:cliente_minha_unimed/shared/api/insecurance-screen/insecuranse-screen-api.dart';
import 'package:cliente_minha_unimed/shared/api/notificacao/notification.api.dart';
import 'package:cliente_minha_unimed/shared/api/opcionais.api.dart';
import 'package:cliente_minha_unimed/shared/api/order-home-buttons.api.dart';
import 'package:cliente_minha_unimed/shared/api/pain-symptom.api.dart';
import 'package:cliente_minha_unimed/shared/api/pdf.api.dart';
import 'package:cliente_minha_unimed/shared/api/pending-issues/pending_issues.api.dart';
import 'package:cliente_minha_unimed/shared/api/portal.api.dart';
import 'package:cliente_minha_unimed/shared/api/profile_preview.graphql.dart';
import 'package:cliente_minha_unimed/shared/api/profile_roles.api.dart';
import 'package:cliente_minha_unimed/shared/api/protocols.graphql.dart';
import 'package:cliente_minha_unimed/shared/api/redefinir_senha.api.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/api/totem.api.dart';
import 'package:cliente_minha_unimed/shared/api/tracking-ambulance/tracking-ambulance.api.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-card.api.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-emergency-service/verification.api.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-emergency-service/virtual-emergency-service.api.dart';
import 'package:cliente_minha_unimed/shared/api/websocket.api.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/services/feature-discovery.service.dart';
import 'package:cliente_minha_unimed/shared/services/geolocation.service.dart';
import 'package:cliente_minha_unimed/shared/services/preferences.service.dart';
import 'package:cliente_minha_unimed/shared/services/privacy-policy.service.dart';
import 'package:cliente_minha_unimed/shared/services/version.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:evaluation/evaluation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:http_client/http_client.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

import 'api/general-config/general-config.api.dart';

class Locator {
  static late GetIt _i;
  static GetIt get instance => _i;

  Locator.setup() {
    _i = GetIt.I;

    Locator.instance.registerLazySingleton<FirebaseAnalytics>(
      () => FirebaseAnalytics.instance,
    );

    _i.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: FlavorConfig.instance!.values.remoteLogEnv,
          username: 'cliente-app',
          password: 'FmR6fqSfQLx8Eyea',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );
    _i.registerSingleton<Evaluation>(
      Evaluation(
          environment: FlavorConfig.instance!.values.evaluationEnv,
          username: 'cliente-app',
          password: 'FmR6fqSfQLx8Eyea',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );

    _i.registerSingleton<UnimedHttpClient>(
      UnimedHttpClient(
          defaultHeaders: {"Content-Type": "application/json"},
          logger: UnimedLogger()),
    );

    _i.registerSingleton<VersionService>(VersionService());

    _i.registerSingleton<AutorizacoesApi>(
      AutorizacoesApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerSingleton<EvaApi>(EvaApi(_i.get<UnimedHttpClient>()));

    _i.registerSingleton<GeolocationService>(GeolocationService());
    _i.registerSingleton<AuthApi>(
      AuthApi(
        _i.get<UnimedHttpClient>(),
      ),
    );
    _i.registerSingleton<GeneralConfigApi>(GeneralConfigApi());

    _i.registerSingleton(GuiaMedicoApi(_i.get<UnimedHttpClient>()));
    _i.registerLazySingleton<TeleconsultaApi>(
      () => TeleconsultaApi(
        _i.get<UnimedHttpClient>(),
        _i.get<AuthApi>(),
      ),
    );

    _i.registerLazySingleton<ConsultaApi>(
      () => ConsultaApi(
        _i.get<UnimedHttpClient>(),
        _i.get<AuthApi>(),
      ),
    );

    _i.registerSingleton<BeneficiarioApi>(BeneficiarioApi(
      httpClient: _i.get<UnimedHttpClient>(),
      authApi: _i.get<AuthApi>(),
    ));

    _i.registerSingleton<FinanceiroApi>(FinanceiroApi(
      httpClient: _i.get<UnimedHttpClient>(),
      authApi: _i.get<AuthApi>(),
    ));

    _i.registerSingleton<ReadjustNegotiationApi>(ReadjustNegotiationApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<GetNetApi>(GetNetApi(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<OrderHomeButtonApi>(
      OrderHomeButtonApi(
        _i.get<UnimedHttpClient>(),
      ),
    );

    _i.registerSingleton<PortalApi>(PortalApi(
      _i.get<UnimedHttpClient>(),
      _i.get<AuthApi>(),
    ));

    _i.registerSingleton<CadastroApi>(CadastroApi(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerLazySingleton<BannersPreferences>(() => BannersPreferences());

    _i.registerSingleton<RedefinirSenhaApi>(
      RedefinirSenhaApi(
        _i.get<UnimedHttpClient>(),
      ),
    );

    _i.registerLazySingleton<AgendamentoApi>(
        () => AgendamentoApi(_i.get<UnimedHttpClient>()));

    _i.registerLazySingleton<OpcionaisApi>(
        () => OpcionaisApi(_i.get<UnimedHttpClient>()));
    _i.registerFactory<VirtualEmergencyServiceApi>(
        () => VirtualEmergencyServiceApi(_i.get<UnimedHttpClient>()));

    _i.registerSingleton<ProfileRolesApi>(
      ProfileRolesApi(),
    );
    _i.registerLazySingleton<VirtualCardApi>(() => VirtualCardApi(
          httpClient: _i.get<UnimedHttpClient>(),
          authApi: _i.get<AuthApi>(),
        ));
    _i.registerLazySingleton<VerificationApi>(
        () => VerificationApi(_i.get<UnimedHttpClient>()));

    _i.registerSingleton<ConsultaVirtualAgendamentoApi>(
      ConsultaVirtualAgendamentoApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'ConectaSaudeAgendamentoApi'),
      ),
    );

    _i.registerSingleton<ListExamsApi>(ListExamsApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'ListExamsApi')));

    _i.registerSingleton<InsecuranceApi>(InsecuranceApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'InsecuranceApi')));

    _i.registerSingleton<PdfApi>(PdfApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'PDFApi')));

    _i.registerSingleton<AuthTokenApi>(AuthTokenApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'AuthTokenApi')));

    _i.registerSingleton<WebSocketApi>(WebSocketApi());
    _i.registerSingleton<FeatureDiscoveryService>(FeatureDiscoveryService());

    _i.registerSingleton<TrackingAmbulanceApi>(
      TrackingAmbulanceApi(
        logger: UnimedLogger(className: 'TrackingAmbulanceApi'),
        httpClient: _i.get<UnimedHttpClient>(),
      ),
    );
    _i.registerSingleton<GuideApi>(GuideApi(
      httpClient: _i.get<UnimedHttpClient>(),
      logger: UnimedLogger(className: 'GuideApi'),
    ));
    _i.registerSingleton<QrCodeApi>(QrCodeApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<CheckinSurgeryApi>(CheckinSurgeryApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'CheckinSurgeryApi')));

    _i.registerSingleton<BannerApiDart>(BannerApiDart(
      _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<PrivacyPolicyService>(PrivacyPolicyService(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<CheckinLabApi>(CheckinLabApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'CheckinLabApi')));

    _i.registerSingleton<CheckinEmergencyApi>(CheckinEmergencyApi(
        httpClient: _i.get<UnimedHttpClient>(),
        logger: UnimedLogger(className: 'CheckinEmergencyApi')));

    _i.registerLazySingleton<PainSymptomApi>(() => PainSymptomApi(
          httpClient: _i.get<UnimedHttpClient>(),
        ));
    _i.registerSingleton<ScheduleExamsApi>(
        ScheduleExamsApi(httpClient: _i.get<UnimedHttpClient>()));
    _i.registerSingleton<SolicitacoesApi>(
        SolicitacoesApi(httpClient: _i.get<UnimedHttpClient>()));

    _i.registerSingleton<NotificationApi>(
        NotificationApi(httpClient: _i.get<UnimedHttpClient>()));

    _i.registerSingleton<GeneralMessagesApi>(GeneralMessagesApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<TotemApi>(TotemApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<LegendMedicalApi>(LegendMedicalApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<ChannelEthicsApi>(ChannelEthicsApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<GlobalKey<NavigatorState>>(
        GlobalKey<NavigatorState>());

    _i.registerSingleton<ProfilePreviewApi>(ProfilePreviewApi(
      _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<ResApi>(ResApi(_i.get<UnimedHttpClient>()));
    _i.registerLazySingleton<GraphQlApi>(
      () => GraphQlApi(_i.get<UnimedHttpClient>()),
    );
    _i.registerSingleton<PendingIssuesApi>(PendingIssuesApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<AssetsApi>(AssetsApi(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<BeneficiaryProtocolApi>(BeneficiaryProtocolApi(
      _i.get<UnimedHttpClient>(),
    ));
  }

  Locator.testSetup() {
    _i = GetIt.I;

    _i.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: FlavorConfig.instance!.values.remoteLogEnv,
          username: 'cliente-app',
          password: 'FmR6fqSfQLx8Eyea',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );

    _i.registerSingleton<UnimedHttpClient>(
      UnimedHttpClient(
          defaultHeaders: {"Content-Type": "application/json"},
          logger: UnimedLogger()),
    );

    _i.registerSingleton<AuthApi>(
      AuthApi(
        _i.get<UnimedHttpClient>(),
      ),
    );

    _i.registerSingleton<BeneficiarioApi>(BeneficiarioApi(
      httpClient: _i.get<UnimedHttpClient>(),
      authApi: _i.get<AuthApi>(),
    ));

    _i.registerSingleton<LegendMedicalApi>(LegendMedicalApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<ProfilePreviewApi>(ProfilePreviewApi(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<AssetsApi>(AssetsApi(
      _i.get<UnimedHttpClient>(),
    ));
  }
}
