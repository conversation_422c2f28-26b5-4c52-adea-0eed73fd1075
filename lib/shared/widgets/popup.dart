// ignore_for_file: use_build_context_synchronously

import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AlertPopup {
  static Future<void> open(
    BuildContext context, {
    required Function onConfirm,
    Function? onClose,
    required String title,
    required String subtitle,
    String? imagePath,
  }) async {
    return showDialog(
      context: context,
      builder: ((context) => Dialog(
            insetPadding: const EdgeInsets.all(20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  //devido ao fato de nas novas expressões nao existir eva sorrindo e o fato de serem png, modifiquei para caso nao seja
                  //informado o caminho da imagem, ele utilize a imagem padrão, que possui eva sorrindo porem em sgv
                  imagePath != null
                      ? Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 60),
                          child: ClipOval(
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(
                                imagePath,
                                width: 80,
                                height: 80,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        )
                      : SvgPicture.asset('assets/svg/eva-assistentevirtual.svg',
                          width: 150),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: Text(title,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: UnimedColors.green,
                        )),
                  ),
                  Padding(
                      padding: const EdgeInsets.only(bottom: 40.0),
                      child: SingleChildScrollView(
                        child: Text(
                          subtitle,
                        ),
                      )),
                  Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10.0),
                          child: InkWell(
                            onTap: () {
                              if (onClose != null) {
                                onClose();
                              }
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              padding: const EdgeInsets.only(
                                  top: 12.0, bottom: 12.0, left: 20, right: 20),
                              decoration: const BoxDecoration(
                                  color: UnimedColors.gray,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(20),
                                  )),
                              child: Text(
                                I18nHelper.translate(context, 'resPopup.close'),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            Navigator.of(context).pop();
                            onConfirm();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 12),
                            decoration: const BoxDecoration(
                                color: UnimedColors.green,
                                borderRadius: BorderRadius.all(
                                  Radius.circular(20),
                                )),
                            child: Text(
                              I18nHelper.translate(context, 'resPopup.action'),
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              )),
            ),
          )),
    );
  }
}
