import 'package:cliente_minha_unimed/bloc/banners/banners_bloc.dart';
import 'package:cliente_minha_unimed/bloc/banners/banners_event.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/remote-config/banner-remote-config.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class UnimedBannerConfirmDialog extends StatefulWidget {
  final BannerRemoteConfig banner;

  UnimedBannerConfirmDialog({
    required this.banner,
  });

  @override
  _UnimedBannerConfirmDialogState createState() =>
      _UnimedBannerConfirmDialogState();
}

class _UnimedBannerConfirmDialogState extends State<UnimedBannerConfirmDialog> {
  bool? _valueCheckboxPopUp = false;

  @override
  Widget build(context) {
    final _alert = AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: Text(widget.banner.title),
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(widget.banner.description),
            Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  _valueCheckboxPopUp = !_valueCheckboxPopUp!;
                });
              },
              child: Row(
                children: <Widget>[
                  Checkbox(
                    value: _valueCheckboxPopUp,
                    onChanged: (bool? val) {
                      setState(() {
                        _valueCheckboxPopUp = val;
                      });
                    },
                    activeColor: UnimedColors.orange,
                  ),
                  Text("Não mostrar mais."),
                ],
              ),
            ),
            SizedBox(height: 10.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.all(10.0),
                      foregroundColor: Colors.white,
                      backgroundColor: unimedOrange,
                    ),
                    child: Text("Saiba mais"),
                    onPressed: _onPressedSaibaMais,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.all(10.0),
                      foregroundColor: Colors.white,
                      backgroundColor: unimedOrange,
                    ),
                    child: Text("Fechar"),
                    onPressed: _onPressedFechar,
                  ),
                ),
              ],
            ),
          ]),
      backgroundColor: Colors.white,
    );

    return _alert;
  }

  void _onPressedFechar() async {
    if (_valueCheckboxPopUp!) {
      BlocProvider.of<BannersBloc>(context).add(MarkBannerAsRead(widget.banner,
          BlocProvider.of<UserBloc>(context).credentials.cpfSabius));
    }

    Navigator.of(context).pop();
  }

  void _onPressedSaibaMais() async {
    if (_valueCheckboxPopUp!) {
      BlocProvider.of<BannersBloc>(context).add(MarkBannerAsRead(widget.banner,
          BlocProvider.of<UserBloc>(context).credentials.cpfSabius));
    }

    AnalyticsService().logButtonClick(
        {'type': 'link', 'value': widget.banner.linkToRedirect});

    if (await canLaunchUrl(Uri.parse(widget.banner.linkToRedirect))) {
      await launchUrl(Uri.parse(widget.banner.linkToRedirect));
      Navigator.of(context).pop();
    } else {
      logger.e('Saiba Mais erro ao abrir link ${widget.banner.linkToRedirect}');
      throw 'Could not launch ${widget.banner.linkToRedirect}';
    }
  }
}
