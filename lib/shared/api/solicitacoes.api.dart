import 'dart:convert';

import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/main.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

const LIMIT_CHARACTERS = 2000;

class SolicitacoesApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'SolicitacoesApi');

  SolicitacoesApi({required this.httpClient});

  //*// consultas com periodo de 1 ano ficarão lentas

  Future<List<GuiaModel>> carregarGuias({
    required Perfil perfil,
    DateTime? dataInicio,
    DateTime? dataFim,
  }) async {
    try {
      final _url =
          '${FlavorConfig.instance!.values.profilePermissions.url}authorization/solicitation/${perfil.carteira?.carteiraNumero}/card${dataInicio != null && dataFim != null ? '?inicio=${DateFormat('yyyyMMdd').format(dataInicio)}&fim=${DateFormat('yyyyMMdd').format(dataFim)}' : ''}';
      String bearer =
          'Bearer ${await (Locator.instance.get<AuthApi>().tokenPerfilApps())}';
      final headers = {
        'Authorization': '$bearer',
        'Content-Type': 'application/json',
      };

      var response = await this.httpClient.get(
            Uri.parse(_url),
            headers: headers,
          );
      if (response.statusCode == 200) {
        final _data = jsonDecode(response.body);

        final _collection = (_data as List)
            .map<GuiaModel>((e) => GuiaModel.fromJson(e))
            .toList();

        logger.d(
          'carregarGuias success statusCode: ${response.statusCode} - guides lenght: ${_collection.length}',
        );

        _collection.sort((a, b) {
          return DateTime.parse(b.dataReferencia!)
              .compareTo(DateTime.parse(a.dataReferencia!));
        });

        return _collection;
      } else {
        logger.e(
          'carregarGuias error statusCode: ${response.statusCode} body: ${response.body}',
        );
        throw GuideException('Erro ${response.statusCode} found');
      }
    } catch (ex) {
      logger.e('carregarGuias error exception $ex');
      throw GenericException();
    }
  }

  DateTime getDataInicio() {
    return DateTime(
      DateTime.now().year,
      DateTime.now().month - defaultTimeSearchAuthorizationInMonth,
      DateTime.now().day,
    );
  }

  DateTime getDataFim() {
    return DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
    );
  }

  Future<DetalheGuiaModel> carregarDetalheGuia({
    required Perfil perfil,
    required GuiaModel guia,
  }) async {
    try {
      String _url =
          '${FlavorConfig.instance!.values.profilePermissions.url}/authorization/solicitation/${perfil.carteira?.carteiraNumero}/card/${guia.codPrestador}/provider/${DateFormat("yyyyMMdd").format(DateTime.parse(guia.dataReferencia!))}/date';
      String bearer =
          'Bearer ${await (Locator.instance.get<AuthApi>().tokenPerfilApps())}';
      final headers = {
        'Authorization': '$bearer',
        'Content-Type': 'application/json',
      };

      var response = await this.httpClient.get(
            Uri.parse(_url),
            headers: headers,
          );
      if (response.statusCode == 200) {
        final _data = jsonDecode(response.body)['retorno'] as List;

        logger.d(
          'carregarDetalheGuia success statusCode: ${response.statusCode}',
        );

        return DetalheGuiaModel.fromJson(_data.first);
      } else {
        logger.e(
          'carregarDetalheGuia error statusCode: ${response.statusCode} body: ${response.body}',
        );
        throw GuideException('Erro ${response.statusCode} found');
      }
    } catch (ex) {
      logger.e('carregarDetalheGuia error exception $ex');
      throw GenericException();
    }
  }

  Future<List<DetalheTimeline>> carregarDetalheTimeline({
    required GuiaModel guia,
  }) async {
    try {
      String _url = "";

      if (guia.codPreSolic != null) {
        _url =
            '${FlavorConfig.instance!.values.profilePermissions.url}authorization/solicitation/${guia.codPreSolic}/attendance';
      } else if (guia.numAtendimento != null) {
        _url =
            '${FlavorConfig.instance!.values.profilePermissions.url}authorization/solicitation/sabius/${guia.numAtendimento}/attendance';
      }
      String bearer =
          'Bearer ${await (Locator.instance.get<AuthApi>().tokenPerfilApps())}';
      final headers = {
        'Authorization': '$bearer',
        'Content-Type': 'application/json',
      };

      var response = await this.httpClient.get(
            Uri.parse(_url),
            headers: headers,
          );
      if (response.statusCode == 200) {
        final _data = jsonDecode(response.body);

        logger.d(
          'carregarDetalheTimeline success statusCode: ${response.statusCode}',
        );

        final collection = (_data as List)
            .map<DetalheTimeline>((e) => DetalheTimeline.fromJson(e))
            .toList();
        // removido pois esta ordenado no serviço, diel confirmou

        // collection.sort((a, b) {
        //   return a.dataReferencia!.compareTo(b.dataReferencia!);
        // });

        return collection;
      } else {
        logger.e(
          'carregarDetalheTimeline error statusCode: ${response.statusCode} body: ${response.body}',
        );
        throw GuideException('Erro ${response.statusCode} found');
      }
    } catch (ex) {
      logger.e('carregarDetalheTimeline error exception $ex');
      throw GenericException();
    }
  }

  Future<RetornoSolicitacaoModel> carregarSolicitacao({
    required String numProtocolo,
  }) async {
    try {
      final _url =
          '${FlavorConfig.instance!.values.profilePermissions.url}authorization/solicitation/$numProtocolo/protocol';
      final bearer =
          'Bearer ${await (Locator.instance.get<AuthApi>().tokenPerfilApps())}';

      var response = await this.httpClient.get(
        Uri.parse(_url),
        headers: {
          'Authorization': '$bearer',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        logger.d(
          'carregarSolicitacao success statusCode: ${response.statusCode} - body: ${response.body}',
        );

        final _data = jsonDecode(response.body);

        //feito set manual do numero do protocolo pois jsonDecode possui um issue relacionado a conversao de numero grandes para o dart
        _data['numProtocolo'] = numProtocolo;

        return RetornoSolicitacaoModel.fromJson(_data);
      } else {
        logger.e(
          'carregarSolicitacao error statusCode: ${response.statusCode} - body: ${response.body}',
        );
        throw SolicitacaoException('Erro ${response.statusCode} found');
      }
    } catch (ex) {
      logger.e('carregarSolicitacao error exception $ex');
      throw GenericException();
    }
  }

  Future<List<String>?> getGuidesPdf({
    required String numProtocolo,
    required int numAtend,
  }) async {
    try {
      final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
      final _url =
          '${FlavorConfig.instance!.values.profilePermissions.url}authorization/guides/protocol/$numProtocolo/atendimento/$numAtend';
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      var response = await this.httpClient.get(
            Uri.parse(_url),
            headers: headers,
          );
      if (response.statusCode == 200) {
        logger.d(
          'getGuidesPdf success statusCode: ${response.statusCode} - body: ${response.body.toString().characters.take(LIMIT_CHARACTERS)}}',
        );
        final List<String> list = [];
        for (final item in jsonDecode(response.body)['retorno']) {
          list.add(item);
        }
        return list;
      } else if (response.statusCode == 500) {
        final data = jsonDecode(response.body);
        logger.e('getGuidesPdf error ${response.statusCode} body: $data');
        final message = data['response'] != null
            ? data['message']
            : MessageException.GENERIC_NO_DATA;

        throw GuideException(message);
      } else {
        logger.e(
          'getGuidesPdf error statusCode: ${response.statusCode} - body: ${response.body}',
        );
        throw SolicitacaoException('Erro ${response.statusCode} found');
      }
    } on GuideException {
      rethrow;
    } catch (ex) {
      logger.e('getGuidesPdf error exception $ex');
      throw GenericException();
    }
  }
}
