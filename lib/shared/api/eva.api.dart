import 'dart:convert';

import 'package:cliente_minha_unimed/models/medical-guide/medical-guide-search.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/main.dart';
import 'package:cliente_minha_unimed/screens/financeiro/main.dart';
import 'package:cliente_minha_unimed/screens/medical-guide/main.dart';
import 'package:cliente_minha_unimed/screens/meu-plano/main.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/widgets.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';

class EvaApi {
  final _url = 'https://assistentevirtual.unimedfortaleza.com.br/api/message';
  final logger = UnimedLogger(className: 'RobertaApi');
  final UnimedHttpClient httpClient;
  dynamic _context;

  EvaApi(this.httpClient) {
    _context = null;
  }

  Future<String?> getSinonimo(String texto) async {
    String? returnTexto = texto;

    final postData = {
      "input": {"text": texto},
      "context": {"device": "APPCLIENTE"},
      "chatId": "1fb26117-1b57-44a4-8d3a-5cf3c3f04bc0", // gerado pela uuid
    };

    final headers = {'Content-Type': 'application/json'};

    logger.d('getSinonimo _url     => $_url');
    logger.d('getSinonimo postData => $postData');
    logger.d('getSinonimo headers  => $headers');

    final response = await this
        .httpClient
        .post(
          Uri.parse(_url),
          body: jsonEncode(postData),
          headers: headers,
        )
        .timeout(Duration(seconds: 30));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      // logger.d('getSinomino data: $data');

      if (data != null &&
          data['intents'] != null &&
          data['intents'].length > 0) {
        logger.d("intents ${data['intents']}");
        final isMedicalGuide = (data['intents'] as List).firstWhere(
            (i) => i['intent'] == 'guia_medico',
            orElse: () => null);

        logger.d('getSinomino isMedicalGuide: $isMedicalGuide');
        if (isMedicalGuide != null) {
          final hasSpeciality = (data['entities'] as List).firstWhere(
              (i) => i['entity'] == 'especialidade',
              orElse: () => null);

          logger.d('getSinomino hasSpeciality: $hasSpeciality');
          if (hasSpeciality != null) {
            returnTexto = hasSpeciality['value'];
          }
        }
      }

      return returnTexto;
    } else {
      logger.e("getSinonimo $response");
      throw UnimedException(response.body);
    }
  }

  /// Return response in markdown
  Future<ResponseEva?> sendMessage({required String inputText}) async {
    if (_context == null) {
      _context = {"device": "APPCLIENTE"};
    }

    final _body = {
      "input": {"text": inputText.trim()},
      "context": _context
    };

    String jsonBody = JsonEncoder.withIndent(' ').convert(_body);
    logger.d('sendMessage _body $jsonBody');

    final response = await this.httpClient.post(
      Uri.parse(_url),
      body: jsonEncode(_body),
      headers: {"Content-Type": "application/json"},
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      logger.d(response.body);

      String jsonData = JsonEncoder.withIndent(' ').convert(data);
      logger.d('sendMessage response data $jsonData');

      _context = data['context'];

      List<String?> intents = [];
      List<String?> entities = [];
      String _message = '';

      if (data['output']['text'] is String) {
        _message = (Bidi.stripHtmlIfNeeded(data['output']['text']))
            .replaceAll("  ", "\n\n")
            .trim();
      } else {
        _message = (Bidi.stripHtmlIfNeeded(data['output']['text'].join('\n')))
            .replaceAll("  ", "\n\n")
            .trim();
      }

      String _originalMessage = '';
      EvaRedirect _redirectTo = EvaRedirect.NAO;

      if (data['intents'] != null) {
        (data['intents'] as List).forEach((i) {
          intents.add(i['intent']);
        });
      }

      if (data['entities'] != null) {
        (data['entities'] as List).forEach((i) {
          entities.add(i['entity']);
        });
      }

      // Verificando nos Intents fixos e tratando se for guia médico
      Intents.maps.map((index, value) {
        if (intents.indexOf(value.key) >= 0) {
          _originalMessage = _message;
          _message = (index == EvaRedirect.GUIA_MEDICO)
              ? '${value.message} $inputText...'
              : value.message;
          _redirectTo = index;
        }
        return MapEntry(index, value);
      });

      // Verificando se existe intents pronto_atendimento, para redirecionar para o PA
      if (intents.contains('pronto_atendimento')) {
        (data['entities'] as List).forEach((i) {
          if (i["entity"] == "acao" && i["value"] == "atendimento") {
            final intent = Intents.maps[EvaRedirect.PA]!;
            _originalMessage = _message;
            _message = intent.message;
            _redirectTo = EvaRedirect.PA;
          }
        });
      }

      // Verificando se existe intents aplicativos, para redirecionar pro cartão
      // virtual
      if (intents.contains('aplicativos')) {
        (data['entities'] as List).forEach((i) {
          if (i["entity"] == "acao" && i["value"] == "Cartão Virtual") {
            final intent = Intents.maps[EvaRedirect.CARTAO_VIRTUAL]!;
            _originalMessage = _message;
            _message = intent.message;
            _redirectTo = EvaRedirect.CARTAO_VIRTUAL;
          }
        });
      }

      // Se o intents for vazio e entity prestador_fisica, redirecionar para
      // guia médico, pois trata-se de um prestador
      if (intents.length <= 0 && entities.indexOf('prestador_fisica') >= 0) {
        final intentPrestadorFisica = Intents.maps[EvaRedirect.GUIA_MEDICO]!;

        _originalMessage = _message;
        _message = intentPrestadorFisica.message;
        _redirectTo = EvaRedirect.GUIA_MEDICO;
      }

      return ResponseEva(
        message: _message,
        input: inputText,
        originalMessage: _originalMessage,
        intents: intents,
        entities: entities,
        redirectTo: _redirectTo,
      );
    } else {
      logger.e(
          'sendMessage error statusCode: (${response.statusCode}) ${response.reasonPhrase} => ${response.body}');
      throw UnimedException('Erro ${response.statusCode} found');
    }
  }

  Future<void> clearContext() async {
    _context = null;
  }
}

class ResponseEva {
  final String input;
  final String message;
  final String? originalMessage;
  final List<String?>? intents;
  final List<String?>? entities;
  final EvaRedirect? redirectTo;

  ResponseEva({
    required this.input,
    required this.message,
    this.intents,
    this.entities,
    this.originalMessage,
    this.redirectTo,
  });

  Widget get screenToRedirect {
    switch (this.redirectTo) {
      case EvaRedirect.GUIA_MEDICO:
        return MedicalGuideScreen(
          searchParams: SearchMedicalGuideModel(
            provider: input,
          ),
          isFavoriteScreen: false,
        );
      case EvaRedirect.AUTORIZACOES:
        return AutorizacoesScreen();
      case EvaRedirect.FATURAS:
        return FinanceiroScreen();
      case EvaRedirect.IMPOSTO_RENDA:
        return FinanceiroScreen();
      // case RobertaRedirect.PLANOS:
      //   return MeuPlanoScreen();
      //   break;
      case EvaRedirect.COOPARTICIPACAO:
        return MeuPlanoScreen();
      default:
        return Container();
    }
  }

  @override
  String toString() {
    return message;
  }
}

enum EvaRedirect {
  NAO,
  GUIA_MEDICO,
  PRESTADOR_FISICA,
  FATURAS,
  AUTORIZACOES,
  CARTAO_VIRTUAL,
  PA,
  // PLANOS,
  COOPARTICIPACAO,
  IMPOSTO_RENDA
}

class Intents {
  final String key;
  final String message;

  Intents(this.key, this.message);

  static Map<EvaRedirect, Intents> get maps => {
        EvaRedirect.PA: Intents(
          'pronto_atendimento',
          'Estou te redirecionando para o PA',
        ),
        EvaRedirect.GUIA_MEDICO: Intents(
          'guia_medico',
          'Estou te redirecionando para o guia médico com os resultados da busca por',
        ),
        EvaRedirect.PRESTADOR_FISICA: Intents(
          'prestador_fisica',
          'Estou te redirecionando para o guia médico com os resultados da busca por',
        ),
        EvaRedirect.AUTORIZACOES: Intents(
          'autorizacao',
          'Estou te redirecionando para a autorização.',
        ),
        EvaRedirect.FATURAS: Intents(
          'faturas',
          'Estou te redirecionando para a faturas.',
        ),
        EvaRedirect.CARTAO_VIRTUAL: Intents(
          'cartao_virtual',
          'Estou te redirecionando para o aplicativo do cartão virtual.',
        ),
        // RobertaRedirect.PLANOS: Intents(
        //   'planos',
        //   'Estou te redirecionando para a planos.',
        // ),
        EvaRedirect.COOPARTICIPACAO: Intents(
          'demonstrativo_coparticipacao',
          'Estou te redirecionando para a geração do demonstrativo de Cooparticipação',
        ),
        EvaRedirect.IMPOSTO_RENDA: Intents(
          'imposto_de_renda',
          'Estou te redirecionando para o imposto de renda.',
        )
      };
}
