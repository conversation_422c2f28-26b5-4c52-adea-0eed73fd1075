import 'package:cliente_minha_unimed/models/general-config/general-config.model.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class InsecuranceApi {
  final UnimedLogger logger;
  final UnimedHttpClient httpClient;
  InsecuranceApi({
    required this.logger,
    required this.httpClient,
  });

  Future<String?> getInsecuranceUrl() async {
    try {
      final _insecuranseURL = '${Locator.instance.get<GeneralConfigModel>().url.plataformaSeguros}';

      return _insecuranseURL;
    } on ServiceTimeoutException catch (e) {
      logger.e('InsecuranceApi ServiceTimeoutException $e');
      throw e;
    } on Exception catch (e) {
      logger.e('InsecuranceApi exception $e');
      throw GenericException();
    }
  }
}
