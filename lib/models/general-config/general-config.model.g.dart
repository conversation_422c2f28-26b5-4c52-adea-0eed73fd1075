// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general-config.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeneralConfigModel _$GeneralConfigModelFromJson(Map json) => GeneralConfigModel(
      url: Urls.fromJson(json['url'] as Map),
      buttons: Buttons.fromJson(json['buttons'] as Map),
      users: Users.fromJson(json['users'] as Map),
      cache: Cache.fromJson(json['cache'] as Map),
      pendingIssues: json['pendingIssues'] == null
          ? null
          : PendingIssues.fromJson(json['pendingIssues'] as Map),
    );

Map<String, dynamic> _$GeneralConfigModelToJson(GeneralConfigModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'buttons': instance.buttons,
      'users': instance.users,
      'cache': instance.cache,
      'pendingIssues': instance.pendingIssues,
    };

Urls _$UrlsFromJson(Map json) => Urls(
      plataformaSeguros: json['plataformaSeguros'] as String,
      socketApps: json['socketApps'] as String?,
      apiAuth: json['apiAuth'] as String?,
      integraMv: json['integraMv'] as String?,
      file1mb: json['file1mb'] as String?,
      jitsi: json['jitsi'] as String?,
    );

Map<String, dynamic> _$UrlsToJson(Urls instance) => <String, dynamic>{
      'socketApps': instance.socketApps,
      'apiAuth': instance.apiAuth,
      'integraMv': instance.integraMv,
      'file1mb': instance.file1mb,
      'jitsi': instance.jitsi,
      'plataformaSeguros': instance.plataformaSeguros,
    };

Buttons _$ButtonsFromJson(Map json) => Buttons(
      homeOpen: json['homeOpen'] as bool? ?? false,
      useNewUpdateContact: json['useNewUpdateContact'] as bool? ?? false,
      useNewGetReport: json['useNewGetReport'] as bool? ?? false,
      reportOnDemand: json['reportOnDemand'] as bool? ?? false,
      sensitiveDataButton: json['sensitiveDataButton'] as bool? ?? false,
      profilePreviewPermission:
          json['profilePreviewPermission'] as bool? ?? false,
      enablePixPayment: json['enablePixPayment'] as bool? ?? false,
      authorizationsV2: json['authorizationsV2'] as bool? ?? false,
    );

Map<String, dynamic> _$ButtonsToJson(Buttons instance) => <String, dynamic>{
      'homeOpen': instance.homeOpen,
      'useNewUpdateContact': instance.useNewUpdateContact,
      'useNewGetReport': instance.useNewGetReport,
      'reportOnDemand': instance.reportOnDemand,
      'sensitiveDataButton': instance.sensitiveDataButton,
      'profilePreviewPermission': instance.profilePreviewPermission,
      'ebablePixPayment': instance.enablePixPayment,
      'authorizationsV2': instance.authorizationsV2,
    };

Users _$UsersFromJson(Map json) => Users(
      apiAuth: ApiAuth.fromJson(json['apiAuth'] as Map),
    );

Map<String, dynamic> _$UsersToJson(Users instance) => <String, dynamic>{
      'apiAuth': instance.apiAuth,
    };

ApiAuth _$ApiAuthFromJson(Map json) => ApiAuth(
      user: json['user'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$ApiAuthToJson(ApiAuth instance) => <String, dynamic>{
      'user': instance.user,
      'password': instance.password,
    };

Cache _$CacheFromJson(Map json) => Cache(
      timeExpirationVirtualCard:
          (json['timeExpirationVirtualCard'] as num).toInt(),
    );

Map<String, dynamic> _$CacheToJson(Cache instance) => <String, dynamic>{
      'timeExpirationVirtualCard': instance.timeExpirationVirtualCard,
    };

PendingIssues _$PendingIssuesFromJson(Map json) => PendingIssues(
      enablePendingIssues: json['enablePendingIssues'] as bool,
      dueInDays: (json['dueInDays'] as num).toInt(),
    );

Map<String, dynamic> _$PendingIssuesToJson(PendingIssues instance) =>
    <String, dynamic>{
      'enablePendingIssues': instance.enablePendingIssues,
      'dueInDays': instance.dueInDays,
    };
