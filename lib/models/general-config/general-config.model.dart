import 'package:json_annotation/json_annotation.dart';

part 'general-config.model.g.dart';

@JsonSerializable(anyMap: true)
class GeneralConfigModel {
  Urls url;
  Buttons buttons;
  Users users;
  Cache cache;
  PendingIssues? pendingIssues;

  GeneralConfigModel({
    required this.url,
    required this.buttons,
    required this.users,
    required this.cache,
    this.pendingIssues,
  });

  factory GeneralConfigModel.fromJson(Map json) =>
      _$GeneralConfigModelFromJson(json);
  Map<String, dynamic> toJson() => _$GeneralConfigModelToJson(this);
}

@JsonSerializable(anyMap: true)
class Urls {
  const Urls({
    this.socketApps,
    this.apiAuth,
    this.integraMv,
    this.file1mb,
    this.jitsi,
    this.plataformaSeguros,
  });
  final String? socketApps;
  final String? apiAuth;
  final String? integraMv;
  final String? file1mb;
  final String? jitsi;
  final String? plataformaSeguros;

  factory Urls.fromJson(Map json) => _$Urls<PERSON>romJson(json);
  Map<String, dynamic> toJson() => _$UrlsToJson(this);
}

@JsonSerializable(anyMap: true)
class Buttons {
  const Buttons(
      {this.homeOpen = false,
      this.useNewUpdateContact = false,
      this.useNewGetReport = false,
      this.reportOnDemand = false,
      this.sensitiveDataButton = false,
      this.profilePreviewPermission = false,
      this.enablePixPayment = false,
      this.authorizationsV2 = false});
  final bool homeOpen;
  final bool useNewUpdateContact;
  final bool useNewGetReport;
  final bool reportOnDemand;
  final bool sensitiveDataButton;
  final bool profilePreviewPermission;
  final bool enablePixPayment;
  final bool authorizationsV2;

  factory Buttons.fromJson(Map json) => _$ButtonsFromJson(json);
  Map<String, dynamic> toJson() => _$ButtonsToJson(this);
}

@JsonSerializable(anyMap: true)
class Users {
  ApiAuth apiAuth;

  Users({required this.apiAuth});

  factory Users.fromJson(Map json) => _$UsersFromJson(json);
  Map<String, dynamic> toJson() => _$UsersToJson(this);
}

@JsonSerializable(anyMap: true)
class ApiAuth {
  String user;
  String password;

  ApiAuth({required this.user, required this.password});

  factory ApiAuth.fromJson(Map json) => _$ApiAuthFromJson(json);
  Map<String, dynamic> toJson() => _$ApiAuthToJson(this);
}

@JsonSerializable(anyMap: true)
class Cache {
  int timeExpirationVirtualCard;

  Cache({required this.timeExpirationVirtualCard});

  factory Cache.fromJson(Map json) => _$CacheFromJson(json);
  Map<String, dynamic> toJson() => _$CacheToJson(this);
}

@JsonSerializable(anyMap: true)
class PendingIssues {
  bool enablePendingIssues;
  int dueInDays;

  PendingIssues({
    required this.enablePendingIssues,
    required this.dueInDays,
  });

  factory PendingIssues.fromJson(Map json) => _$PendingIssuesFromJson(json);
  Map<String, dynamic> toJson() => _$PendingIssuesToJson(this);
}
