import 'package:intl/intl.dart';

class BeneficiaryProtocolModel {
  String? requesterName;
  late String status;
  late String protocolType;
  late AnsProtocolNumberHistory ansProtocolNumberHistory;

  BeneficiaryProtocolModel(
      {this.requesterName,
      required this.status,
      required this.protocolType,
      required this.ansProtocolNumberHistory});

  String get dataHoraFormat => DateFormat("dd/MM/yyyy - HH:mm")
      .format(DateTime.parse(this.ansProtocolNumberHistory.protocolDate));

  BeneficiaryProtocolModel.fromJson(Map<String, dynamic> json) {
    requesterName = json['requesterName'];
    status = json['status'];
    protocolType = json['protocolType'];
    ansProtocolNumberHistory =
        new AnsProtocolNumberHistory.fromJson(json['ansProtocolNumberHistory']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['requesterName'] = this.requesterName;
    data['status'] = this.status;
    data['protocolType'] = this.protocolType;
    data['ansProtocolNumberHistory'] = this.ansProtocolNumberHistory.toJson();

    return data;
  }
}

class AnsProtocolNumberHistory {
  late String ansProtocolNumber;
  late String protocolDate;
  int? transactionNumber;
  AnsProtocolOrigin? ansProtocolOrigin;

  AnsProtocolNumberHistory(
      {required this.ansProtocolNumber,
      required this.protocolDate,
      this.transactionNumber,
      this.ansProtocolOrigin});

  AnsProtocolNumberHistory.fromJson(Map<String, dynamic> json) {
    ansProtocolNumber = json['ansProtocolNumber'];
    protocolDate = json['protocolDate'];
    transactionNumber = json['transactionNumber'];
    ansProtocolOrigin = json['ansProtocolOrigin'] != null
        ? new AnsProtocolOrigin.fromJson(json['ansProtocolOrigin'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ansProtocolNumber'] = this.ansProtocolNumber;
    data['protocolDate'] = this.protocolDate;
    data['transactionNumber'] = this.transactionNumber;
    if (this.ansProtocolOrigin != null) {
      data['ansProtocolOrigin'] = this.ansProtocolOrigin!.toJson();
    }
    return data;
  }
}

class AnsProtocolOrigin {
  String? originCode;

  AnsProtocolOrigin({this.originCode});

  AnsProtocolOrigin.fromJson(Map<String, dynamic> json) {
    originCode = json['originCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['originCode'] = this.originCode;
    return data;
  }
}
