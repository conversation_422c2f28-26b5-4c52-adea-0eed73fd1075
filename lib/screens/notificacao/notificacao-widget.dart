import 'dart:io';

import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_state.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_state.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation__event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_bloc.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_state.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_event.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/notificacao.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/screens/agendamento/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/main.dart';
import 'package:cliente_minha_unimed/screens/meu-plano/main.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/scale.transition.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/size.transition.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-pa-enter-room.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert_confirm.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NotificacaoControleWidget extends StatefulWidget {
  final Widget child;
  final Alignment? alignment;

  NotificacaoControleWidget({required this.child, this.alignment, Key? key});

  @override
  _NotificacaoControleState createState() => _NotificacaoControleState();
}

class _NotificacaoControleState extends State<NotificacaoControleWidget> {
  @override
  void initState() {
    super.initState();
  }

  void _close(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        widget.child,
        BlocBuilder<NotificacaoBloc, NotificacaoState>(
          buildWhen: (previousState, state) {
            if (state is NotificacaoLembreteSalaState) {
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) => UnimedAlertDialog(
                  textWidget: Text(
                    state.notificacao!.body!,
                    textAlign: TextAlign.center,
                  ),
                  onPressed: () {
                    _close(context);
                  },
                ),
              );
            } else if (state is NotificacaoLembreteState) {
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) => UnimedAlertDialog(
                  textWidget: Text(
                    "${state.notificacao!.title} \n ${state.notificacao!.body}",
                    textAlign: TextAlign.center,
                  ),
                  onPressed: () {
                    _close(context);
                  },
                ),
              );
            } else if (state is NotificacaoConfirmacaoState) {
              bool openCancelDialog = false;
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) => UnimedConfirmDialog(
                  textWidget: Text(state.notificacao!.title!),
                  textDescription: Text(state.notificacao!.body!),
                  textButton: "Confirmar consulta",
                  colorButton: UnimedColors.green,
                  onClose: () {
                    openCancelDialog = true;
                    _close(context);
                  },
                  onPressed: () {
                    Navigator.of(context).pop();
                    BlocProvider.of<ConfirmarAgendamentoBloc>(context).add(
                      ConfirmarConsultaAgendamentoEvent(
                          protocolo:
                              state.notificacao!.additionalData!['protocolo'],
                          perfil: BlocProvider.of<PerfilBloc>(context).perfil),
                    );
                  },
                ),
              ).then((value) {
                if (openCancelDialog) {
                  _showDialogCancelar(
                    context,
                    state.notificacao!.additionalData!['protocolo'],
                    state.notificacao!.additionalData!['tipoFinalizacao'],
                  );
                }
              });
            } else if (state is NotificacaoPendenciaResolvidaState) {
              showDialog(
                  barrierDismissible: false,
                  context: context,
                  builder: (BuildContext context) => UnimedConfirmDialog(
                        textWidget: Text(state.notificacao!.title!),
                        textDescription: Text(state.notificacao!.body!),
                        textButton: "Ir para consultas",
                        colorButton: UnimedColors.green,
                        onClose: () {
                          _close(context);
                        },
                        onPressed: () {
                          _close(context);

                          final _perfil =
                              BlocProvider.of<PerfilBloc>(context).perfil;
                          BlocProvider.of<AgendamentoBloc>(context)
                              .add(ListarEvent(perfil: _perfil));

                          Navigator.push(
                              context, SizeRoute(page: AgendamentoScreen()));
                        },
                      ));
            } else if (state is NotificacaoCoparticipacaoState) {
              final notification = AdditionalNotificacao.fromJson(
                  state.notificacao!.additionalData!);
              debugPrint("======== ${notification.usuarioLogin}");
              final Iterable<Perfil> perfil = BlocProvider.of<UserBloc>(context)
                  .user
                  .perfis
                  .where((element) =>
                      element.contratoBeneficiario.beneficiario!.cpf
                          .toString() ==
                      notification.usuarioLogin);
              BlocProvider.of<PerfilBloc>(context).add(
                ChangePerfil(
                  carteira: perfil.first.contratoBeneficiario.carteira,
                  user: BlocProvider.of<UserBloc>(context).user,
                ),
              );
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) => UnimedAlertDialog(
                  textWidget: Text(
                    "${state.notificacao!.title} \n ${state.notificacao!.body}",
                    textAlign: TextAlign.center,
                  ),
                  onPressed: () {
                    _close(context);
                    Navigator.push(
                        context,
                        SizeRoute(
                            page: MeuPlanoScreen(
                          isPushNotification: true,
                        )));
                  },
                ),
              );
            } else if (state is NotificacaoAutorizacaoSucessState) {
              BlocProvider.of<AcceptedSolicBloc>(context).add(CleanRequest());
              BlocProvider.of<ProviderSolicitationBloc>(context).add(
                  SelectProviderSolicitationEvent(providerSolicitation: null));

              // BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
              //     .add(RemoveAllAttachedFiles());
//TODO: ajustar notificacao no android

              if (!Platform.isAndroid)
                showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (BuildContext context) => UnimedConfirmDialog(
                          textWidget: Text(state.notificacao!.title!),
                          textDescription: Text(state.notificacao!.body!),
                          textButton: "Ir para solicitações",
                          colorButton: UnimedColors.green,
                          onClose: () {
                            Navigator.of(context).pop();
                          },
                          onPressed: () {
                            Navigator.of(context).pop();

                            Navigator.push(
                                context, SizeRoute(page: AutorizacoesScreen()));
                          },
                        ));
            } else if (state is NotificacaoAutorizacaoFailedState) {
              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) => UnimedConfirmDialog(
                  textWidget: Text(state.notificacao!.title!),
                  textDescription: Text(state.notificacao!.body!),
                  textButton: "Tente novamente",
                  colorButton: UnimedColors.green,
                  onClose: () {
                    Navigator.of(context).pop();
                  },
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.push(
                        context,
                        ScaleRoute(
                            page: BlocProvider(
                          create: (context) => VerifySolicBloc(),
                          child: NovaSolicitacaoScreen(
                            fromAlert: true,
                          ),
                        )));
                  },
                ),
              );
            } else if (state is VESNotificationState) {
              AlertPAEnterRoom.open(
                context,
                notification: state.notification,
                title: state.notification!.title,
                text: state.notification!.body!,
              );
            } else {
              // TODO se for necessário, colocar um tratamento para as outras notificações, se vai abrir alguma tela ou algo parecido
            }

            return true;
          },
          builder: (context, state) {
            if (state is NotificacaoConfirmacaoState) {
              return MultiBlocListener(
                listeners: [
                  BlocListener<ConfirmarAgendamentoBloc,
                      ConfirmarAgendamentoState>(
                    listener: (context, state) {
                      if (state is ConfirmarAgendamentoErrorState) {
                        // dialog error confirmar
                        _showDialogResponse(state.message);
                      } else if (state is ConfirmarAgendamentoDoneState) {
                        // dialog confirmar
                        _showDialogResponse("Consulta confirmada com sucesso!");
                      }
                    },
                  ),
                  BlocListener<CancelarAgendamentoBloc,
                      CancelarAgendamentoState>(
                    listener: (context, state) {
                      if (state is CancelarAgendamentoErrorState) {
                        // dialog cancelar agendamento
                        _showDialogResponse(state.message);
                      } else if (state is CancelarAgendamentoDoneState) {
                        // dialog confirmacao de encerramento
                        _showDialogResponse("Consulta cancelada com sucesso!");
                      }
                    },
                  ),
                ],
                child: Container(),
              );
            }

            return Container();
          },
        )
      ],
    );
  }

  void _showDialogCancelar(context, protocolo, tipoFinalizacao) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => UnimedConfirmDialog(
        textWidget: Text("Atenção! Cancelamento de consulta"),
        textDescription: Text(
            "Você realmente deseja cancelar sua consulta? Protocolo: $protocolo"),
        textButton: "Cancelar consulta",
        colorButton: Colors.red,
        onClose: () {
          Navigator.of(context).pop();
        },
        onPressed: () {
          Navigator.of(context).pop();
          BlocProvider.of<CancelarAgendamentoBloc>(context).add(
            FinalizarAgendamentoComTipoEvent(
                observacoes: "",
                tipoFinalizacao: tipoFinalizacao,
                protocolo: protocolo,
                perfil: BlocProvider.of<PerfilBloc>(context).perfil),
          );
        },
      ),
    );
  }

  void _showDialogResponse(
    String? texto,
  ) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => UnimedAlertDialog(
        textWidget: Text(
          texto!,
          textAlign: TextAlign.center,
        ),
        onPressed: () {
          Navigator.of(context).pop();
          final _perfil = BlocProvider.of<PerfilBloc>(context).perfil;

          BlocProvider.of<AgendamentoBloc>(context)
              .add(ListarEvent(perfil: _perfil));
        },
      ),
    );
  }
}
