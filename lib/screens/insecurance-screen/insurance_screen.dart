import 'package:cliente_minha_unimed/bloc/insecurance-screen/insecurance-bloc.dart';
import 'package:cliente_minha_unimed/bloc/insecurance-screen/insurance_event.dart';
import 'package:cliente_minha_unimed/bloc/insecurance-screen/insurance_state.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class InsuranceScreen extends StatefulWidget {
  const InsuranceScreen({super.key});

  @override
  State<InsuranceScreen> createState() => _InsuranceScreenState();
}

class _InsuranceScreenState extends State<InsuranceScreen> {
  @override
  void initState() {
    super.initState();
    AnalyticsService().addLogScreenView(
      screenName: 'InsuranceScreen',
      screenClass: 'InsuranceScreen',
    );
  }

  Future<void> _launchURL(BuildContext context, String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Navigator.popUntil(
          context, (route) => route.isFirst); // Volta para a tela inicial
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Não foi possível abrir a URL: $url')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => InsuranceBloc()..add(LoadInsuranceUrlEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Seguros'),
        ),
        body: BlocBuilder<InsuranceBloc, InsuranceState>(
          builder: (context, state) {
            if (state is InsuranceLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is InsuranceLoaded) {
              _launchURL(context, state.url);
              return const Center(child: CircularProgressIndicator());
            } else if (state is InsuranceError) {
              return Center(
                  child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: EvaTriste(
                    message: Text(
                  state.message,
                  textAlign: TextAlign.center,
                )),
              ));
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
