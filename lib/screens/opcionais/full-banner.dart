import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_state.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_event.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/screens/opcionais/generic-banner.dart';

class FullBannerOpcionais extends StatefulWidget {
  final Widget child;
  final bool amIHome;

  FullBannerOpcionais({required this.child, required this.amIHome});

  @override
  _FullBannerOpcionalState createState() => _FullBannerOpcionalState();
}

class _FullBannerOpcionalState extends State<FullBannerOpcionais> {
  @override
  Widget build(BuildContext context) {
    return Container(
        child: Stack(
      children: <Widget>[
        widget.child,
        _loadFullBanner(),
      ],
    ));
  }

  Widget _loadFullBanner() {
    return BlocListener<OpcionalBloc, OpcionalState>(
      listenWhen: (oldState, newState) {
        return newState is MostrarSaibaMaisBannerState ||
            newState is CarregarSaibaMaisBannerSuccessState;
      },
      listener: (context, state) {
        if (state is MostrarSaibaMaisBannerState) {
          logger.d('oppening banner => ${state.opcional}');
          showDialog(
              barrierDismissible: true,
              context: context,
              builder: (context) {
                final mediaQuery = MediaQuery.of(context);

                final statusBarHeight = mediaQuery.padding.top;
                final appBarHeight = AppBar().preferredSize.height;

                final topPadding = (appBarHeight + statusBarHeight) / 2;

                return Padding(
                  padding: EdgeInsets.only(top: topPadding, left: 15),
                  child: Center(
                    child: BannerCustom(
                      opcionalVO: state.opcional,
                      amIHome: false,
                    ),
                  ),
                );
              });
        } else if (state is CarregarSaibaMaisBannerSuccessState) {
          logger.d(
              'Carregamento do banner concluído com sucesso, mostrando banner...');
          // Dispara o evento para mostrar o banner após carregar com sucesso
          BlocProvider.of<OpcionalBloc>(context)
              .add(MostrarSaibaMaisBannerEvent(opcional: state.opcional));
        }
      },
      child: Container(),
    );
  }
}
