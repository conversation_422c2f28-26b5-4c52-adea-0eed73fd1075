import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/screens/opcionais/contratar_opcional.dart';
import 'package:cliente_minha_unimed/shared/api/vo/opcionais.vo.dart';
import 'package:cliente_minha_unimed/shared/widgets/letter_animation.dart';

class BannerCustom extends StatefulWidget {
  final OpcionalVO? opcionalVO;
  final bool? amIHome;
  BannerCustom({this.opcionalVO, this.amIHome});
  @override
  _BannerCustomState createState() => _BannerCustomState();
}

class _BannerCustomState extends State<BannerCustom>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
        duration: const Duration(milliseconds: 10), vsync: this);

    Future.delayed(Duration(milliseconds: 10), () {
      _startAnimation();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _startAnimation() async {
    try {
      await _controller.forward().orCancel;
    } on TickerCanceled {
      // the animation got canceled, probably because we were disposed
    }
  }

  Future<void> _closeAnimation(Function closeCallback) async {
    try {
      await _controller.reverse().orCancel.then((value) {
        closeCallback();
      });
    } on TickerCanceled {
      // the animation got canceled, probably because we were disposed
    }
  }

  @override
  Widget build(BuildContext context) {
    return LetterAnimation(
        screenSize: MediaQuery.of(context).size,
        controller: _controller.view,
        banner: _contentBanner(widget.opcionalVO!, () {
          _closeAnimation(() {
            logger.d('closing banner => ${widget.opcionalVO}');
            if (widget.amIHome!) {
              BlocProvider.of<OpcionalBloc>(context).add(FecharFullBannerEvent(
                  perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                  opcional: widget.opcionalVO));
            } else {
              Navigator.of(context).pop();
              BlocProvider.of<OpcionalBloc>(context)
                  .add(FecharSaibaMaisBannerEvent());
            }
          });
        }));
  }

  Widget _contentBanner(OpcionalVO vo, Function onPressed) {
    return Padding(
        padding: EdgeInsets.only(top: 50, right: 30, left: 30, bottom: 30),
        child: dialogContent(context, vo, onPressed));
  }

  Widget dialogContent(
      BuildContext context, OpcionalVO vo, Function onPressed) {
    return Container(
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          Card(
            elevation: 8,
            child: GestureDetector(
                onTap: () {
                  if (vo.possuiItem == 'NAO') {
                    final Perfil? _perfil =
                        BlocProvider.of<PerfilBloc>(context).perfil;

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ContratarOpcionalScreen(
                                perfil: _perfil,
                                opcional: vo,
                                previousScreenName: 'home',
                              )),
                    );
                  }
                },
                child: _imgDecode(vo)),
          ),
          Positioned(
            right: -5,
            top: -5,
            child: Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                key: Key('bntCloseBanner'),
                onTap: onPressed as void Function()?,
                child: Container(
                  decoration: BoxDecoration(
                      color: vo.getColor(),
                      borderRadius: BorderRadius.circular(20)),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _imgDecode(OpcionalVO vo) {
    return widget.amIHome!
        ? Image.memory(
            base64Decode(vo.fullBannerData.data!),
          )
        : Image.memory(
            base64Decode(vo.saibaMaisBannerData!.data!),
          );
  }
}
