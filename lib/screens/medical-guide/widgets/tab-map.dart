import 'dart:async';

import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_state.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/button_route_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/button_route_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/button_route_state.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_event.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_state.dart'
    as mgs;
import 'package:cliente_minha_unimed/bloc/medical-guide/pin_map_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/tab_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/tab_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/screens/medical-guide/widgets/marker-prestador.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/services/geolocation.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/maps.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:pa_virtual/shared/widgets/alert/alert.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

class GuiaMedicoTabMap extends StatefulWidget {
  final logger = UnimedLogger(className: 'GuiaMedicoTabMap');
  final TabController? tabController;
  final changeTab;

  GuiaMedicoTabMap({this.tabController, this.changeTab});

  _GuiaMedicoTabMapState createState() => _GuiaMedicoTabMapState();
}

class _GuiaMedicoTabMapState extends State<GuiaMedicoTabMap>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  Completer<GoogleMapController> _controllerComplete = Completer();
  late GoogleMapController _controller;

  final Set<Polyline> _polylines = Set<Polyline>();
  Map<MarkerId, MarkerPrestador> _markers = <MarkerId, MarkerPrestador>{};

  late AnimationController animationController;
  late Animation<Offset> animationOffset;

  ImageConfiguration? _imageConfiguration;
  List<LatLng> _points = [];

  MarkerPrestador? _previousMarker;
  Iterable<ProviderModel>? _prestadores =
      List<ProviderModel>.empty(growable: true);
  PrestadorSelecionado? _prestador;

  LatLng? _pointClicked;

  bool needRefreshMarkers = false;

  @override
  void initState() {
    super.initState();

    debugPrint('Init Tab Map');
    animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 400),
    );
    animationOffset = Tween<Offset>(
      begin: Offset(0.0, -1.0),
      end: Offset.zero,
    ).animate(animationController);

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    animationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      Future.delayed(Duration(seconds: 1), () {
        setState(() {
          _onTapMap(_pointClicked);
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TabBloc, TabState>(
      buildWhen: (oldState, state) {
        if (state is LoadedTabState) {
          _prestadores = state.prestadores;
          _markers = <MarkerId, MarkerPrestador>{};
        } else if (state is SelectedPrestadorTabState) {
          _prestadores = state.prestadores;
          _prestador = state.prestador;
          setState(() {
            // _selectAddress(_prestador!.endereco, _prestador!.prestador,
            //     _prestador!.latLng, _prestador!.index);

            // TODO verificar que, se são params obrigatórios, o model deveria ser required.
            _selectAddress(
                endereco: _prestador!.endereco,
                providerModel: _prestador!.prestador,
                ll: _prestador!.latLng,
                index: _prestador!.index);
          });
        }

        return true;
      },
      builder: (context, state) {
        debugPrint('Estado Map: ' + state.toString());
        if (state is LoadingTabState) {
          return EvaLoading(color: UnimedColors.orange);
        } else if (state is LoadedTabState) {
          _markers = <MarkerId, MarkerPrestador>{};
          _createMarkers();

          return _mapa();
        } else if (state is SelectedPrestadorTabState) {
          //_zoomInPrestador();
          return _mapa();
        } else {
          return EvaLoading();
        }
      },
    );
  }

  Widget _mapa() {
    _imageConfiguration ??= createLocalImageConfiguration(context);
    if (_prestadores!.length <= 0) {
      return BlocBuilder<MedicalGuideBloc, mgs.MedicalGuideState>(
          builder: (context, state) {
        String errorMessage = 'Sem resultados no momento...';
        if (state is mgs.ErrorSearchState) {
          errorMessage = state.message;
        }

        return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          EvaTriste(
            message: Text(errorMessage,
                style: Theme.of(context).textTheme.titleLarge),
          ),
        ]);
      });
    } else {
      return Stack(
        children: <Widget>[
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(-3.7305240631103516, -38.5148155247689),
              zoom: 13.0,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            rotateGesturesEnabled: false,
            mapToolbarEnabled: false,
            polylines: _polylines,
            markers: Set<Marker>.of(_markers.values),
            onTap: _onTapMap,
            onMapCreated: _onMapCreated,
            gestureRecognizers: Set()
              ..add(Factory<PanGestureRecognizer>(() => PanGestureRecognizer()))
              ..add(Factory<ScaleGestureRecognizer>(
                  () => ScaleGestureRecognizer()))
              ..add(Factory<TapGestureRecognizer>(() => TapGestureRecognizer()))
              ..add(Factory<VerticalDragGestureRecognizer>(
                  () => VerticalDragGestureRecognizer())),
          ),
          _cardDadosPrestador(),
        ],
      );
    }
  }

  void _onTapMap(LatLng? latLgn) {
    AnalyticsService().logButtonClick({'type': 'click off marker'});

    _clearRoute();
    _createMarkers();
    _zoomOutAllMarkers();

    animationController.reverse().then((_) async {
      if (_previousMarker != null && _prestador != null) {
        if (_markers.values.length > 1) {
          final _marker = await _previousMarker!.copyWithSmallIcon();

          setState(() {
            _markers[_previousMarker!.markerId] = _marker;
          });
        }
      }
      BlocProvider.of<ButtonRouteBloc>(context).add(InicialButtonRouteEvent());
      BlocProvider.of<MedicalGuideBloc>(context).add(ClearPrestador());
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    setState(() => _controller = controller);

    _createMarkers();

    Locator.instance
        .get<GeolocationService>()
        .getCurrentPosition()
        .then((position) {
      widget.logger.d('onMapCreated my position $position');

      Future.delayed(Duration(seconds: 1), () async {
        //final prestador = BlocProvider.of<TabBloc>(context).prestador;
        if (_prestador == null &&
            BlocProvider.of<MedicalGuideBloc>(context).state
                is LoadedSearchState) {
          _zoomOutAllMarkers();
        } else if (_prestador != null &&
            BlocProvider.of<TabBloc>(context).state
                is SelectedPrestadorTabState) {
          final markerId =
              MarkerPrestador.generateMarkerId(_prestador!.endereco);
          final marker = _markers[markerId];
          if (marker == null) {
            logger.e(
                'falha no marcador: ${_prestador!.prestador.providerName} ${_prestador!.latLng}');

            return;
          }
          final updatedMarker = await marker.copyWithBigIcon();

          setState(() {
            _markers[markerId] = updatedMarker;
            _previousMarker = updatedMarker;
          });

          final latlon = _prestador!.latLng;
          animationController.forward();
          _controller.animateCamera(CameraUpdate.newLatLngZoom(latlon, 15));
        }
      });
    });

    _controllerComplete.complete(controller);
  }

  void _zoomOutAllMarkers() {
    try {
      if (_points.length > 0) {
        LatLngBounds bounds = MapsUtil.getLatLngBounds(_points);
        _controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50.0));
      }
    } catch (e) {
      debugPrint("Falha ao dar zoomOut => $e");
    }
  }

  Widget _cardDadosPrestador() {
    final _prestadorSelecionado =
        BlocProvider.of<MedicalGuideBloc>(context).provider;

    return SlideTransition(
      position: animationOffset,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          InkWell(
            onTap: () {
              widget.changeTab(0);
              widget.tabController!.animateTo(0);
              BlocProvider.of<MedicalGuideBloc>(context).add(
                  ScrollToElementEvent(
                      index: _prestadorSelecionado?.index ?? 0));
            },
            child: Card(
              margin: EdgeInsets.symmetric(horizontal: 10),
              elevation: 5,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    _prestadorSelecionado != null
                        ? FittedBox(
                            fit: BoxFit.scaleDown,
                            alignment: Alignment.center,
                            child: Image.asset(
                              _prestadorSelecionado.prestador.iconPath,
                              width: 40,
                            ),
                          )
                        : Container(),
                    Flexible(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            _prestadorSelecionado != null
                                ? '${_prestadorSelecionado.prestador.providerName}'
                                : '',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 17,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            _prestadorSelecionado != null
                                ? '${_prestadorSelecionado.endereco}'
                                : '',
                            textAlign: TextAlign.center,
                          ),
                          BlocBuilder<ButtonRouteBloc, ButtonRouteState>(
                            builder:
                                (BuildContext context, ButtonRouteState state) {
                              if (state is DoneState) {
                                return Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Text(
                                      'distância: ',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    _medirDistancia(state.distance),
                                  ],
                                );
                              }

                              return Container();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 60),
              child: BlocBuilder<ButtonRouteBloc, ButtonRouteState>(
                buildWhen: (previousState, state) {
                  if (state is DoneState) {
                    final points =
                        BlocProvider.of<ButtonRouteBloc>(context).routeCoords!;

                    var prestador =
                        BlocProvider.of<MedicalGuideBloc>(context).provider!;
                    final markerId =
                        MarkerPrestador.generateMarkerId(prestador.endereco);

                    setState(() {
                      _polylines.add(
                        Polyline(
                          color: Colors.blue,
                          polylineId: PolylineId('route1'),
                          visible: true,
                          points: points,
                          width: 4,
                          startCap: Cap.roundCap,
                          endCap: Cap.buttCap,
                        ),
                      );

                      _markers
                          .removeWhere((mId, m) => markerId.value != mId.value);

                      needRefreshMarkers = true;
                    });

                    // Zoom out to entire router
                    LatLngBounds bounds = MapsUtil.getLatLngBounds(points);
                    _controller.animateCamera(
                        CameraUpdate.newLatLngBounds(bounds, 80.0));
                  } else if (state is ErrorState) {
                    if (!FlavorConfig.isProduction()) {
                      final snackBar = SnackBar(
                        duration: Duration(seconds: 2),
                        backgroundColor: Colors.red,
                        content: Text(state.message),
                      );
                      ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
                  }

                  return true;
                },
                builder: (context, state) {
                  if (state is LoadingState) {
                    return FloatingActionButton(
                      child: Center(
                        // Display Progress Indicator
                        child: CircularProgressIndicator(
                          backgroundColor: Colors.orange,
                        ),
                      ),
                      onPressed: () {},
                    );
                  } else if (state is InitialState ||
                      state is DoneState ||
                      state is ErrorState) {
                    return Column(
                      children: <Widget>[
                        FloatingActionButton(
                          child: Icon(Icons.directions),
                          onPressed: _handlePoints,
                        ),
                        _buttonOpenInMaps()
                      ],
                    );
                  }

                  return Container();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _createMarkers() {
    debugPrint('Criando Markers...');
    if (_prestadores != null && _prestadores!.length > 0) {
      _prestadores!.toList().asMap().forEach((index, p) async {
        if (p.addresses != null && p.addresses!.length > 0) {
          p.addresses!.forEach(
              (endereco) => _addMarkerPrestadorEndereco(endereco, p, index));
        }
      });
    }
  }

  _addMarkerPrestadorEndereco(
      AddressModel endereco, ProviderModel p, int index) {
    if (endereco.coords != null &&
        (endereco.coords!.lat ?? 0) != 0 &&
        (endereco.coords!.lat ?? 0) != 0) {
      final ll = LatLng(endereco.coords!.lat!, endereco.coords!.lon!);

      _points.add(ll);

      var marker = MarkerPrestador(
          endereco: endereco,
          prestador: p,
          pinMaps: BlocProvider.of<PinMapGuiaMedicoBloc>(context).pinsMap,
          onTap: () => _onTapEndereco(endereco, p, ll, index));

      _markers[marker.markerId] = marker;
    }
  }

  _onTapEndereco(
      AddressModel endereco, ProviderModel p, LatLng ll, int index) async {
    BlocProvider.of<ButtonRouteBloc>(context).add(InicialButtonRouteEvent());

    AnalyticsService().logButtonClick({
      'type': 'marker',
      'value':
          'codPrestador:${p.providerCode}-codEndereco:${endereco.codEndereco}'
    });

    // try {

    BlocProvider.of<MedicalGuideBloc>(context).add(SetPrestador(
      PrestadorSelecionado(
          latLng: ll, prestador: p, endereco: endereco, index: index),
    ));
  }

  /**
   *  return if is valid address
   */
  Future<void> _selectAddress({
    required AddressModel endereco,
    required ProviderModel providerModel,
    required LatLng ll,
    required int index,
  }) async {
    if (needRefreshMarkers) {
      _markers.clear();
      _createMarkers();

      needRefreshMarkers = false;
    }

    BlocProvider.of<ButtonRouteBloc>(context).add(InicialButtonRouteEvent());

    try {
      final MarkerId markerId = MarkerPrestador.generateMarkerId(endereco);
      final MarkerPrestador? marker = _markers[markerId];

      if (marker != null) {
        final MarkerPrestador updatedMarker = await marker.copyWithBigIcon();

        if (_previousMarker != null) {
          final MarkerPrestador previousMarker =
              await _previousMarker!.copyWithSmallIcon();

          _clearRoute();
          _markers[_previousMarker!.markerId] = previousMarker;
        }

        setState(() {
          _markers[markerId] = updatedMarker;
          _previousMarker = marker;
        });

        animationController.forward();

        _controller.animateCamera(CameraUpdate.newLatLngZoom(ll, 15));
      }
    } catch (e) {
      logger.e('_selectAdress error');
      logger.e(e.toString());
    }
  }

  /// Limpar a rota existente no mapa
  void _clearRoute() {
    setState(() {
      _polylines.removeWhere((i) => true);
    });
  }

  void _handlePoints() {
    final p = BlocProvider.of<MedicalGuideBloc>(context).provider;
    if (p != null) {
      AnalyticsService()
          .logButtonClick({'type': 'button', 'value': 'route to ${p.latLng}'});
      BlocProvider.of<ButtonRouteBloc>(context)
          .add(SearchButtonRouteEvent(p.latLng));
    }
  }

  Widget _medirDistancia(double distance) {
    var f = NumberFormat("###,###.0#", "pt_BR");

    return distance < 1000.0
        ? Text('${f.format(distance)} metros')
        : Text('${f.format(distance / 1000.0)} km');
  }

  Widget _buttonOpenInMaps() {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: new BorderRadius.circular(18.0),
        ),
      ),
      onPressed: _showPointInMap,
      child: Text("Abrir no mapa"),
    );
  }

  Future<void> _showPointInMap() async {
    final permissionLocation = await Permission.location.status;
    if (permissionLocation.isGranted) {
      final p = BlocProvider.of<MedicalGuideBloc>(context).provider!;
      var currentLocation =
          await Locator.instance.get<GeolocationService>().getCurrentPosition();

      String googleUrl =
          'https://www.google.com/maps/dir/?api=1&origin=${currentLocation!.latitude},${currentLocation.longitude}&destination=${p.latLng.latitude},${p.latLng.longitude}&travelmode=driving';
      if (await canLaunchUrl(Uri.parse(googleUrl))) {
        setState(() {
          _pointClicked =
              LatLng(currentLocation.latitude, currentLocation.longitude);
        });
        launchUrl(Uri.parse(googleUrl));
      } else {
        throw 'Could not open map.';
      }
    } else if (permissionLocation.isPermanentlyDenied) {
      _openAlerlLocalizationPermission(permissionLocation);
    } else {
      await Permission.location.request().then((value) {
        if (value.isPermanentlyDenied) {
          _openAlerlLocalizationPermission(permissionLocation);
        }
      });
    }
  }

  _openAlerlLocalizationPermission(PermissionStatus permissionLocation) {
    Alert.open(
      context,
      title: 'Acesso a localização ',
      text: 'Precisamos acessar a sua localização para acessar o mapa',
      textButtonClose: 'Fechar',
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.orange,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
          child: Text('Autorizar'),
          onPressed: () {
            Navigator.of(context).pop();
            openAppSettings();
          },
        ),
      ],
    );
  }
}
