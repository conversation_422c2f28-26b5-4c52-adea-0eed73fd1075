import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_event.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_events.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_conent_terms_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/res-brazil/alerts/res_alerts_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/allergies/res_brazil_allergy.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/attendance/main.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/diagnostico/res_brazil_%20diagnosis.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/documentos/res_brazil_documents.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/exam-results/res_brazil_exam_results.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/indicators/res_brazil_indicators_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/procedures/res_internal_precedure_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/card_res_icon_button.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/widgets/subtitle_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pa_virtual/shared/widgets/app_bar_unimed.dart';

class JorneyHealthHistory extends StatefulWidget {
  const JorneyHealthHistory({super.key});

  @override
  State<JorneyHealthHistory> createState() => _JorneyHealthHistoryState();
}

class _JorneyHealthHistoryState extends State<JorneyHealthHistory> {
  List<Widget> listButton = [];

  @override
  void initState() {
    _createListButton(context: context);

    BlocProvider.of<ResConfigBloc>(context).add(GetResConfigEvent(
        card: BlocProvider.of<PerfilBloc>(context)
            .perfil
            .contratoBeneficiario
            .carteira!
            .carteiraNumero,
        cpf: BlocProvider.of<PerfilBloc>(context)
                .perfil
                .contratoBeneficiario
                .beneficiario
                ?.cpf11Digits ??
            ""));

    AnalyticsService().addLogScreenView(
      screenName: 'JorneyHealthHistory',
      screenClass: 'JorneyHealthHistory',
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUnimed(
        title: Column(
          children: [
            AutoSizeText(
              'Histórico de Saúde',
              minFontSize: 10,
              maxLines: 1,
            ),
            SubtitleAppbar(),
          ],
        ),
      ),
      backgroundColor: UnimedColors.grayLight,
      body: BlocListener<ResConsentBloc, ResConsentState>(
        listener: (context, state) {
          if (state is ResConsnetTermsAccept && !state.isAccepted) {
            // Exibir mensagem de sucesso
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Seu aceite de compartilhamento de dados foi revogado com sucesso.'),
                backgroundColor: UnimedColors.green,
                duration: Duration(seconds: 3),
              ),
            );

            // Navegar para home
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => HomeScreen()),
              (route) => false,
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
          child: Column(
            children: [
              // Widget de compartilhamento de dados
              Container(
                margin: EdgeInsets.only(bottom: 24),
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.security,
                      size: 40,
                      color: UnimedColors.green,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Compartilhamento de dados do histórico de saúde',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: UnimedColors.grayDark,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Você pode revogar o consentimento para compartilhamento dos seus dados de saúde a qualquer momento.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: UnimedColors.grayDark,
                      ),
                    ),
                    SizedBox(height: 20),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _showRevokeConsentDialog,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Revogar Compartilhamento',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  childAspectRatio: 1.4,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    ...listButton.map(
                      (item) {
                        return item;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createListButton({required BuildContext context}) {
    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_attendance.svg',
        title: "Atendimento",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResAttencandeScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_document.svg',
        title: "Documentos",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResDocumentosScreen(
                card: BlocProvider.of<PerfilBloc>(context)
                    .perfil
                    .contratoBeneficiario
                    .carteira!
                    .carteiraNumero,
                listDiagnostics: const [],
              ),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_diagnostico.svg',
        title: "Diagnósticos",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResDiagnosticoScreen(
                card: BlocProvider.of<PerfilBloc>(context)
                    .perfil
                    .contratoBeneficiario
                    .carteira!
                    .carteiraNumero,
                listDiagnostics: [],
              ),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_exam_results.svg',
        title: "Resultados de exames",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResBrazilExamResults(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_allergy.svg',
        title: "Alergias",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResAllergiesScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_exam_result.svg',
        title: "Indicadores",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResIndicatorsScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_alert.svg',
        title: "Alertas",
        onClick: () {
          FocusScope.of(context).requestFocus(FocusNode());
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ResAlertsScreen()),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_procedure.svg',
        title: "Procedimentos",
        onClick: () {
          FocusScope.of(context).requestFocus(FocusNode());
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ResInternalProceduresScreen(
                      nameBeneficiary: BlocProvider.of<PerfilBloc>(context)
                          .perfil
                          .contratoBeneficiario
                          .beneficiario!
                          .nome,
                      beneficiaryCard: BlocProvider.of<PerfilBloc>(context)
                          .perfil
                          .contratoBeneficiario
                          .carteira!
                          .carteiraNumero,
                    )),
          );
        },
      ),
    );
  }

  void _showRevokeConsentDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Revogar Consentimento'),
          content: Text(
            'Tem certeza que deseja revogar o consentimento para compartilhamento de dados do histórico de saúde?',
          ),
          actions: [
            TextButton(
              child: Text('Cancelar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                'Revogar',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop();

                // Chamar o BLoC para revogar o consentimento
                BlocProvider.of<ResConsentBloc>(context).add(
                  AnswerTermsConsentEvent(
                    cpf: BlocProvider.of<UserBloc>(context).credentials.cpf,
                    value: false,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
