import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_event.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_events.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_conent_terms_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/alerts/res_alerts_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/allergies/res_brazil_allergy.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/attendance/main.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/diagnostico/res_brazil_%20diagnosis.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/documentos/res_brazil_documents.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/exam-results/res_brazil_exam_results.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/indicators/res_brazil_indicators_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/procedures/res_internal_precedure_screen.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/card_res_icon_button.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/widgets/subtitle_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pa_virtual/shared/widgets/app_bar_unimed.dart';

class JorneyHealthHistory extends StatefulWidget {
  const JorneyHealthHistory({super.key});

  @override
  State<JorneyHealthHistory> createState() => _JorneyHealthHistoryState();
}

class _JorneyHealthHistoryState extends State<JorneyHealthHistory> {
  List<Widget> listButton = [];

  @override
  void initState() {
    _createListButton(context: context);

    BlocProvider.of<ResConfigBloc>(context).add(GetResConfigEvent(
        card: BlocProvider.of<PerfilBloc>(context)
            .perfil
            .contratoBeneficiario
            .carteira!
            .carteiraNumero,
        cpf: BlocProvider.of<PerfilBloc>(context)
                .perfil
                .contratoBeneficiario
                .beneficiario
                ?.cpf11Digits ??
            ""));

    AnalyticsService().addLogScreenView(
      screenName: 'JorneyHealthHistory',
      screenClass: 'JorneyHealthHistory',
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUnimed(
        title: Column(
          children: [
            AutoSizeText(
              'Histórico de Saúde',
              minFontSize: 10,
              maxLines: 1,
            ),
            SubtitleAppbar(),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 28,
            ),
            onPressed: _showRevokeConsentDialog,
            tooltip: 'Revogar compartilhamento de dados',
          ),
        ],
      ),
      backgroundColor: UnimedColors.grayLight,
      body: BlocListener<ResConsentBloc, ResConsentState>(
        listener: (context, state) {
          if (state is ResConsnetTermsAccept && !state.isAccepted) {
            // Exibir mensagem de sucesso
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('Termo de consentimento'),
                content: Text(
                    'Seu aceite de compartilhamento de dados foi revogado com sucesso.'),
                actions: [
                  TextButton(
                    child: Text('OK'),
                    onPressed: () {
                      Navigator.of(context).popUntil((route) => route.isFirst);
                    },
                  ),
                ],
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
          child: Column(
            children: [
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  childAspectRatio: 1.4,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    ...listButton.map(
                      (item) {
                        return item;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createListButton({required BuildContext context}) {
    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_attendance.svg',
        title: "Atendimento",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResAttencandeScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_document.svg',
        title: "Documentos",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResDocumentosScreen(
                card: BlocProvider.of<PerfilBloc>(context)
                    .perfil
                    .contratoBeneficiario
                    .carteira!
                    .carteiraNumero,
                listDiagnostics: const [],
              ),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_diagnostico.svg',
        title: "Diagnósticos",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResDiagnosticoScreen(
                card: BlocProvider.of<PerfilBloc>(context)
                    .perfil
                    .contratoBeneficiario
                    .carteira!
                    .carteiraNumero,
                listDiagnostics: [],
              ),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_exam_results.svg',
        title: "Resultados de exames",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResBrazilExamResults(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_allergy.svg',
        title: "Alergias",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResAllergiesScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_exam_result.svg',
        title: "Indicadores",
        onClick: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResIndicatorsScreen(),
            ),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_alert.svg',
        title: "Alertas",
        onClick: () {
          FocusScope.of(context).requestFocus(FocusNode());
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ResAlertsScreen()),
          );
        },
      ),
    );

    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_procedure.svg',
        title: "Procedimentos",
        onClick: () {
          FocusScope.of(context).requestFocus(FocusNode());
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ResInternalProceduresScreen(
                      nameBeneficiary: BlocProvider.of<PerfilBloc>(context)
                          .perfil
                          .contratoBeneficiario
                          .beneficiario!
                          .nome,
                      beneficiaryCard: BlocProvider.of<PerfilBloc>(context)
                          .perfil
                          .contratoBeneficiario
                          .carteira!
                          .carteiraNumero,
                    )),
          );
        },
      ),
    );
  }

  void _showRevokeConsentDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('Revogar Consentimento'),
            ],
          ),
          content: Text(
            'Tem certeza que deseja revogar o consentimento para compartilhamento de dados do histórico de saúde?\n\nEsta ação irá remover sua autorização para compartilhamento dos seus dados médicos.',
          ),
          actions: [
            TextButton(
              child: Text('Cancelar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                'Revogar',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                // Chamar o BLoC para revogar o consentimento
                BlocProvider.of<ResConsentBloc>(context).add(
                  AnswerTermsConsentEvent(
                    cpf: BlocProvider.of<UserBloc>(context).credentials.cpf,
                    value: false,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
