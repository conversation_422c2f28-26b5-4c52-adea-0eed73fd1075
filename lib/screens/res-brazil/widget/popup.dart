import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';

import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_state.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_conent_terms_state.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_events.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/consent-form/res_consent_form_screen.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/widgets/popup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResPopupControl extends StatelessWidget {
  final Widget child;

  final logger = UnimedLogger(className: 'ResPopupControl');

  ResPopupControl({Key? key, required this.child}) : super(key: key);

  Widget build(BuildContext context) {
    return BlocListener<ResConfigBloc, ResConfigsState>(
      listener: (context, state) async {
        if (state is LoadedResConfigsState) {
          if (state.resConfigModel.popup?.enable == true) {
            if (state.resConfigModel.popup?.checkAccept == true) {
              BlocProvider.of<ResConsentBloc>(context).add(
                  CheckTermsConsentAcceptEvent(
                      cpf: BlocProvider.of<UserBloc>(context).credentials.cpf));
            } else {
              if (state.resConfigModel.popup?.title != null &&
                  state.resConfigModel.popup?.subtitle != null) {
                AlertPopup.open(context,
                    title: state.resConfigModel.popup!.title!,
                    subtitle: state.resConfigModel.popup!.subtitle!,
                    onConfirm: () {
                  logger.d("ResPopup - confirm popup");
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ResConsentPage()));
                }, onClose: () {
                  logger.d("ResPopup - close popup");
                });
              }
            }
          }
        }
      },
      child: BlocListener<ResConsentBloc, ResConsentState>(
          listener: (context, state) async {
            if (state is ResConsentTermsVerifyAccept && !state.isAccepted) {
              if (BlocProvider.of<ResConfigBloc>(context)
                          .resConfigModel
                          .popup
                          ?.enable ==
                      true &&
                  // Verifica se está home
                  (Navigator.of(context).canPop() == false)) {
                AlertPopup.open(context,
                    title: BlocProvider.of<ResConfigBloc>(context)
                            .resConfigModel
                            .popup
                            ?.title ??
                        "",
                    subtitle: BlocProvider.of<ResConfigBloc>(context)
                            .resConfigModel
                            .popup
                            ?.subtitle ??
                        "", onConfirm: () {
                  logger.d("ResPopup - confirm popup");
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ResConsentPage()));
                }, onClose: () {
                  logger.d("ResPopup - close popup");
                });
              }
            }
          },
          child: child),
    );
  }
}
