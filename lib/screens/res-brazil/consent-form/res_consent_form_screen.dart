import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_conent_terms_state.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_events.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/historic_healt_screen.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste-refreshable.dart';
import 'package:cliente_minha_unimed/shared/widgets/pdf-view/pdf-view-screen.dart';
import 'package:cliente_minha_unimed/shared/widgets/subtitle_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pa_virtual/shared/widgets/snack.dart';

class ResConsentPage extends StatefulWidget {
  @override
  _ResConsentPageState createState() => _ResConsentPageState();
}

class _ResConsentPageState extends State<ResConsentPage> {
  @override
  void initState() {
    BlocProvider.of<ResConsentBloc>(context).add(CheckTermsConsentAcceptEvent(
        cpf: BlocProvider.of<UserBloc>(context).credentials.cpf));

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUnimed(
        title: Column(
          children: [
            AutoSizeText(
              'Termo de consentimento',
              minFontSize: 10,
              maxLines: 1,
            ),
            SubtitleAppbar(),
          ],
        ),
      ),
      body: BlocConsumer<ResConsentBloc, ResConsentState>(
        listener: (context, state) {
          if (state is ResConsnetTermsAccept) {
            if (state.isAccepted) {
              Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => JorneyHealthHistory(),
                  ));
              _showMessage(state.message);
            } else {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: Text('Termo de consentimento'),
                    content: Text(
                        'Seu aceite de compartilhamento de dados foi revogado com sucesso.'),
                    actions: [
                      TextButton(
                        child: Text('OK'),
                        onPressed: () {
                         Navigator.of(context).popUntil((route) => route.isFirst);
                        },
                      ),
                    ],
                  );
                },
              );
            }
          } else if (state is ResConsentTermsVerifyAccept) {
            if (state.isAccepted) {
              Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => JorneyHealthHistory(),
                  ));
            } else {
              BlocProvider.of<ResConsentBloc>(context).add(
                  GetpdfConsentTextEvent(
                      cpf: BlocProvider.of<UserBloc>(context).credentials.cpf));
            }
          }
        },
        builder: (context, state) {
          if (state is ResConsentLoading) {
            return EvaLoading();
          } else if (state is ResConsetLoaded) {
            return FutureBuilder<String>(
              future: _viewPDFFromBase64(
                base64Report: state.base64Report,
                context: context,
              ),
              builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return CircularProgressIndicator(); // Exibe um indicador de carregamento
                } else if (snapshot.hasError) {
                  return Text(
                      'Erro: ${snapshot.error}'); // Exibe uma mensagem de erro
                } else if (snapshot.hasData) {
                  return PopScope(
                    onPopInvokedWithResult: (didPop, result) async {
                      BlocProvider.of<ResConsentBloc>(context).add(
                        AnswerTermsConsentEvent(
                            cpf: BlocProvider.of<UserBloc>(context)
                                .credentials
                                .cpf,
                            value: false),
                      );
                    },
                    child: PDFViewScreen(
                      isAppBarVisible: false,
                      snapshot.data!,
                      share: true,
                      isPath: true,
                      filename: '',
                      title: 'Termo de consentimento do beneficiário',
                      onAccepted: () {
                        BlocProvider.of<ResConsentBloc>(context).add(
                            AnswerTermsConsentEvent(
                                cpf: BlocProvider.of<UserBloc>(context)
                                    .credentials
                                    .cpf,
                                value: true));
                      },
                      onRejected: () {
                        // Enviamos o evento com value: false
                        BlocProvider.of<ResConsentBloc>(context).add(
                          AnswerTermsConsentEvent(
                            cpf: BlocProvider.of<UserBloc>(context)
                                .credentials
                                .cpf,
                            value: false,
                          ),
                        );
                      },
                    ),
                  );
                } else {
                  return Text('Sem dados disponíveis.'); // Caso genérico
                }
              },
            );
          } else if (state is ResConsentError) {
            return EvaTristeRefreshable(
                onRefresh: () async {
                  BlocProvider.of<ResConsentBloc>(context).add(
                      CheckTermsConsentAcceptEvent(
                          cpf: BlocProvider.of<UserBloc>(context)
                              .credentials
                              .cpf));
                },
                message: Text(state.message));
          } else {
            return Container();
          }
        },
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        Snack.success(
          message,
          duration: Duration(seconds: 3),
        ),
      );
    });
  }

  Future<String> _viewPDFFromBase64({
    required base64Report,
    required context,
  }) async {
    final pathFile = await FileUtils.createFileFromString(
        base64String: base64Report, extension: FileExtension.PDF);
    return pathFile;
  }
}
