import 'dart:math';

// import 'package:cliente_minha_unimed/bloc/tracking-ambulance/tracking-ambulance_bloc.dart';
// import 'package:cliente_minha_unimed/bloc/tracking-ambulance/tracking-ambulance_event.dart';
// import 'package:cliente_minha_unimed/screens/home/<USER>/main.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:biometria_perfilapps/main.dart';
import 'package:biometria_perfilapps/widgets/register_biometric_check.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_state.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/verify_show_schedule_exams/verify_show_schedule_exams_bloc.dart';
import 'package:cliente_minha_unimed/bloc/general_config/general_config_bloc.dart';
import 'package:cliente_minha_unimed/bloc/general_config/general_config_state.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_event.dart';
import 'package:cliente_minha_unimed/bloc/order-buttons-home/order-buttons_bloc.dart';
import 'package:cliente_minha_unimed/bloc/order-buttons-home/order-buttons_state.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pa-virtual/list-consultations/list_ve_consultations_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_event.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_state.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/scheduling_consultation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/speciality-page/specialty_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/speciality-page/specialty_event.dart';
import 'package:cliente_minha_unimed/bloc/virtual-card/virtual_card/virtual_card_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/order-buttons-home/order-buttons-home.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/permissions/permission.const.dart';
import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/screens/agendamento/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/main.dart';
import 'package:cliente_minha_unimed/screens/checkin-unity/main.dart';
import 'package:cliente_minha_unimed/screens/club-mais-vantagem/club-mais-vantagem_screen.dart';
import 'package:cliente_minha_unimed/screens/conecta-saude/main.dart';
import 'package:cliente_minha_unimed/screens/exams/main.dart';
import 'package:cliente_minha_unimed/screens/financeiro/main.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/home/<USER>/home-button-permission.model.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/home/<USER>/pending_issues_widget.dart';
import 'package:cliente_minha_unimed/screens/insecurance-screen/insurance_screen.dart';
import 'package:cliente_minha_unimed/screens/medical-guide/portal/main.dart';
import 'package:cliente_minha_unimed/screens/meu-plano/main.dart';
import 'package:cliente_minha_unimed/screens/opcionais/main.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/consent-form/res_consent_form_screen.dart';
//import 'package:cliente_minha_unimed/screens/res-brazil/consent-form/res_consent_form_screen.dart';
import 'package:cliente_minha_unimed/screens/scheduling-consultation/main.dart';
import 'package:cliente_minha_unimed/screens/virtual-card/main.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/constants.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/size.transition.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/services/permission.service.dart';
import 'package:cliente_minha_unimed/shared/utils/biometry_utils.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/uicons.dart';
import 'package:cliente_minha_unimed/shared/utils/user_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-evaluation.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/show-up-animation/show-up-animation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

const WIDTH_BUTTON = 130.0;

class PanelButtonsHome extends StatefulWidget {
  PanelButtonsHome();

  _PanelButtonsHomeState createState() => _PanelButtonsHomeState();
}

class _PanelButtonsHomeState extends BaseState<PanelButtonsHome> {
  final UnimedLogger logger = UnimedLogger(className: 'PanelButtonsHome');

  ScrollController? scrollController;
  bool _expandedMode = false;

  final double expandedHight = 150.0;
  final String screenKey = PermissionConst.home;
  static const baseTranslate = 'home.buttons';

  bool loadingRegisterBiometricButton = false;

  @override
  void initState() {
    final _perfil = BlocProvider.of<PerfilBloc>(context).perfil;
    BlocProvider.of<OpcionalBloc>(context)
        .add(ChecarOpcionaisEvent(perfil: _perfil));

    // BlocProvider.of<GeneralConfigBloc>(context).add(
    //     GetGeneralConfigEvent(carteiraId: _perfil.carteira!.carteiraNumero));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<GeneralConfigBloc, GeneralConfigState>(
      listener: (context, state) {
        if (state is DoneGeneralConfigState) {
          BlocProvider.of<VirtualCardBloc>(context).add(
            GetAllVirtualCardsEvent(
              perfis: BlocProvider.of<UserBloc>(context).user.perfis,
              slectedPerfil: BlocProvider.of<PerfilBloc>(context).perfil,
            ),
          );
          if ((state.generalConfigModel.pendingIssues?.enablePendingIssues ??
                  false) &&
              BlocProvider.of<PendingIssuesBloc>(context).state
                  is InitialPendingIssuesState) {
            BlocProvider.of<PendingIssuesBloc>(context).add(
              GetUpcomingInvoicesEvent(
                perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                cpfLoggedIn:
                    BlocProvider.of<UserBloc>(context).credentials.cpfSabius,
                dueInDays: BlocProvider.of<GeneralConfigBloc>(context)
                        .generalConfigModel
                        ?.pendingIssues
                        ?.dueInDays ??
                    0,
              ),
            );
          }
          // BlocProvider.of<VirtualCardBloc>(context).add(UpdateExpirationTime(
          //     addTimeInSeconds:
          //         state.generalConfigModel.cache.timeExpirationVirtualCard));
          if (state.generalConfigModel.buttons.homeOpen) _open();
        }
      },
      child: BlocBuilder<PerfilBloc, PerfilState>(
        builder: (context, state) {
          Perfil? _profile = BlocProvider.of<PerfilBloc>(context).perfil;
          ProfileRoles2ScreenModel _screenPermissions;
          if (state is LoadedProfileState) {
            _profile = state.profile;
            _screenPermissions = state.profile.permissions!
                .firstWhere((element) => element.screen == screenKey);

            return BlocListener<AutorizacoesEnvioBloc, AutorizacoesEnvioState>(
              listener: (context, autorizacoesState) {
                if (autorizacoesState is AutorizacoesEnvioDoneState ||
                    autorizacoesState is AutorizacoesEnvioErrorState) {
                  BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
                      .add(RemoveAllAttachedFiles());
                }
              },
              child: Expanded(
                child: ShaderMask(
                  shaderCallback: (Rect rect) {
                    return LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white,
                        Colors.transparent,
                        Colors.transparent,
                        Colors.white
                      ],
                      stops: [
                        0.0,
                        0.1,
                        0.9,
                        1.0
                      ], // 10% purple, 80% transparent, 10% purple
                    ).createShader(rect);
                  },
                  blendMode: BlendMode.dstOut,
                  child: ListView(
                    physics: ClampingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    children: <Widget>[
                      PendingIssuesWidget(),
                      BlocBuilder<OrderButtonsHomeBloc, OrderButtonsHomeState>(
                        builder: (context, state) {
                          if (state is OrderButtonsHomeLoaded) {
                            final List<Widget> _list = _buttons(
                              profile: _profile,
                              screenPermissions: _screenPermissions,
                              orderButtonsHome: state.orderButtonsHome,
                            );
                            final List<Widget> _filteredList =
                                !_expandedMode && _list.length > 3
                                    ? _list.sublist(0, 3)
                                    : _list;

                            if (PermissionService().canViewWidget(
                                PermissionProfileBiometricRegistration(),
                                _screenPermissions)) {
                              _list.add(_homeButtonRegisterBiometrics(
                                  _list.length * 50, _profile));
                            }

                            return _buildButtons(
                              filteredList: _filteredList,
                              fullList: _list,
                            );
                          } else if (state is OrderButtonsHomeError ||
                              state is OrderButtonsHomeLoading) {
                            final List<Widget> _list = _buttons(
                              profile: _profile,
                              screenPermissions: _screenPermissions,
                            );
                            final List<Widget> _filteredList =
                                !_expandedMode && _list.length > 3
                                    ? _list.sublist(0, 3)
                                    : _list;

                            if (PermissionService().canViewWidget(
                                PermissionProfileBiometricRegistration(),
                                _screenPermissions)) {
                              _list.add(_homeButtonRegisterBiometrics(
                                  _list.length * 50, _profile));
                            }

                            return _buildButtons(
                              filteredList: _filteredList,
                              fullList: _list,
                            );
                          }
                          return const Center();
                        },
                      ),
                      PanelNoticiasHome(
                        onError: () {
                          SchedulerBinding.instance
                              .addPostFrameCallback((_) {});
                        },
                      ),
                      //Espaço para evitar que icone da Eva cobrisse o ultimo botão
                      SizedBox(height: 48),
                    ],
                  ),
                ),
              ),
            );
          } else if (state is LoadingPerfilState) {
            return EvaLoading(
              text: Text(
                'Carregando perfil e permissões',
                style: TextStyle(
                    color: UnimedColors.greenChart,
                    fontWeight: FontWeight.bold),
              ),
            );
          } else
            return Container();
        },
      ),
    );
  }

  Widget _buildButtons(
      {required List<Widget> filteredList, required List<Widget> fullList}) {
    return Column(
      children: [
        if (filteredList.length >= 3)
          GridView.count(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            crossAxisCount: 3,
            padding: EdgeInsets.only(bottom: 8, left: 8, right: 8),
            children: filteredList,
          ),
        if (fullList.length > 3)
          AnimatedArrow(
            angle: _expandedMode ? pi : 0,
            onTap: _expandedMode ? _close : _open,
          ),
      ],
    );
  }

  _goTo(StatefulWidget page) {
    logger.i('_goTo ${page.toStringShort()}');
    Navigator.push(context, SizeRoute(page: page));
  }

  _goToFinanceiro() {
    _goTo(FinanceiroScreen());
  }

  _goToMeuPlano() {
    AnalyticsService()
        .logButtonClick({'type': 'button', 'value': 'home_meu_plano'});
    _goTo(MeuPlanoScreen());
  }

  _goToAgendamento() {
    AnalyticsService()
        .logButtonClick({'type': 'button', 'value': 'home_consultas'});
    final _perfil = BlocProvider.of<PerfilBloc>(context).perfil;
    BlocProvider.of<ListVeConsultationsBloc>(context).add(ResetState());

    BlocProvider.of<AgendamentoBloc>(context).add(ListarEvent(perfil: _perfil));

    _goTo(AgendamentoScreen());
  }

  _goToAgendamentoDeConsultas() {
    AnalyticsService().logButtonClick(
        {'type': 'button', 'value': 'home_agendamento_consultas'});
    final _perfil = BlocProvider.of<PerfilBloc>(context).perfil;

    BlocProvider.of<SchedulingConsultationBloc>(context)
        .add(SchedulingSetToInitialState());

    BlocProvider.of<SchedulingConsultationBloc>(context)
        .add(ClearSpecialtyEvent());

    BlocProvider.of<SpecialtyBloc>(context).add(ListScheduledSpecialtiesEvent(
      perfil: _perfil,
    ));
    _goTo(SchedulingConsulationScreen());
  }

  void _goToHealthConnection(
      bool showOptionals, ProfileRoles2ScreenModel screenPermissions) {
    _goTo(UnimedConectaScreen(
      showOptionals: showOptionals,
    ));
  }

  _goToOpcionais() {
    _goTo(OpcionaisScreen());
  }

  _goToAutorizacoes() {
    AnalyticsService()
        .logButtonClick({'type': 'button', 'value': 'home_autorizacoes'});
    _goTo(AutorizacoesScreen());
  }

  _goToProtocol() {
    AnalyticsService()
        .logButtonClick({'type': 'button', 'value': 'home_autorizacoes'});
    _goTo(ProtocolsScreen());
  }

  void _goToCartaoVirtual() {
    AnalyticsService().logButtonClick({
      'type': 'button',
      'value': 'home_cartao_virtual',
    });
    _goTo(CartaoVirtualScreen());
  }

  void _goToConsentForm() {
    Navigator.push(
        context, MaterialPageRoute(builder: (context) => ResConsentPage()));
  }

  void _goToClubMaisVantagens() {
    _goTo(ClubMaisVantagensScreen());
  }

  void _goToInsecuranceScreen() {
    _goTo(InsuranceScreen());
  }

  void _open() {
    setState(() {
      _expandedMode = true;
    });
  }

  void _close() {
    setState(() {
      _expandedMode = false;
    });
  }

  @override
  void dispose() {
    scrollController?.dispose();
    super.dispose();
  }

  bool _showOptionalButton(
      Perfil? profile, ProfileRoles2ScreenModel screenPermissions) {
    if (profile != null &&
        profile.titular &&
        profile.pessoaFisica! &&
        (PermissionService().canViewWidget(
            PermissionProfileWidgetOPTIONAL(), screenPermissions))) return true;

    return false;
  }

  List<Widget> _buttons({
    Perfil? profile,
    required ProfileRoles2ScreenModel screenPermissions,
    List<OrderButtonsHomeModel>? orderButtonsHome,
  }) {
    final List<HomeButtonPermission> rawButtons =
        _listButtons(profile, screenPermissions);

    if (orderButtonsHome != null) {
      for (final button in rawButtons) {
        button.order = orderButtonsHome
            .firstWhere((element) => element.description == button.description)
            .order;
      }

      rawButtons.sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
    }

    return rawButtons
        .where((button) => button.canShow)
        .toList()
        .asMap()
        .entries
        .map<Widget>(
          (entry) => ShowUp(
            delay: entry.key * 50,
            duration: 500,
            child: ButtonHome(
              sgvColor: entry.value.description == 'club-mais-vantagens'
                  ? false
                  : true,
              sgvSize:
                  entry.value.description == 'club-mais-vantagens' ? 80 : 50,
              showLabel: entry.value.description == 'club-mais-vantagens'
                  ? false
                  : true,
              key: entry.value.key,
              text: entry.value.label,
              widgetText: entry.value.widgetLabel,
              icomoon: entry.value.icon ?? Icons.error,
              iconPath: entry.value.iconPath ?? null,
              backgroundColor: UnimedColors.green,
              onPressed: entry.value.onPressed,
              width: WIDTH_BUTTON,
            ),
          ),
        )
        .toList();
  }

  List<HomeButtonPermission> _listButtons(
    Perfil? profile,
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return [
      _insecuranceScreen(screenPermissions),
      _clubeMaisVantagens(screenPermissions),
      _financialButton(screenPermissions),
      _consultasVirtuaisButton(screenPermissions, profile),
      _checkinUnitButton(screenPermissions),
      _solicitationButton(screenPermissions),
      _schedulingConsultation(screenPermissions),
      _virtualCard(screenPermissions),
      _examsButton(screenPermissions),
      _consultationsButton(screenPermissions),
      _planButton(screenPermissions),
      _medicalGuideButton(screenPermissions),
      _resBrazilButton(screenPermissions),
      _optionalButton(screenPermissions, profile),
      _evaluateButton(screenPermissions),
    ];
  }

  HomeButtonPermission _resBrazilButton(
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetRes(),
      screenPermissions: screenPermissions,
      label: 'Histórico de Saúde',
      icon: UIcons.stethoscope,
      onPressed: _goToConsentForm,
      description: RES,
    );
  }

  HomeButtonPermission _clubeMaisVantagens(
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetClubMais(),
      screenPermissions: screenPermissions,
      label: 'Clube Mais Vantagens',
      iconPath: 'assets/svg/icon_clube-mais-vantagens.svg',
      icon: Icons.local_offer_outlined,
      onPressed: _goToClubMaisVantagens,
      description: CLUB_MAIS_VANTAGENS,
    );
  }

  HomeButtonPermission _insecuranceScreen(
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetClubMais(),
      screenPermissions: screenPermissions,
      widgetLabel: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Seguros',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 3),
            FittedBox(
              child: AutoSizeText(
                'Proteja o seu patrimônio',
                textAlign: TextAlign.center,
                style: TextStyle(
                  // fontSize: 10,
                  color: Colors.white,
                ),
                minFontSize: 6,
                maxFontSize: 10,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
      label: 'Seguros',
      iconPath: 'assets/svg/icon_seguros-cliente.svg',
      icon: Icons.local_offer_outlined,
      onPressed: _goToInsecuranceScreen,
      description: INSECURANCE_SCREEN,
    );
  }

  HomeButtonPermission _consultasVirtuaisButton(
      ProfileRoles2ScreenModel screenPermissions, Perfil? profile) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetHELTHCONNECTION(),
      screenPermissions: screenPermissions,
      // label: translate('$baseTranslate.healthConection'),
      label: "",
      widgetLabel: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(children: [
          TextSpan(
            text: translate('$baseTranslate.healthConection'),
            style: TextStyle(fontSize: 13),
          ),
        ]),
      ),
      icon: ConectaSaudeIcons.unimed_conecta,
      key: Key('consultasVirtuais'),
      onPressed: () => _goToHealthConnection(
        _showOptionalButton(profile, screenPermissions),
        screenPermissions,
      ),
      description: CONSULTAS_VIRTUAIS,
    );
  }

  HomeButtonPermission _solicitationButton(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetAUTHORIZATION(),
      screenPermissions: screenPermissions,
      label: (BlocProvider.of<GeneralConfigBloc>(context)
                  .generalConfigModel
                  ?.buttons
                  .authorizationsV2 ??
              false)
          ? translate('$baseTranslate.protocols')
          : translate('$baseTranslate.requests'),
      icon: UIcons.autorizacoes,
      onPressed: (BlocProvider.of<GeneralConfigBloc>(context)
                  .generalConfigModel
                  ?.buttons
                  .authorizationsV2 ??
              false)
          ? _goToProtocol
          : _goToAutorizacoes,
      description: AUTHORIZATIONS,
    );
  }

  HomeButtonPermission _examsButton(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetEXAMS(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.exams'),
      icon: ConectaSaudeIcons.exams,
      onPressed: () => {
        BlocProvider.of<ScheduleExamsVerifyBloc>(context).add(
          VerifyMustShowScheduleExams(
              perfil: BlocProvider.of<PerfilBloc>(context).perfil),
        ),
        _goTo(ExamsScreen())
      },
      description: LIST_EXAMS,
    );
  }

  HomeButtonPermission _consultationsButton(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetAPPOINTMENT(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.medicalAppointment'),
      icon: UIcons.agendamento,
      onPressed: _goToAgendamento,
      description: APPOINTMENT,
    );
  }

  HomeButtonPermission _schedulingConsultation(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetSCHEDULEAPPOINTMENT(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.schedulingAppointment'),
      icon: UIcons.agendamento,
      onPressed: _goToAgendamentoDeConsultas,
      description: SCHEDULING_APPOINTMENT,
    );
  }

  HomeButtonPermission _virtualCard(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetVIRTUALCARD(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.virtualCard'),
      icon: UIcons.cartaovirtual,
      onPressed: _goToCartaoVirtual,
      description: VIRTUAL_CARD,
    );
  }

  HomeButtonPermission _planButton(ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetPLAN(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.myPlan'),
      icon: UIcons.meuplano,
      onPressed: _goToMeuPlano,
      description: PLAN,
    );
  }

  HomeButtonPermission _financialButton(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      key: Key('btnFinancial'),
      permission: PermissionProfileWidgetFINANCIAL(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.financial'),
      icon: UIcons.financeiro,
      onPressed: _goToFinanceiro,
      description: FINANCIAL,
    );
  }

  HomeButtonPermission _optionalButton(
      ProfileRoles2ScreenModel screenPermissions, Perfil? profile) {
    return HomeButtonPermission(
      key: Key('homeButtonOpcional'),
      permission: PermissionProfileWidgetOPTIONAL(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.optional'),
      icon: UIcons.opcionais,
      onPressed: _goToOpcionais,
      disabled: !_showOptionalButton(profile, screenPermissions),
      description: OPTIONAL,
    );
  }

  HomeButtonPermission _evaluateButton(
      ProfileRoles2ScreenModel screenPermissions) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetEVALUATION(),
      key: Key('btnAvaliar'),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.evaluate'),
      icon: UIcons.starHalf,
      onPressed: () => AlertEvaluation.exibeAlert(
        context: context,
        servico: AvaliacaoLabels.HOME,
      ),
      description: EVALUATION,
    );
  }

  HomeButtonPermission _checkinUnitButton(
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return HomeButtonPermission(
      permission: PermissionProfileWidgetCHECKINUNIT(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.checkinUnity'),
      icon: UIcons.checkin,
      onPressed: () => {_goTo(CheckinUnityScreen())},
      description: CHECKIN_UNITY,
    );
  }

  Widget _registerBiometricsButton() {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: UnimedColors.green,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
          bottomLeft: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: UnimedColors.grayDark,
            blurRadius: 2.0,
            spreadRadius: 0.0,
            offset: Offset(2.0, 2.0),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Icon(UIcons.biometrics, size: 40, color: Colors.white),
          ),
          Flexible(
            child: Container(
              width: WIDTH_BUTTON,
              padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
              child: AutoSizeText(
                translate('$baseTranslate.registerBiometrics'),
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 13, color: Colors.white),
              ),
            ),
          )
        ],
      ),
    );
  }

  HomeButtonPermission _medicalGuideButton(
    ProfileRoles2ScreenModel screenPermissions,
  ) {
    return HomeButtonPermission(
      permission: PermissionMedicalGuidePortal(),
      screenPermissions: screenPermissions,
      label: translate('$baseTranslate.medicalGuide'),
      icon: UIcons.painRadar,
      onPressed: () => {_goTo(MedicalGuidePortalScreen())},
      description: MEDICAL_GUIDE_PORTAL,
    );
  }

  Widget _loadingButton() {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: UnimedColors.green,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
          bottomLeft: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: UnimedColors.grayDark,
            blurRadius: 2.0,
            spreadRadius: 0.0,
            offset: Offset(2.0, 2.0),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Flexible(
            child: Container(
              width: WIDTH_BUTTON,
              padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
              child: SpinKitThreeBounce(
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _homeButtonRegisterBiometrics(int delay, Perfil? profile) {
    return ShowUp(
      delay: delay,
      duration: 500,
      child: Padding(
        padding: EdgeInsets.all(5),
        child: RegisterBiometricCheck(
          perfilAppsCredentials: BiometriaUtils.convertDefaultCredentials(
              FlavorConfig.instance!.values.profilePermissions),
          carteira:
              profile?.contratoBeneficiario.carteira?.carteiraNumero ?? "",
          logger: BiometryLogger.biometryLogger,
          environment: FlavorConfig.instance!.values.validadeBioIdEnv,
          child: loadingRegisterBiometricButton
              ? _loadingButton()
              : _registerBiometricsButton(),
          onLoading: () {
            logger.i('_goTo RegisterBiometricButton');

            setState(() {
              loadingRegisterBiometricButton = true;
            });
          },
          onError: (String message) {
            logger
                .i('_goTo RegisterBiometricButton error on biometric register');
            setState(() {
              loadingRegisterBiometricButton = false;
            });
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            Alert.open(
              context,
              title: 'Biometria',
              text: translate(message),
            );
          },
          onExistBiometrics: () {
            logger.i(
                '_goTo RegisterBiometricButton biometrics already registered');

            setState(() {
              loadingRegisterBiometricButton = false;
            });
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            Alert.open(
              context,
              title: 'Biometria',
              text: translate('alert.biometria.biometricsRegistered'),
            );
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
          onNotExistBiometrics: () async {
            logger.i('_goTo RegisterBiometricButton biometrics not registered');

            if (UserUtils.isAgeBetween(
                profile: profile!, startAge: 6, endAge: 18)) {
              _alertRangeAge(profile);
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BiometryUnimed(
                    perfilAppsCredentials:
                        BiometriaUtils.convertDefaultCredentials(
                            FlavorConfig.instance!.values.profilePermissions),
                    carteira:
                        profile.contratoBeneficiario.carteira!.carteiraNumero,
                    cameraPreview: BlocProvider.of<UserBloc>(context)
                            .user
                            .biometry
                            .cameraPicker ==
                        false,
                    maxHeight: BlocProvider.of<UserBloc>(context)
                        .user
                        .biometry
                        .alturaMaxima!,
                    maxQuality: BlocProvider.of<UserBloc>(context)
                        .user
                        .biometry
                        .qualidadeMaxima!,
                    name: profile.contratoBeneficiario.beneficiario!.nome,
                    codBeneficiario:
                        profile.contratoBeneficiario.beneficiario!.codBenef,
                    environment: FlavorConfig.instance!.values.validadeBioIdEnv,
                    onCancel: () {
                      Navigator.of(context).pop();
                    },
                    onValid: () {
                      logger.i(
                          '_goTo RegisterBiometricButton biometrics registered by home');
                      Navigator.of(context).pop();
                    },
                    logger: BiometryLogger.biometryLogger,
                  ),
                ),
              );
            }

            setState(() {
              loadingRegisterBiometricButton = false;
            });
          },
        ),
      ),
    );
  }

  _alertRangeAge(Perfil profile) {
    Alert.open(
      context,
      title: 'Atenção',
      text:
          'A biometria a ser cadastrada precisa ser do paciente, e não do responsável financeiro.',
      textButtonClose: 'Cancelar',
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.green,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
          child: Text('Ok'),
          onPressed: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BiometryUnimed(
                  perfilAppsCredentials:
                      BiometriaUtils.convertDefaultCredentials(
                          FlavorConfig.instance!.values.profilePermissions),
                  carteira:
                      profile.contratoBeneficiario.carteira!.carteiraNumero,
                  cameraPreview: BlocProvider.of<UserBloc>(context)
                          .user
                          .biometry
                          .cameraPicker ==
                      false,
                  maxHeight: BlocProvider.of<UserBloc>(context)
                      .user
                      .biometry
                      .alturaMaxima!,
                  maxQuality: BlocProvider.of<UserBloc>(context)
                      .user
                      .biometry
                      .qualidadeMaxima!,
                  name: profile.contratoBeneficiario.beneficiario!.nome,
                  codBeneficiario:
                      profile.contratoBeneficiario.beneficiario!.codBenef,
                  environment: FlavorConfig.instance!.values.validadeBioIdEnv,
                  onCancel: () {
                    Navigator.of(context).pop();
                  },
                  onValid: () {
                    logger.i(
                        '_goTo RegisterBiometricButton biometrics registered by home');
                    Navigator.of(context).pop();
                  },
                  logger: BiometryLogger.biometryLogger,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
