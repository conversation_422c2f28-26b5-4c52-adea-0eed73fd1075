import 'dart:convert';

import 'package:cliente_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PixPaymentPage extends StatelessWidget {
  final String pixCopiaCola;
  final String pixQrCode;
  final String? valor;

  const PixPaymentPage({
    Key? key,
    required this.pixCopiaCola,
    required this.pixQrCode,
    this.valor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
   
    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Pagamento pendente',
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              Image.memory(
                base64Decode(pixQrCode),
                width: MediaQuery.of(context).size.width / 2,
                height: MediaQuery.of(context).size.width / 2,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Valor total:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(width: 8),
                  Text(
                    valor ?? 'R\$ 0,00',
                    style: TextStyle(fontWeight: FontWeight.bold, color: UnimedColors.redStatus),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              InkWell(
                onTap: () {
                   Clipboard.setData(ClipboardData(text: pixCopiaCola));
                },
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    border: Border.all(color: UnimedColors.green),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    pixCopiaCola,
                    style: const TextStyle(fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: UnimedColors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: pixCopiaCola));

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Código Pix copiado!'),
                    ),
                  );
                },
                child: const Text('Copiar código Pix'),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
