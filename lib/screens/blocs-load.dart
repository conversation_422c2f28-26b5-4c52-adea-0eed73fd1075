import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push/cancelar_agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/chat-pendencia/chat_pendencia_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/consulta_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/controle-animacao-consulta/controle_animacao_consulta_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/especialidade_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/teleconsulta_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/verificar-pendencia/verificar_pendencia_bloc.dart';
import 'package:cliente_minha_unimed/bloc/auth-token/auth_token_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/pdf_exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/detalhe-guia-timeline/detalhe_guia_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/detalhe-guia/detalhe_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/guias_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_bloc.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/banners/banners_bloc.dart';
import 'package:cliente_minha_unimed/bloc/beneficiary-protocol/beneficiary_protocol_bloc.dart';
import 'package:cliente_minha_unimed/bloc/cadastral-update/accession-notification-bloc.dart';
import 'package:cliente_minha_unimed/bloc/cadastral-update/cadastral-update-bloc.dart';
import 'package:cliente_minha_unimed/bloc/cadastro/cadastro_bloc.dart';
import 'package:cliente_minha_unimed/bloc/cadastro/disable_account_bloc.dart';
import 'package:cliente_minha_unimed/bloc/channel-ethics/channel_ethics_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-emergency/checkin_emergency_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-emergency/infos/checkin_page_info_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-emergency/qrcode/checkin_page_qrcode_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-emergency/sympthons-pains/checkin_sympthons_pains_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/checkin_laboratory_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/exam/exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/guide/guide_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/mydata/my_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/verify-checkin/new/create_new_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-laboratory/verify-checkin/verify_checkin_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/checkin_surgery_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/documents/documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/informational-documents/checkin_surgery_informational_documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/infos/checkin_surgery_info_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/infos/load-info/load_info_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/latex/checkin_surgery_latex_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/mydata/my_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/status-documents/checkin_surgery_status_documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/checkin-surgery/verify-checkin/verify_checkin_surgery_bloc.dart';
import 'package:cliente_minha_unimed/bloc/clube-mais-vantagens/club-mais-vantagens_bloc.dart';
import 'package:cliente_minha_unimed/bloc/contrato/alterar-endereco/alterar_endereco_bloc.dart';
import 'package:cliente_minha_unimed/bloc/contrato/buscar-enderecos/buscar_enderecos_bloc.dart';
import 'package:cliente_minha_unimed/bloc/contrato/contrato_bloc.dart';
import 'package:cliente_minha_unimed/bloc/control_screen_bloc.dart';
import 'package:cliente_minha_unimed/bloc/eva/eva_messages_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/list-exams/list_exams_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/list-images-exams/list_images_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/address/schedule_exam_address_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/create/create_exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/guide/guide_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/image-exam-schedule-history/image_exam_schedule_history_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/mydata/my_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/questionary/questionary_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/schedule_exams_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/select-schedule/select_schedule_bloc.dart';
import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/verify_show_schedule_exams/verify_show_schedule_exams_bloc.dart';
import 'package:cliente_minha_unimed/bloc/favorites/favorites_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/controle-animacao-negociacao/controle_animacao_negociacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/controle-animacao-status/controle_animacao_status_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/debito-em-conta/debito_em_conta_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/fatura-email/fatura_email_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/fatura/fatura_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/negociacao-debito/negociacoes_debitos_bloc.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/reajust-negotiation/readjust_negotiation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/full-health-attention/full_health_attention_bloc.dart';
import 'package:cliente_minha_unimed/bloc/general_config/general_config_bloc.dart';
import 'package:cliente_minha_unimed/bloc/insecurance-screen/insecurance-bloc.dart';
import 'package:cliente_minha_unimed/bloc/login_by_toten/login_by_toten_bloc.dart';
import 'package:cliente_minha_unimed/bloc/login_permission/login_permission_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/legend_medical_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/pin_map_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/tab_bloc.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/offline/offline_bloc.dart';
import 'package:cliente_minha_unimed/bloc/opcionais/opcional_bloc.dart';
import 'package:cliente_minha_unimed/bloc/order-buttons-home/order-buttons_bloc.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pa-virtual/list-consultations/list_ve_consultations_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pa-virtual/pa_virtual_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pa-virtual/verify-time/verify_time_pa_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pdf-by-url/pdf_by_url_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pdf-factory/pdf_factory_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/portal-noticias/noticia/noticia_bloc.dart';
import 'package:cliente_minha_unimed/bloc/portal-noticias/noticias/noticias_bloc.dart';
import 'package:cliente_minha_unimed/bloc/privacy-policy/privacy_policy_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/change_profile_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/contacts/contacts_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/contacts/register_contact_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/profile_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-preview/profile_preview_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/attendance/res_attendance_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/exam-results/detail/res_exam_result_detail_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/exam-results/exam-results_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/alerts/alerts_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/allergies/allergies_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/consent-term/res_consent_term_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/diagnostic/diagnostic_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/documentos/document_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicator-data/indicators_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicators_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/res_procedures_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/data-page/schedule_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/provider-page/schedule_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/provider-page/scheduling_consultation_provider_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/scheduling_consultation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/scheduling-consultation/speciality-page/specialty_bloc.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/teleconsultation/teleconsultation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/totem/totem_bloc.dart';
import 'package:cliente_minha_unimed/bloc/update-version/update_version_bloc.dart';
import 'package:cliente_minha_unimed/bloc/virtual-card/token/virtual_token_bloc.dart';
import 'package:cliente_minha_unimed/bloc/virtual-card/virtual_card/virtual_card_bloc.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/consulta-virtual.api.dart';
import 'package:cliente_minha_unimed/shared/api/auth-token.api.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/exams/list_exams.api.dart';
import 'package:cliente_minha_unimed/shared/api/pdf.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/button-chat.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/res/procedures/detail/res_procedures_detail_bloc.dart';

class BlocsLoad extends StatelessWidget {
  final Widget child;

  final GuideSolicitationBloc guideSolicitationBloc = GuideSolicitationBloc();
  final RegisterContactBloc registerContactBloc = RegisterContactBloc(
      beneficiaryApi: Locator.instance.get<BeneficiarioApi>());
  final ContactsBloc contactsBloc =
      ContactsBloc(beneficiaryApi: Locator.instance.get<BeneficiarioApi>());
  final VirtualCardTokenBloc virtualCardTokenBloc = VirtualCardTokenBloc();
  final SolicitacaoBloc solicitacaoBloc = SolicitacaoBloc();
  final AuthorizationDocumentsBloc authorizationDocumentsBloc =
      AuthorizationDocumentsBloc();

  BlocsLoad({required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<UpdateVersionBloc>(
            create: (context) => UpdateVersionBloc()),
        BlocProvider<NoticiasBloc>(create: (context) => NoticiasBloc()),
        BlocProvider<NoticiaBloc>(create: (context) => NoticiaBloc()),
        BlocProvider<UserBloc>(create: (context) => UserBloc()),
        BlocProvider<PerfilBloc>(create: (context) => PerfilBloc()),
        BlocProvider<CadastroEnvioBloc>(
            create: (context) => CadastroEnvioBloc()),
        BlocProvider<ContratoBloc>(create: (context) => ContratoBloc()),
        BlocProvider<VirtualCardBloc>(create: (context) => VirtualCardBloc()),
        BlocProvider<MedicalGuideBloc>(create: (context) => MedicalGuideBloc()),
        BlocProvider<AutorizacaoAttachmentsBloc>(
            create: (context) => AutorizacaoAttachmentsBloc()),
        BlocProvider<AutorizacoesEnvioBloc>(
            create: (context) => AutorizacoesEnvioBloc()),
        BlocProvider<PinMapGuiaMedicoBloc>(
            create: (context) => PinMapGuiaMedicoBloc()),
        BlocProvider<LegendMedicalBloc>(
            create: (context) => LegendMedicalBloc()),
        BlocProvider<NegociacoesDebitosBloc>(
            create: (context) => NegociacoesDebitosBloc()),
        BlocProvider<ControleAnimacaoNegociacaoBloc>(
            create: (context) => ControleAnimacaoNegociacaoBloc()),
        BlocProvider<ControleAnimacaoStatusBloc>(
            create: (context) => ControleAnimacaoStatusBloc()),
        BlocProvider<EvaButtonBloc>(create: (context) => EvaButtonBloc()),
        BlocProvider<EvaMessagesBloc>(create: (context) => EvaMessagesBloc()),
        BlocProvider<FaturaBloc>(create: (context) => FaturaBloc()),
        BlocProvider<ControlScreenBloc>(
          create: (context) => ControlScreenBloc(),
        ),
        BlocProvider<BannersBloc>(
          create: (context) => BannersBloc(),
        ),
        BlocProvider<AgendamentoBloc>(
          create: (context) => AgendamentoBloc(),
        ),
        BlocProvider<EspecialidadeBloc>(
          create: (context) => EspecialidadeBloc(),
        ),
        BlocProvider<TeleconsultaBloc>(
          create: (context) => TeleconsultaBloc(),
        ),
        BlocProvider<NotificacaoBloc>(create: (context) => NotificacaoBloc()),
        BlocProvider<ConfirmarAgendamentoBloc>(
            create: (context) => ConfirmarAgendamentoBloc()),
        BlocProvider<CancelarAgendamentoBloc>(
            create: (context) => CancelarAgendamentoBloc()),
        BlocProvider<VerificarPendenciaBloc>(
            create: (context) => VerificarPendenciaBloc()),
        BlocProvider<ChatPendenciaBloc>(
            create: (context) => ChatPendenciaBloc()),
        BlocProvider<ControleAnimacaoConsultaBloc>(
            create: (context) => ControleAnimacaoConsultaBloc()),
        BlocProvider<ConsultaBloc>(create: (context) => ConsultaBloc()),
        BlocProvider<OpcionalBloc>(create: (context) => OpcionalBloc()),
        BlocProvider<TabBloc>(create: (context) => TabBloc()),
        BlocProvider<EvaluationBloc>(create: (context) => EvaluationBloc()),
        BlocProvider<FavoriteBloc>(create: (context) => FavoriteBloc()),
        BlocProvider<ExamBloc>(create: (context) => ExamBloc()),
        BlocProvider<PdfExamBloc>(create: (context) => PdfExamBloc()),
        BlocProvider<AlterarEnderecoBloc>(
            create: (context) => AlterarEnderecoBloc()),
        BlocProvider<BuscarEnderecosBloc>(
            create: (context) => BuscarEnderecosBloc()),
        BlocProvider<AcceptedSolicBloc>(
            create: (context) => AcceptedSolicBloc()),
        BlocProvider<PrivacyPolicyBloc>(
            create: (context) => PrivacyPolicyBloc()),
        BlocProvider<ReadjustNegotiationBloc>(
          create: (context) => ReadjustNegotiationBloc(),
        ),
        BlocProvider<VerifyTimePaBloc>(create: (context) => VerifyTimePaBloc()),
        BlocProvider<FullHealthAttentionBloc>(
            create: (context) => FullHealthAttentionBloc(
                  conectaSaudeApi:
                      Locator.instance.get<ConsultaVirtualAgendamentoApi>(),
                )),
        BlocProvider<PAVirtualBloc>(create: (context) => PAVirtualBloc()),
        BlocProvider<ListExamsBloc>(
            create: (context) => ListExamsBloc(
                  listExamsApi: Locator.instance.get<ListExamsApi>(),
                )),
        BlocProvider<PdfByUrlBloc>(
            create: (context) => PdfByUrlBloc(
                  pdfApi: Locator.instance.get<PdfApi>(),
                )),
        BlocProvider<AuthTokenBloc>(
          create: (context) => AuthTokenBloc(
            authTokenApi: Locator.instance.get<AuthTokenApi>(),
          ),
        ),
        BlocProvider<ListVeConsultationsBloc>(
            create: (context) => ListVeConsultationsBloc(
                  agendamentoApi: Locator.instance.get<AgendamentoApi>(),
                )),
        BlocProvider<CadastroEnvioBloc>(
            create: (context) => CadastroEnvioBloc()),
        BlocProvider<ChannelEthicsBloc>(
            create: (context) => ChannelEthicsBloc()),
        BlocProvider<FaturaEmailBloc>(create: (context) => FaturaEmailBloc()),
        BlocProvider<ProfileDataBloc>(
            create: (context) => ProfileDataBloc(
                beneficiaryApi: Locator.instance.get<BeneficiarioApi>())),
        BlocProvider<TeleconsultationBloc>(
            create: (context) => TeleconsultationBloc()),
        BlocProvider<CheckinEmergencyBloc>(
            create: (context) => CheckinEmergencyBloc()),
        BlocProvider<CheckinPageSympthonsPainsBloc>(
            create: (context) => CheckinPageSympthonsPainsBloc()),
        BlocProvider<CheckinInfoBloc>(create: (context) => CheckinInfoBloc()),
        BlocProvider<CheckinLaboratoryBloc>(
            create: (context) => CheckinLaboratoryBloc()),
        BlocProvider<DebitAccountBloc>(create: (context) => DebitAccountBloc()),
        BlocProvider<ProviderSolicitationBloc>(
            create: (context) => ProviderSolicitationBloc()),
        BlocProvider<PdfFactoryBloc>(create: (context) => PdfFactoryBloc()),
        BlocProvider<CheckinQrCodeBloc>(
            create: (context) => CheckinQrCodeBloc()),
        BlocProvider<ListImagesBloc>(create: (context) => ListImagesBloc()),
        BlocProvider<GuideBloc>(create: (context) => GuideBloc()),
        BlocProvider<ExamGuideBloc>(create: (context) => ExamGuideBloc()),
        BlocProvider<SchedulingConsultationBloc>(
            create: (context) => SchedulingConsultationBloc()),
        BlocProvider<ScheduleDataBloc>(create: (context) => ScheduleDataBloc()),
        BlocProvider<ProviderScheduleBloc>(
            create: (context) => ProviderScheduleBloc()),
        BlocProvider<SpecialtyBloc>(create: (context) => SpecialtyBloc()),
        BlocProvider<CheckinLaboratoryMyDataBloc>(
            create: (context) => CheckinLaboratoryMyDataBloc()),
        BlocProvider<SchedulingProviderBloc>(
            create: (context) => SchedulingProviderBloc()),
        BlocProvider<CheckinSurgeryBloc>(
            create: (context) => CheckinSurgeryBloc()),
        BlocProvider<CheckinSurgeryMyDataBloc>(
            create: (context) => CheckinSurgeryMyDataBloc()),
        BlocProvider<CheckinSurgeryDocumentsBloc>(
            create: (context) => CheckinSurgeryDocumentsBloc()),
        BlocProvider<CheckinSurgeryInfoBloc>(
            create: (context) => CheckinSurgeryInfoBloc()),
        BlocProvider<LoadInfoBloc>(create: (context) => LoadInfoBloc()),
        BlocProvider<CheckinSurgeryStatusDocumentsBloc>(
            create: (context) => CheckinSurgeryStatusDocumentsBloc()),
        BlocProvider<CheckinSurgeryInformationalDocumentsBloc>(
            create: (context) => CheckinSurgeryInformationalDocumentsBloc()),
        BlocProvider<VerifyCheckinBloc>(
            create: (context) => VerifyCheckinBloc()),
        BlocProvider<CreateNewBloc>(create: (context) => CreateNewBloc()),
        BlocProvider<VerifyCheckinSurgeryBloc>(
            create: (context) => VerifyCheckinSurgeryBloc()),
        BlocProvider<ScheduleExamsBloc>(
            create: (context) => ScheduleExamsBloc()),
        BlocProvider<ScheduleExamsMyDataBloc>(
            create: (context) => ScheduleExamsMyDataBloc()),
        BlocProvider<ScheduleExamsGuideBloc>(
            create: (context) => ScheduleExamsGuideBloc()),
        BlocProvider<ScheduleExamCreateBloc>(
            create: (context) => ScheduleExamCreateBloc()),
        BlocProvider<SelectScheduleBloc>(
            create: (context) => SelectScheduleBloc()),
        BlocProvider<ScheduleExamsAdressBloc>(
            create: (context) => ScheduleExamsAdressBloc()),
        BlocProvider<ScheduleExamsQuestionaryBloc>(
            create: (context) => ScheduleExamsQuestionaryBloc()),
        BlocProvider<GuiasBloc>(create: (context) => GuiasBloc()),
        BlocProvider<DetalheTimelineGuiaBloc>(
            create: (context) => DetalheTimelineGuiaBloc()),
        BlocProvider<DetalheBloc>(create: (context) => DetalheBloc()),
        BlocProvider<ChangeProfileDataBloc>(
          create: (context) => ChangeProfileDataBloc(
            beneficiaryApi: Locator.instance.get<BeneficiarioApi>(),
          ),
        ),
        BlocProvider<DisableAccountBloc>(
            create: (context) => DisableAccountBloc()),
        BlocProvider<CheckinSurgeryLatexBloc>(
            create: (context) => CheckinSurgeryLatexBloc()),
        BlocProvider<ScheduleExamsVerifyBloc>(
            create: (context) => ScheduleExamsVerifyBloc()),
        BlocProvider<LoginByTotenBloc>(create: (context) => LoginByTotenBloc()),
        BlocProvider<LoginPermissionBloc>(
            create: (context) => LoginPermissionBloc()),
        BlocProvider<AccessionNotificationBloc>(
            create: (context) => AccessionNotificationBloc()),
        BlocProvider<CadastralUpdateBloc>(
            create: (context) => CadastralUpdateBloc()),
        BlocProvider<GeneralConfigBloc>(
            create: (context) => GeneralConfigBloc()),
        BlocProvider<OfflineBloc>(create: (context) => OfflineBloc()),
        BlocProvider<AuthorizationDocumentsBloc>.value(
            value: authorizationDocumentsBloc),
        BlocProvider<SolicitacaoBloc>(create: (context) => solicitacaoBloc),
        BlocProvider<VirtualCardTokenBloc>.value(value: virtualCardTokenBloc),
        BlocProvider<ContactsBloc>.value(value: contactsBloc),
        BlocProvider<RegisterContactBloc>.value(value: registerContactBloc),
        BlocProvider<GuideSolicitationBloc>.value(value: guideSolicitationBloc),
        BlocProvider<ImageExamScheduleHistoryBloc>(
            create: (context) => ImageExamScheduleHistoryBloc()),
        BlocProvider<TotemBloc>(create: (context) => TotemBloc()),
        BlocProvider<SensitiveDataCubit>(
            create: (context) => SensitiveDataCubit()),
        BlocProvider<ProfilePreviewBloc>(
            create: (context) => ProfilePreviewBloc()),
        BlocProvider<ResAllergyBloc>(create: (context) => ResAllergyBloc()),
        BlocProvider<ResConfigBloc>(create: (context) => ResConfigBloc()),
        BlocProvider<ResExamResultsBloc>(
            create: (context) => ResExamResultsBloc()),
        BlocProvider<ResBrazilAttendancesBloc>(
            create: (context) => ResBrazilAttendancesBloc()),
        BlocProvider<ResExamResultDetailBloc>(
            create: (context) => ResExamResultDetailBloc()),
        BlocProvider<ResIndicatorBloc>(create: (context) => ResIndicatorBloc()),
        BlocProvider<ResIndicatorsDataBloc>(
            create: (context) => ResIndicatorsDataBloc()),
        BlocProvider<ResConsentBloc>(create: (context) => ResConsentBloc()),
        BlocProvider<ResAlertsBloc>(create: (context) => ResAlertsBloc()),
        BlocProvider<PendingIssuesBloc>(
            create: (context) => PendingIssuesBloc()),
        BlocProvider<ResDiagnosticBloc>(
            create: (context) => ResDiagnosticBloc()),
        BlocProvider<ClubMaisVantagensBloc>(
            create: (context) => ClubMaisVantagensBloc()),
        BlocProvider<ResProceduresBloc>(
            create: (context) => ResProceduresBloc()),
        BlocProvider<ResProcedureDetailBloc>(
            create: (context) => ResProcedureDetailBloc()),
        BlocProvider<ResBrazilDocumentsBloc>(
            create: (context) => ResBrazilDocumentsBloc()),
        BlocProvider<OrderButtonsHomeBloc>(
            create: (context) => OrderButtonsHomeBloc()),
        BlocProvider<InsuranceBloc>(create: (context) => InsuranceBloc()),
        BlocProvider<BeneficiaryProtocolBloc>(
            create: (context) => BeneficiaryProtocolBloc()),
      ],
      child: child,
    );
  }
}
