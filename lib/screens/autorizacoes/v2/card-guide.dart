import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BeneficiaryProtocolCardGuide extends StatefulWidget {
  final BeneficiaryProtocolModel beneficiaryProtocol;
  final bool isLoading;

  BeneficiaryProtocolCardGuide({
    required this.beneficiaryProtocol,
    required this.isLoading,
  });

  @override
  State<BeneficiaryProtocolCardGuide> createState() =>
      _BeneficiaryProtocolCardGuideState();
}

class _BeneficiaryProtocolCardGuideState
    extends BaseState<BeneficiaryProtocolCardGuide> {
  final _baseTranslate = 'autorizations.v2.card';

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _header(widget.beneficiaryProtocol.protocolType),
            Divider(
              height: 15,
              thickness: 1,
            ),
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _rowInfo(
                    title: translate("$_baseTranslate.requestName"),
                    value: widget.beneficiaryProtocol.requesterName ?? "",
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _rowInfo(
                    title: translate("$_baseTranslate.requestDate"),
                    value: widget.beneficiaryProtocol.dataHoraFormat,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _rowInfo(
                    title: translate("$_baseTranslate.protocol"),
                    value: widget.beneficiaryProtocol.ansProtocolNumberHistory
                        .ansProtocolNumber,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _rowInfo(
                    title: translate("$_baseTranslate.status"),
                    value: widget.beneficiaryProtocol.status,
                    color: statusColor(widget.beneficiaryProtocol.status),
                    isBold: true,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}

Widget _header(String protocolType) {
  return Row(
    children: [
      Container(
        decoration: BoxDecoration(
          color: UnimedColors.greenDark2,
          shape: BoxShape.circle,
        ),
        padding: EdgeInsets.all(6),
        child: Icon(
          Icons.calendar_today_outlined,
          color: UnimedColors.greenDark,
          size: 16,
        ),
      ),
      SizedBox(width: 4),
      Container(
          decoration: BoxDecoration(
            color: protocolTypeColorBackground(protocolType),
            borderRadius: BorderRadius.circular(4),
          ),
          padding: EdgeInsets.all(2),
          child: Text(protocolType,
              style: TextStyle(
                color: protocolTypeColor(protocolType),
              ))),
    ],
  );
}

Widget _rowInfo(
    {required String title,
    required String value,
    Color? color,
    bool isBold = false}) {
  return value.isEmpty
      ? Container()
      : Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "$title:",
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            Expanded(
              child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return Text(
                      state.isSensitiveDataVisible
                          ? StringUtils.formahideDatatMessage(value)
                          : value,
                      textAlign: TextAlign.end,
                      style: TextStyle(
                          color: color ?? UnimedColors.grayDark,
                          fontWeight:
                              isBold ? FontWeight.bold : FontWeight.normal));
                },
              ),
            ),
          ],
        );
}

Color protocolTypeColorBackground(String protocolType) {
  switch (protocolType.trim()) {
    case "Autorização":
      return UnimedColors.cream;
    case "Agendamento":
      return UnimedColors.greenLight6;
    default:
      return UnimedColors.redLight;
  }
}

Color protocolTypeColor(String protocolType) {
  switch (protocolType.trim()) {
    case "Autorização":
      return UnimedColors.creamDark;
    case "Agendamento":
      return UnimedColors.greenDark3;
    default:
      return UnimedColors.redDark;
  }
}

Color statusColor(String status) {
  switch (status.trim()) {
    case "Em andamento":
      return UnimedColors.yellowDark;
    case "Solicitado":
      return UnimedColors.yellowDark;
    case "Com pendências":
      return UnimedColors.red;
    case "Autorizado":
      return UnimedColors.greenLight7;
    default:
      return UnimedColors.blackText;
  }
}
