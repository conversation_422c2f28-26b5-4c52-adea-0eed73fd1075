import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_bloc.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_event.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_state.dart';
import 'package:cliente_minha_unimed/bloc/beneficiary-protocol/beneficiary_protocol_bloc.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/panel-date-filter.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/solicitacao/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/card-guide.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/scale.transition.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/router-observer.dart';
import 'package:cliente_minha_unimed/shared/utils/sensitive-data-button.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-evaluation.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/eva-wpp-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/change-profile/change-profile-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/info.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste.dart';
import 'package:cliente_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';

const defaultTimeSearchAuthorizationInMonth = 3;

class ProtocolsScreen extends StatefulWidget {
  @override
  _ProtocolsScreenState createState() => _ProtocolsScreenState();
}

class _ProtocolsScreenState extends BaseState<ProtocolsScreen> with RouteAware {
  bool filtersPanelOpened = false;
  FocusNode? focusTextFieldSearch;
  List<String> _filterTags = [];
  List<String> _filterStatus = [];
  List<BeneficiaryProtocolModel>? filtrados = List.empty(growable: true);
  DateTimeRange? _dateRangeToFilter;

  final _baseTranslate = 'autorizations.v2';

  void _loadProtocols() =>
      BlocProvider.of<BeneficiaryProtocolBloc>(context).add(
        GetBeneficiaryProtocol(
            profile: BlocProvider.of<PerfilBloc>(context).perfil,
            dateTimeRange: _dateRangeToFilter),
      );
  void _filterProtocols() =>
      BlocProvider.of<BeneficiaryProtocolBloc>(context).add(
        FilterBeneficiaryProtocol(
          tags: _filterTags,
          status: _filterStatus,
        ),
      );

  @override
  void initState() {
    super.initState();

    _loadProtocols();

    focusTextFieldSearch = FocusNode();

    AnalyticsService().addLogScreenView(
      screenName: 'ProtocolsScreen',
      screenClass: 'ProtocolsScreen',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();
    filtrados?.clear();
    super.dispose();
  }

  // _scrollListener() {
  //   setState(() {
  //     _scrollPosition = _scrollController.position.pixels;
  //   });
  // }

  void didPopNext() {
    debugPrint('Voltou para a tela');
    final evalutionValues =
        BlocProvider.of<UserBloc>(context).user.config.evalution;
    BlocProvider.of<EvaluationBloc>(context).add(CheckEvaluationEvent(
      enable: evalutionValues!.enabled,
      minutes: evalutionValues.delay,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<PerfilBloc, PerfilState>(listener: (context, state) {
          if (state is LoadedProfileState) {
            _loadProtocols();
          }
        }),
        BlocListener<EvaluationBloc, EvaluationState>(
          listener: (context, state) {
            if (state is NeededShowEvaluationState) {
              AlertEvaluation.exibeAlert(
                context: context,
                servico: AvaliacaoLabels.AUTORIZACAO,
              );
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: UnimedColors.greyBackground,
        appBar: AppBarUnimed(
          title: BlocBuilder<BeneficiaryProtocolBloc, BeneficiaryProtocolState>(
              builder: (context, state) {
            int numSolicitacoes = 0;

            if (state is LoadedBeneficiaryProtocolState) {
              numSolicitacoes = state.protocols.length;
            }

            return FittedBox(
              child: Column(
                children: [
                  Text(
                    filtersPanelOpened
                        ? 'Filtrado ($numSolicitacoes)'
                        : 'Autorizações (${filtrados?.length})',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                      builder: (context, state) {
                    return AutoSizeText(
                      state.isSensitiveDataVisible
                          ? StringUtils.formahideDatatMessage(
                              BlocProvider.of<PerfilBloc>(context)
                                  .perfil
                                  .nomeBeneficiario,
                            )
                          : BlocProvider.of<PerfilBloc>(context)
                                  .perfil
                                  .nomeBeneficiario ??
                              '',
                      minFontSize: 10,
                      maxLines: 1,
                      style: TextStyle(fontSize: 10),
                    );
                  }),
                ],
              ),
            );
          }),
          backgroundColor: UnimedColors.green,
          actions: <Widget>[
            SensitibeDataButton(),
            ChangeProfileButton(),
          ],
        ),
        body: Column(
          children: <Widget>[
            _filters(),
            AnimatedContainer(
              height: _dateRangeToFilter != null ? 70 : 0,
              curve: Curves.easeIn,
              duration: Duration(milliseconds: 300),
              child: _dateRangeToFilter != null
                  ? PanelDateFilter(
                      dateStartFilter:
                          _dateRangeToFilter?.start ?? DateTime.now(),
                      dateEndFilter: _dateRangeToFilter?.end ?? DateTime.now(),
                    )
                  : const Center(),
            ),

            // Lista de solicitacoes
            Expanded(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: BlocBuilder<BeneficiaryProtocolBloc,
                      BeneficiaryProtocolState>(
                    builder: (context, state) {
                      if (state is LoadedBeneficiaryProtocolState) {
                        return _listAutorizacao(state.protocols);
                      } else if (state is LoadingBeneficiaryProtocolState) {
                        return EvaLoading();
                      } else if (state is NoDataBeneficiaryProtocolState) {
                        return EvaInfo.EvaInfo(
                          message: "Você não tem histórico de autorizações",
                          onTap: null,
                        );
                      } else if (state is ErrorBeneficiaryProtocolState) {
                        return RefreshIndicator(
                          onRefresh: () async {
                            _loadProtocols();
                          },
                          child: ListView(
                            padding: const EdgeInsets.only(top: 8.0),
                            physics: ClampingScrollPhysics(
                              parent: AlwaysScrollableScrollPhysics(),
                            ),
                            children: [
                              EvaTriste(
                                message: Text(
                                  state.message,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        );
                      } else {
                        return Container();
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Navigator.push(
              context,
              ScaleRoute(
                page: BlocProvider(
                  create: (context) => VerifySolicBloc(),
                  child: NovaSolicitacaoScreen(
                    fromAlert: false,
                  ),
                ),
              ),
            );
          },
          backgroundColor: UnimedColors.green,
          child: Icon(Icons.plus_one),
        ),
      ),
    );
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
        context: context,
        helpText: helpText,
        initialDate: DateTime.now(),
        firstDate: firstDate ?? DateTime.now().subtract(Duration(days: 365)),
        lastDate: DateTime.now(),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              textSelectionTheme: TextSelectionThemeData(
                selectionColor: UnimedColors.green,
                cursorColor: UnimedColors.blackText,
                selectionHandleColor: UnimedColors.green,
              ),
              inputDecorationTheme: InputDecorationTheme(
                labelStyle: TextStyle(
                  color: UnimedColors.green,
                ),
                hintStyle: TextStyle(
                  color: UnimedColors.blackText,
                ),
                filled: true,
                fillColor: UnimedColors.white,
                contentPadding: EdgeInsets.symmetric(horizontal: 12.0),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: UnimedColors.green,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: UnimedColors.green, width: 2.0),
                ),
              ),
            ),
            child: child!,
          );
        });
  }

  Widget _filters() {
    return BlocBuilder<BeneficiaryProtocolBloc, BeneficiaryProtocolState>(
      builder: (context, state) {
        if (state is LoadedBeneficiaryProtocolState) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    _openFilterModal(context, state.listTags, state.listStatus);
                  },
                  child: Container(
                    padding:
                        EdgeInsets.only(top: 4, bottom: 4, left: 24, right: 8),
                    decoration: BoxDecoration(
                      color: UnimedColors.greenDark,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(6),
                        topLeft: Radius.circular(6),
                        bottomLeft: Radius.circular(6),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          translate('$_baseTranslate.filters'),
                          style: TextStyle(
                            color: UnimedColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.filter_alt_outlined,
                            color: UnimedColors.white),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _dateRangeToFilter == null
                        ? Icons.date_range
                        : Icons.clear_outlined,
                  ),
                  color: UnimedColors.greenDark,
                  onPressed: _dateRangeToFilter != null
                      ? () {
                          setState(() {
                            _dateRangeToFilter = null;
                          });
                          _loadProtocols();
                        }
                      : () async {
                          DateTime? dateStart;
                          DateTime? dateEnd;
                          dateStart = await _selectDateToFilter(
                            context,
                            'Selecione a data inicial',
                          );
                          if (dateStart != null) {
                            dateEnd = await _selectDateToFilter(
                              context,
                              'Selecione a data final',
                              firstDate: dateStart,
                            );
                          }
                          if (dateStart != null && dateEnd != null) {
                            setState(() {
                              _dateRangeToFilter = DateTimeRange(
                                start: dateStart!,
                                end: dateEnd!,
                              );
                            });
                            if (dateStart.isBefore(dateEnd)) {
                              _loadProtocols();
                            } else {
                              _showMessage(
                                  'A data inicial deve ser anterior a data final');
                            }
                          }
                        },
                ),
              ],
            ),
          );
        }
        return Container();
      },
    );
  }

  _openFilterModal(
      BuildContext context, List<String> tags, List<String> status) {
    showModalBottomSheet<void>(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setModalState) {
            return SafeArea(
                child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24.0, vertical: 16.0),
                    color: UnimedColors.white,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                icon: Icon(Icons.close),
                                color: UnimedColors.greenLight5,
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                              ),
                            ],
                          ),
                          Text(translate('$_baseTranslate.filterHeader'),
                              style: TextStyle(
                                  fontSize: 22, color: UnimedColors.blackText)),
                          SizedBox(height: 16),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: tags.length,
                            itemBuilder: (context, index) {
                              return checkBoxItem(
                                title: tags[index],
                                value: _filterTags.contains(tags[index]),
                                color: protocolTypeColor(tags[index]),
                                colorBackground:
                                    protocolTypeColorBackground(tags[index]),
                                onChanged: (value) {
                                  setModalState(() {
                                    if (value == true) {
                                      _filterTags.add(tags[index]);
                                    } else {
                                      _filterTags.remove(tags[index]);
                                    }
                                  });
                                  //_loadProtocols();
                                },
                              );
                            },
                          ),
                          Divider(
                            height: 10,
                            thickness: 1,
                            color: UnimedColors.greyDisabled,
                          ),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: status.length,
                            itemBuilder: (context, index) {
                              return checkBoxItem(
                                title: status[index],
                                value: _filterStatus.contains(status[index]),
                                color: statusColor(status[index]),
                                onChanged: (value) {
                                  setModalState(() {
                                    if (value == true) {
                                      _filterStatus.add(status[index]);
                                    } else {
                                      _filterStatus.remove(status[index]);
                                    }
                                  });
                                },
                              );
                            },
                          ),
                          SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: UnimedColors.greenLight5,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                onPressed: () {
                                  _filterProtocols();
                                  Navigator.pop(context);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      top: 8.0,
                                      bottom: 8.0,
                                      left: 24.0,
                                      right: 24.0),
                                  child:
                                      Text(translate('$_baseTranslate.filter'),
                                          style: TextStyle(
                                            color: UnimedColors.white,
                                            fontSize: 18,
                                          )),
                                ),
                              ),
                            ],
                          )
                        ])));
          });
        });
  }

  Widget _listAutorizacao(List<BeneficiaryProtocolModel> _list) {
    filtrados = _list;

    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(left: 8.0, right: 8.0),
            child: BlocProvider<SolicitacaoBloc>(
              create: (context) => SolicitacaoBloc(),
              child: BlocConsumer<SolicitacaoBloc, SolicitacaoState>(
                listener: (context, state) {
                  if (state is ErrorSolicitacaoState) {
                    Alert.open(
                      context,
                      title: 'Atenção',
                      text: state.message,
                    );
                  } else if (state is DoneGetSolicitacaoState) {
                    if ((state.solicitacao.pedidosAutorizados ?? []).isNotEmpty ||
                        (state.solicitacao.pedidosEmAndamento ?? [])
                            .isNotEmpty ||
                        (state.solicitacao.pedidosIndeferidos ?? [])
                            .isNotEmpty ||
                        (state.solicitacao.etapas ?? []).isNotEmpty) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SolicitacaoScreen(
                            solicitacao: state.solicitacao,
                          ),
                        ),
                      );
                    } else {
                      _showModal(state.solicitacao);
                    }
                  }
                },
                builder: (context, state) {
                  return RefreshIndicator(
                      onRefresh: () async {
                        _loadProtocols();
                      },
                      child: ListView.builder(
                        physics: ClampingScrollPhysics(
                            parent: AlwaysScrollableScrollPhysics()),
                        itemCount: _list.length,
                        itemBuilder: (context, index) {
                          //Criado contexto do PdfBloc aqui para evitar varios alerts em caso de erro por causa da listagem
                          return BeneficiaryProtocolCardGuide(
                              beneficiaryProtocol: _list.elementAt(index),
                              isLoading: state is LoadingSolicitacaoState);
                        },
                      ));
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showModal(RetornoSolicitacaoModel solicitacao) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          content: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: SingleChildScrollView(
              child: Stack(
                children: [
                  Positioned(
                    right: 0,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Icon(Icons.close_outlined),
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 12,
                      ),
                      _itemView(
                        title: 'Protocolo',
                        description: '${solicitacao.numProtocolo}',
                        isSelectableText: true,
                      ),
                      Divider(
                        thickness: 1,
                      ),
                      _itemView(
                        title: 'Data da solicitação',
                        description: '${solicitacao.dataSolicitacaoFormatted}',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            EvaWppButton(protocolo: solicitacao.numProtocolo ?? "")
            //_btnWhatsApp(context),
          ],
        );
      },
    );
  }

  Widget checkBoxItem({
    required String title,
    required bool value,
    required Color color,
    Color? colorBackground,
    required Function(bool?) onChanged,
  }) {
    return InkWell(
      onTap: () {
        debugPrint('onTap: $title');
        debugPrint('onTap: ${!value}');
        onChanged(!value);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: UnimedColors.greenDark,
          ),
          Container(
              decoration: BoxDecoration(
                color: colorBackground,
                borderRadius: BorderRadius.circular(4),
              ),
              padding: EdgeInsets.all(2),
              child: Text(title,
                  style: TextStyle(
                    color: color,
                    fontWeight: colorBackground == null
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ))),
        ],
      ),
    );
  }

  Widget _itemView({
    required String title,
    required String description,
    bool isAlignEnd = false,
    bool isSelectableText = false,
  }) {
    return Column(
      crossAxisAlignment:
          isAlignEnd ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: UnimedColors.greyCard,
            fontSize: 12,
          ),
          textAlign: TextAlign.start,
        ),
        isSelectableText
            ? SelectableText(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              )
            : Text(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              ),
      ],
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        Snack.success(
          message,
          duration: Duration(seconds: 3),
        ),
      );
    });
  }
}
