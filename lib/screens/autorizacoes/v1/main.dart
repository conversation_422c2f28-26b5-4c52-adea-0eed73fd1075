import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/pdf_exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/guias_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_bloc.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_event.dart';
import 'package:cliente_minha_unimed/bloc/avaliacao/avaliacao_state.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/card-guide.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/main.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/panel-date-filter.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/solicitacao/main.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/scale.transition.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/router-observer.dart';
import 'package:cliente_minha_unimed/shared/utils/sensitive-data-button.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-evaluation.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/eva-wpp-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/change-profile/change-profile-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/info.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste.dart';
import 'package:cliente_minha_unimed/shared/widgets/pdf-view/pdf-view-screen.dart';
import 'package:cliente_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

const defaultTimeSearchAuthorizationInMonth = 3;

class AutorizacoesScreen extends StatefulWidget {
  @override
  _AutorizacoesScreenState createState() => _AutorizacoesScreenState();
}

class _AutorizacoesScreenState extends State<AutorizacoesScreen>
    with RouteAware {
  bool filtersPanelOpened = false;
  FocusNode? focusTextFieldSearch;
  bool _isLoading = false;
  int _filterMonthSelected = 3;
  List<GuiaModel>? filtrados = List.empty(growable: true);
  DateTimeRange? _dateRangeToFilter;
  final _lastMonthsToFilter = [3, 6];

  void _carregarGuias() => BlocProvider.of<GuiasBloc>(context).add(
        CarregarGuiasEvent(
          perfil: BlocProvider.of<PerfilBloc>(context).perfil,
          dataInicio: DateTime(
            DateTime.now().year,
            (DateTime.now().month - 3),
            DateTime.now().day,
          ),
          dataFim: DateTime.now(),
        ),
      );

  @override
  void initState() {
    super.initState();

    _carregarGuias();

    focusTextFieldSearch = FocusNode();

    AnalyticsService().addLogScreenView(
      screenName: 'AutorizacoesScreen',
      screenClass: 'AutorizacoesScreen',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();
    filtrados?.clear();
    super.dispose();
  }

  // _scrollListener() {
  //   setState(() {
  //     _scrollPosition = _scrollController.position.pixels;
  //   });
  // }

  void didPopNext() {
    debugPrint('Voltou para a tela');
    final evalutionValues =
        BlocProvider.of<UserBloc>(context).user.config.evalution;
    BlocProvider.of<EvaluationBloc>(context).add(CheckEvaluationEvent(
      enable: evalutionValues!.enabled,
      minutes: evalutionValues.delay,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<PerfilBloc, PerfilState>(listener: (context, state) {
          if (state is LoadedProfileState) {
            _carregarGuias();
          }
        }),
        BlocListener<EvaluationBloc, EvaluationState>(
          listener: (context, state) {
            if (state is NeededShowEvaluationState) {
              AlertEvaluation.exibeAlert(
                context: context,
                servico: AvaliacaoLabels.AUTORIZACAO,
              );
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppBarUnimed(
          title: BlocBuilder<GuiasBloc, GuiasState>(builder: (context, state) {
            int numSolicitacoes = 0;

            if (state is SuccessCarregarGuiaState) {
              numSolicitacoes = state.list.length;
            }

            return FittedBox(
              child: Column(
                children: [
                  Text(
                    filtersPanelOpened
                        ? 'Filtrado ($numSolicitacoes)'
                        : 'Autorizações (${filtrados?.length})',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                      builder: (context, state) {
                    return AutoSizeText(
                      state.isSensitiveDataVisible
                          ? StringUtils.formahideDatatMessage(
                              BlocProvider.of<PerfilBloc>(context)
                                  .perfil
                                  .nomeBeneficiario,
                            )
                          : BlocProvider.of<PerfilBloc>(context)
                                  .perfil
                                  .nomeBeneficiario ??
                              '',
                      minFontSize: 10,
                      maxLines: 1,
                      style: TextStyle(fontSize: 10),
                    );
                  }),
                ],
              ),
            );
          }),
          backgroundColor: UnimedColors.green,
          actions: <Widget>[
            SensitibeDataButton(),
            ChangeProfileButton(),
          ],
        ),
        body: Column(
          children: <Widget>[
            // Campo de busca
            // AnimatedContainer(
            //   height: filtersPanelOpened ? 60 : 0,
            //   curve: Curves.easeIn,
            //   duration: Duration(milliseconds: 300),
            //   child: filtersPanelOpened
            //       ? FiltersPanel(
            //           focusNode: focusTextFieldSearch,
            //           dateToFilter: DateTime.now(),
            //         )
            //       : const Center(),
            // ),
            _filters(),
            AnimatedContainer(
              height: _dateRangeToFilter != null ? 70 : 0,
              curve: Curves.easeIn,
              duration: Duration(milliseconds: 300),
              child: _dateRangeToFilter != null
                  ? PanelDateFilter(
                      dateStartFilter:
                          _dateRangeToFilter?.start ?? DateTime.now(),
                      dateEndFilter: _dateRangeToFilter?.end ?? DateTime.now(),
                    )
                  : const Center(),
            ),

            // Lista de solicitacoes
            Expanded(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: BlocConsumer<GuiasBloc, GuiasState>(
                    listener: (context, state) {
                      if (state is LoadingCarregarGuiaState) {
                        setState(() {
                          _isLoading = true;
                        });
                      } else {
                        setState(() {
                          _isLoading = false;
                        });
                      }
                    },
                    builder: (context, state) {
                      if (state is SuccessCarregarGuiaState) {
                        return _list(state.list, state.listStatus);
                      } else if (state is LoadingCarregarGuiaState) {
                        return EvaLoading();
                      } else if (state is NoDataGuiaState) {
                        return EvaInfo.EvaInfo(
                          message: "Você não tem histórico de autorizações",
                          onTap: null,
                        );
                      } else if (state is NoDataFoundGuiaState) {
                        return EvaInfo.EvaInfo(
                          message:
                              "Nenhuma autorização encontrada para o filtro aplicado",
                          onTap: null,
                        );
                      } else if (state is ErrorCarregarGuiaState) {
                        return RefreshIndicator(
                          onRefresh: () async {
                            _carregarGuias();
                          },
                          child: ListView(
                            padding: const EdgeInsets.only(top: 8.0),
                            physics: ClampingScrollPhysics(
                              parent: AlwaysScrollableScrollPhysics(),
                            ),
                            children: [
                              EvaTriste(
                                message: Text(
                                  state.message,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        );
                      } else {
                        return Container();
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Navigator.push(
              context,
              ScaleRoute(
                page: BlocProvider(
                  create: (context) => VerifySolicBloc(),
                  child: NovaSolicitacaoScreen(
                    fromAlert: false,
                  ),
                ),
              ),
            );
          },
          backgroundColor: UnimedColors.green,
          child: Icon(Icons.plus_one),
        ),
      ),
    );
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
        context: context,
        helpText: helpText,
        initialDate: DateTime.now(),
        firstDate: firstDate ?? DateTime.now().subtract(Duration(days: 365)),
        lastDate: DateTime.now(),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              textSelectionTheme: TextSelectionThemeData(
                selectionColor: UnimedColors.green,
                cursorColor: UnimedColors.blackText,
                selectionHandleColor: UnimedColors.green,
              ),
              inputDecorationTheme: InputDecorationTheme(
                labelStyle: TextStyle(
                  color: UnimedColors.green,
                ),
                hintStyle: TextStyle(
                  color: UnimedColors.blackText,
                ),
                filled: true,
                fillColor: UnimedColors.white,
                contentPadding: EdgeInsets.symmetric(horizontal: 12.0),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: UnimedColors.green,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: UnimedColors.green, width: 2.0),
                ),
              ),
            ),
            child: child!,
          );
        });
  }

  Widget _filters() {
    if (_isLoading) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 30),
        child: SpinKitThreeBounce(
          color: UnimedColors.green,
          size: 20,
        ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 40,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _lastMonthsToFilter.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    int _key = _lastMonthsToFilter.elementAt(index);

                    return _chipFilter(_key);
                  },
                ),
              ),
              IconButton(
                icon: Icon(
                  _dateRangeToFilter == null
                      ? Icons.calendar_month_outlined
                      : Icons.clear_outlined,
                ),
                color: UnimedColors.greenDark,
                onPressed: _dateRangeToFilter != null
                    ? () {
                        setState(() {
                          _dateRangeToFilter = null;
                        });
                        _carregarGuias();
                      }
                    : () async {
                        DateTime? dateStart;
                        DateTime? dateEnd;
                        dateStart = await _selectDateToFilter(
                          context,
                          'Selecione a data inicial',
                        );
                        if (dateStart != null) {
                          dateEnd = await _selectDateToFilter(
                            context,
                            'Selecione a data final',
                            firstDate: dateStart,
                          );
                        }
                        if (dateStart != null && dateEnd != null) {
                          setState(() {
                            _filterMonthSelected = 3;
                            _dateRangeToFilter = DateTimeRange(
                              start: dateStart!,
                              end: dateEnd!,
                            );
                          });
                          if (dateStart.isBefore(dateEnd)) {
                            BlocProvider.of<GuiasBloc>(context).add(
                              CarregarGuiasEvent(
                                perfil:
                                    BlocProvider.of<PerfilBloc>(context).perfil,
                                dataInicio: DateTime(
                                  dateStart.year,
                                  dateStart.month,
                                  dateStart.day,
                                ),
                                dataFim: DateTime(
                                  dateEnd.year,
                                  dateEnd.month,
                                  dateEnd.day,
                                ),
                              ),
                            );
                          } else {
                            _showMessage(
                                'A data inicial deve ser anterior a data final');
                          }
                        }
                      },
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _chipFilter(int filterSelected) {
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: InkWell(
        onTap: () {
          if (_filterMonthSelected != filterSelected) {
            setState(() {
              _filterMonthSelected = filterSelected;
              _dateRangeToFilter = null;
            });
            final dateResult = DateTime(DateTime.now().year,
                (DateTime.now().month - filterSelected), DateTime.now().day);
            BlocProvider.of<GuiasBloc>(context).add(
              CarregarGuiasEvent(
                perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                dataInicio: DateTime(
                  dateResult.year,
                  dateResult.month,
                  dateResult.day,
                ),
              ),
            );
          } else {
            setState(() {
              _filterMonthSelected = 3;
            });
            _carregarGuias();
          }
        },
        child: Chip(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              color: UnimedColors.greenDark,
            ),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10),
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
            ),
          ),
          label: Text(
            'Últimos $filterSelected meses',
            style: TextStyle(
              color: filterSelected == _filterMonthSelected &&
                      _dateRangeToFilter == null
                  ? UnimedColors.grayLight
                  : UnimedColors.greenDark,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: _filterMonthSelected == filterSelected &&
                  _dateRangeToFilter == null
              ? UnimedColors.greenDark
              : UnimedColors.grayLight,
        ),
      ),
    );
  }

  Widget _listAutorizacao(List<GuiaModel> _list) {
    filtrados = _list;

    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(left: 8.0, right: 8.0),
            child: BlocProvider<SolicitacaoBloc>(
              create: (context) => SolicitacaoBloc(),
              child: BlocConsumer<SolicitacaoBloc, SolicitacaoState>(
                listener: (context, state) {
                  setState(() {
                    _isLoading = state is LoadingSolicitacaoState;
                  });
                  if (state is ErrorSolicitacaoState) {
                    Alert.open(
                      context,
                      title: 'Atenção',
                      text: state.message,
                    );
                  } else if (state is DoneGetSolicitacaoState) {
                    if ((state.solicitacao.pedidosAutorizados ?? []).isNotEmpty ||
                        (state.solicitacao.pedidosEmAndamento ?? [])
                            .isNotEmpty ||
                        (state.solicitacao.pedidosIndeferidos ?? [])
                            .isNotEmpty ||
                        (state.solicitacao.etapas ?? []).isNotEmpty) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SolicitacaoScreen(
                            solicitacao: state.solicitacao,
                          ),
                        ),
                      );
                    } else {
                      _showModal(state.solicitacao);
                    }
                  }
                },
                builder: (context, state) {
                  return RefreshIndicator(
                      onRefresh: () async {
                        _carregarGuias();
                      },
                      child: ListView.builder(
                        physics: ClampingScrollPhysics(
                            parent: AlwaysScrollableScrollPhysics()),
                        itemCount: _list.length,
                        itemBuilder: (context, index) {
                          //Criado contexto do PdfBloc aqui para evitar varios alerts em caso de erro por causa da listagem
                          return BlocProvider<PdfExamBloc>(
                              create: (context) => PdfExamBloc(),
                              child: BlocBuilder<PdfExamBloc, ExamState>(
                                buildWhen: (previousState, pdfState) {
                                  if (previousState is LoadingPdf &&
                                      pdfState is DonePdf) {
                                    if (pdfState.codSolicitacao ==
                                        _list.elementAt(index).codPreSolic)
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PDFViewScreen(
                                            pdfState.path,
                                            title:
                                                'Detalhes da guia eletrônica',
                                            isPath: true,
                                          ),
                                        ),
                                      );
                                  } else if (previousState is LoadingPdf &&
                                      pdfState is ErrorPdf) {
                                    Alert.open(
                                      context,
                                      title: 'Ocorreu um erro',
                                      text: pdfState.message,
                                    );
                                  }

                                  return true;
                                },
                                builder: (context, pdfState) {
                                  return AuthorizationCardGuide(
                                    guiaModel: _list.elementAt(index),
                                    isLoading:
                                        state is LoadingSolicitacaoState ||
                                            pdfState is LoadingPdf,
                                  );
                                },
                              ));
                        },
                      ));
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showModal(RetornoSolicitacaoModel solicitacao) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          content: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: SingleChildScrollView(
              child: Stack(
                children: [
                  Positioned(
                    right: 0,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Icon(Icons.close_outlined),
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 12,
                      ),
                      _itemView(
                        title: 'Protocolo',
                        description: '${solicitacao.numProtocolo}',
                        isSelectableText: true,
                      ),
                      Divider(
                        thickness: 1,
                      ),
                      _itemView(
                        title: 'Data da solicitação',
                        description: '${solicitacao.dataSolicitacaoFormatted}',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            EvaWppButton(protocolo: solicitacao.numProtocolo ?? "")
            //_btnWhatsApp(context),
          ],
        );
      },
    );
  }

  Widget _itemView({
    required String title,
    required String description,
    bool isAlignEnd = false,
    bool isSelectableText = false,
  }) {
    return Column(
      crossAxisAlignment:
          isAlignEnd ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: UnimedColors.greyCard,
            fontSize: 12,
          ),
          textAlign: TextAlign.start,
        ),
        isSelectableText
            ? SelectableText(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              )
            : Text(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              ),
      ],
    );
  }

  Widget _list(
    List<GuiaModel> solicitacoes,
    Map<String?, List<GuiaModel>> solicitacoesStatus,
  ) {
    return _listAutorizacao(solicitacoes);
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        Snack.success(
          message,
          duration: Duration(seconds: 3),
        ),
      );
    });
  }
}
