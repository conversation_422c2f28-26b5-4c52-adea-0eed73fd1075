import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/guias_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';

class FiltersPanel extends StatefulWidget {
  final FocusNode? focusNode;
  final DateTime? dateToFilter;

  FiltersPanel({this.focusNode, this.dateToFilter});

  @override
  _FiltersPanelState createState() => _FiltersPanelState();
}

class _FiltersPanelState extends State<FiltersPanel> {
  final TextEditingController _searchText = TextEditingController();
  bool showClearButton = true;

  @override
  void initState() {
    super.initState();
    _searchText.addListener(_onSearchText);

    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _searchText.removeListener(_onSearchText);
    _searchText.dispose();
    super.dispose();
  }

  void _onSearchText() {
    BlocProvider.of<GuiasBloc>(context).add(GuiasFiltro(filter: _searchText.text, dateFilter: widget.dateToFilter));
    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.0),
      child: Container(
        alignment: Alignment.topCenter,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: UnimedColors.green,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
            bottomLeft: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: UnimedColors.grayDark,
              blurRadius: 2.0,
              spreadRadius: 0.0,
              offset: Offset(2.0, 2.0),
            ),
          ],
        ),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 4.0),
          child: TextField(
            controller: _searchText,
            style: TextStyle(
              color: UnimedColors.grayLight,
            ),
            textInputAction: TextInputAction.unspecified,
            focusNode: widget.focusNode,
            cursorColor: UnimedColors.grayLight,
            decoration: InputDecoration(
                hintText: "Buscar...",
                hintStyle: TextStyle(color: UnimedColors.grayLight),
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                suffixIcon: _searchText.text.length > 0
                    ? IconButton(
                        icon: Icon(Icons.clear, color: UnimedColors.grayLight),
                        onPressed: () => _searchText.clear(),
                      )
                    : null,
                prefixIcon: Icon(
                  Icons.search,
                  color: UnimedColors.grayLight,
                )),
          ),
        ),
      ),
    );
  }
}
