import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation__event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_state.dart';
import 'package:cliente_minha_unimed/bloc/contrato/contrato_bloc.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/profile_data_bloc.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider-solicitation.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:cliente_minha_unimed/shared/constants.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/utils/validators.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-error.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-evaluation.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/field-telephone.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:unimed_select/unimed-select.dart';

class NovaSolicitacaoTabData extends StatefulWidget {
  @override
  _NovaSolicitacaoTabDataState createState() => _NovaSolicitacaoTabDataState();
}

class _NovaSolicitacaoTabDataState extends State<NovaSolicitacaoTabData>
    with AutomaticKeepAliveClientMixin<NovaSolicitacaoTabData> {
  final telefoneFormatter = MaskTextInputFormatter(
    mask: '(##) #####-####',
    filter: {"#": RegExp(r'[0-9]')},
  );

  final observacoesFormatter = MaskTextInputFormatter(
    mask:
        '############################################################################################################################################################',
    filter: {
      "#": RegExp(
          r"^[a-zA-Z0-9 A-Za-záàâãéèêíïóôõöúçñÁÀÂÃÉÈÍÏÓÔÕÖÚÇÑ,.;?!@:-_]"),
    },
  );
  final tecTelefone = TextEditingController();
  final tecEmail = TextEditingController();
  final tecObservacoes = TextEditingController();

  TextEditingController _perfilController = TextEditingController();
  TextEditingController _localController = TextEditingController();

  final _formKey = GlobalKey<FormState>();
  late bool _disableFields;
  bool selectHospital = true;

  @override
  void initState() {
    _disableFields = false;
    tecEmail.text = BlocProvider.of<ProfileDataBloc>(context)
            .contactsModel
            ?.getPreferencial(CONTACT_TYPE_EMAIL)
            ?.contato ??
        "";
    tecTelefone.text = MaskTextInputFormatter(mask: '(##) # #### - ####')
        .maskText(BlocProvider.of<ProfileDataBloc>(context)
                .contactsModel
                ?.getPreferencial(CONTACT_TYPE_CELL)
                ?.contato ??
            "");

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Padding(
      padding: EdgeInsets.all(10),
      child: Form(
        key: _formKey,
        child: IgnorePointer(
          ignoring: _disableFields,
          child: SingleChildScrollView(
            child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, stateSensitiveData) {
              bool hideSensitiveData =
                  stateSensitiveData.isSensitiveDataVisible;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  _dropDownBeneficiarios(
                    context,
                    hideSensitiveData: hideSensitiveData,
                  ),
                  Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: _dropDownHospitais(context)),
                  _inputPhone(hideSensitiveData: hideSensitiveData),
                  _inputEmail(hideSensitiveData: hideSensitiveData),
                  TextField(
                    inputFormatters: [observacoesFormatter],
                    controller: tecObservacoes,
                    maxLines: 5,
                    minLines: 5,
                    textInputAction: TextInputAction.send,
                    style: TextStyle(color: UnimedColors.green),
                    decoration: InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: UnimedColors.grayLight2),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: UnimedColors.grayLight2),
                      ),
                      labelText: 'Observações',
                    ),
                    onSubmitted: (_) => _sendSolicitacao(),
                  ),
                  _buttonEnviar()
                ],
              );
            }),
          ),
        ),
        // ),
      ),
    );
  }

  InputBorder _borderError() {
    return OutlineInputBorder(
      borderSide: BorderSide(color: UnimedColors.redCancel),
    );
  }

  InputBorder _borderNormal() {
    return OutlineInputBorder(
      borderSide: BorderSide(color: UnimedColors.grayLight2),
    );
  }

  Widget _buttonEnviar() {
    return BlocConsumer<AutorizacoesEnvioBloc, AutorizacoesEnvioState>(
      listener: (context, state) {
        if (state is AutorizacoesEnvioLoadingState) {
          Navigator.popUntil(context, (route) => route.isFirst);

          Alert.open(
            Locator.instance.get<GlobalKey<NavigatorState>>().currentContext!,
            text:
                'Sua solicitação esta sendo enviada. Você pode acompanhar o progresso na tela de notificações',
            title: 'Enviando Solicitação',
          );
        } else if (state is AutorizacoesEnvioErrorState) {
          setState(() => _disableFields = false);
          UnimedErrorDialog.showAlert(
              context, state.message, AvaliacaoLabels.AUTORIZACAO);
        }
      },
      builder: (context, state) {
        return state is AutorizacoesEnvioLoadingState
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: 12.0),
                child: SpinKitThreeBounce(color: UnimedColors.green, size: 35))
            : Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: UnimedColors.green,
                        shape: RoundedRectangleBorder(
                            borderRadius: new BorderRadius.circular(18.0),
                            side: BorderSide(color: UnimedColors.green))),
                    onPressed: _sendSolicitacao,
                    child: Text('Enviar')));
      },
    );
  }

  _sendSolicitacao() {
    if (_disableFields) return;

    setState(() => _disableFields = true);

    AnalyticsService().logButtonClick({
      'type': 'button',
      'value': 'send_solicitacao',
      'email': tecEmail.text
    });

    selectHospital = _localController.text.isNotEmpty;

    if (_formKey.currentState!.validate() && selectHospital) {
      final envio = EnvioSolicitacaoAutorizacao(
        hospitalSelecionado: BlocProvider.of<ProviderSolicitationBloc>(context)
            .providerSolicitation,
        beneficiarioSelecionado: BlocProvider.of<PerfilBloc>(context).perfil,
        email: tecEmail.text.trim(),
        telefone: tecTelefone.text.trim(),
        anexos:
            BlocProvider.of<AutorizacaoAttachmentsBloc>(context).attachments,
        observacoes: tecObservacoes.text,
      );

      if (envio.anexos!.length > 0)
        BlocProvider.of<AutorizacoesEnvioBloc>(context).add(Enviar(
          cpfLogin: BlocProvider.of<UserBloc>(context).user.cpfLogin11Digits,
          dao: envio,
          beneficiarioData: BlocProvider.of<ContratoBloc>(context).beneficiario,
        ));
      else {
        Alert.open(
          context,
          text:
              "Você precisa anexar algum documento para completar o envio da solicitação.",
          title: 'Falha no envio de solicitação',
        );
        setState(() => _disableFields = false);
      }
    } else {
      setState(() => _disableFields = false);
    }
  }

  Widget _dropDownBeneficiarios(BuildContext context,
      {bool hideSensitiveData = false}) {
    final List<Perfil>? items = BlocProvider.of<UserBloc>(context).user.perfis;
    final Perfil perfilSelected = BlocProvider.of<PerfilBloc>(context).perfil;

    _perfilController.text = hideSensitiveData
        ? StringUtils.formahideDatatMessage(perfilSelected.nomeBeneficiario!)
        : perfilSelected.nomeBeneficiario!;
    // BlocProvider.of<PerfilBloc>(context)
    //     .add(SelectPerfil(perfil: perfilSelected));

    return BlocBuilder<PerfilBloc, PerfilState>(builder: (context, state) {
      return UnimedSelect<Perfil>(
        title: 'Perfil',
        showClearButton: false,
        controller: _perfilController,
        searchById: false,
        items: items != null
            ? items
                .map(
                  (entry) => UnimedSelectItemModel(
                    value: entry,
                    label: hideSensitiveData
                        ? StringUtils.formahideDatatMessage(
                            entry.nomeBeneficiario ?? "Sem Nome",
                          )
                        : entry.nomeBeneficiario ?? "Sem Nome",
                    id: entry.contratoBeneficiario.beneficiario?.cpf11Digits ??
                        "SEM CPF",
                  ),
                )
                .toList()
            : List.empty(),
        onSelect: (Perfil? item) {
          setState(() {
            BlocProvider.of<PerfilBloc>(context).add(ChangePerfil(
              carteira: item!.contratoBeneficiario.carteira,
              user: BlocProvider.of<UserBloc>(context).user,
            ));
            BlocProvider.of<ProviderSolicitationBloc>(context)
                .add(ListAllProviderSolicitationEvent(perfil: item));
          });
        },
      );
    });
  }

  Widget _inputPhone({bool hideSensitiveData = false}) {
    return BlocConsumer<ProfileDataBloc, ProfileDataState>(
        listener: (context, state) {
      if (state is ProfileContactDataDone) {
        setState(() {
          tecTelefone.text = telefoneFormatter.maskText(
              BlocProvider.of<ProfileDataBloc>(context)
                      .contactsModel
                      ?.getPreferencial(CONTACT_TYPE_CELL)
                      ?.contato ??
                  "");
        });
      }
    }, builder: (context, state) {
      if (state is ProfileDataLoading) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: TelephoneField(
            key: Key('textTelefone'),
            controller: TextEditingController(),
            mask: TelephoneFieldMask.phoneAndCell,
            hint: 'Telefone de contato',
            obscureText: hideSensitiveData,
            inputDecoration: InputDecoration(
                enabledBorder: _borderNormal(),
                focusedBorder: _borderNormal(),
                errorBorder: _borderError(),
                disabledBorder: _borderNormal(),
                focusedErrorBorder: _borderError(),
                labelText: 'Telefone de contato',
                suffixIcon: Container(
                  height: 30,
                  padding: const EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(),
                )),
          ),
        );
      } else {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: TelephoneField(
            key: Key('textTelefone'),
            controller: tecTelefone,
            mask: TelephoneFieldMask.phoneAndCell,
            hint: 'Telefone de contato',
            inputDecoration: InputDecoration(
              enabledBorder: _borderNormal(),
              focusedBorder: _borderNormal(),
              errorBorder: _borderError(),
              focusedErrorBorder: _borderError(),
              labelText: 'Telefone de contato',
            ),
          ),
        );
      }
    });
  }

  Widget _inputEmail({bool hideSensitiveData = false}) {
    return BlocConsumer<ProfileDataBloc, ProfileDataState>(
      listener: (context, state) {
        if (state is ProfileContactDataDone) {
          setState(() {
            tecEmail.text = state.email;
          });
        }
      },
      builder: (context, state) {
        if (state is ProfileDataLoading) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: TextFormField(
              enabled: false,
              keyboardType: TextInputType.emailAddress,
              controller: TextEditingController(),
              style: TextStyle(color: UnimedColors.green),
              autovalidateMode: AutovalidateMode.always,
              obscureText: hideSensitiveData,
              decoration: InputDecoration(
                  enabledBorder: _borderNormal(),
                  focusedBorder: _borderNormal(),
                  disabledBorder: _borderNormal(),
                  errorBorder: _borderError(),
                  focusedErrorBorder: _borderError(),
                  labelText: 'E-mail de contato',
                  suffixIcon: Container(
                    height: 30,
                    padding: const EdgeInsets.all(8.0),
                    child: CircularProgressIndicator(),
                  )),
              validator: TextFieldValidators.email,
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: TextFormField(
            keyboardType: TextInputType.emailAddress,
            controller: tecEmail,
            style: TextStyle(color: UnimedColors.green),
            autovalidateMode: AutovalidateMode.always,
            decoration: InputDecoration(
              enabledBorder: _borderNormal(),
              focusedBorder: _borderNormal(),
              errorBorder: _borderError(),
              focusedErrorBorder: _borderError(),
              labelText: 'E-mail de contato',
            ),
            validator: TextFieldValidators.email,
          ),
        );
      },
    );
  }

  Widget _dropDownHospitais(BuildContext context) {
    List<ProviderSolicitationModel> items =
        BlocProvider.of<ProviderSolicitationBloc>(context).prestadores
            as List<ProviderSolicitationModel>;
    final ProviderSolicitationModel? hospitalSelected =
        BlocProvider.of<ProviderSolicitationBloc>(context).providerSolicitation;

    List<String?> itensNames = List<String?>.empty(growable: true);
    items.forEach((e) => itensNames.add(e.providerName));

    if (hospitalSelected == null ||
        !itensNames.contains(hospitalSelected.providerName)) {
      selectHospital = false;
      _localController.text = "";
    }

    return BlocBuilder<ProviderSolicitationBloc, ProviderSolicitationState>(
      builder: (context, state) {
        if (state is ProviderSolicitationLoadingState) {
          return DropdownButtonFormField(
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: UnimedColors.grayLight2),
              ),
            ),
            hint: Row(
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 8.0, right: 8.0),
                  child: SizedBox(
                    height: 20.0,
                    width: 20.0,
                    child: CircularProgressIndicator(),
                  ),
                ),
                Text('Carregando Hospitais...'),
              ],
            ),
            style: TextStyle(color: UnimedColors.green),
            items: <DropdownMenuItem>[],
            onChanged: null,
          );
        } else if (state is ProviderSolicitationErrorState) {
          return Row(
            children: <Widget>[
              Flexible(
                  child: Text('${state.message}',
                      style: TextStyle(color: Colors.red))),
              IconButton(
                icon: Icon(Icons.refresh, color: unimedGreen),
                onPressed: () => {
                  BlocProvider.of<ProviderSolicitationBloc>(context).add(
                      ListAllProviderSolicitationEvent(
                          perfil: BlocProvider.of<PerfilBloc>(context).perfil))
                },
              )
            ],
          );
        }
        List<ProviderSolicitationModel> items =
            BlocProvider.of<ProviderSolicitationBloc>(context).prestadores
                as List<ProviderSolicitationModel>;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UnimedSelect<ProviderSolicitationModel>(
              title: 'Local',
              showClearButton: false,
              controller: _localController,
              items: items
                  .map(
                    (entry) => UnimedSelectItemModel(
                      value: entry,
                      label: entry.providerName,
                    ),
                  )
                  .toList(),
              onSelect: (ProviderSolicitationModel? item) {
                setState(() {
                  BlocProvider.of<ProviderSolicitationBloc>(context).add(
                      SelectProviderSolicitationEvent(
                          providerSolicitation: item));
                  selectHospital = true;
                });
              },
            ),
            if (!selectHospital)
              Padding(
                  padding: EdgeInsets.only(top: 5, left: 5),
                  child: Text(
                    'Necessário selecionar um local',
                    style: TextStyle(fontSize: 12, color: Colors.red[800]),
                  )),
          ],
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
