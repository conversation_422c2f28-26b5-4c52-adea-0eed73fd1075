import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/shared/widgets/show-up-animation/show-up-animation.dart';
import 'package:flutter/material.dart';

class NewSolicitationFeatureDiscovery extends StatelessWidget {
  NewSolicitationFeatureDiscovery({required this.onConfirm});
  final VoidCallback onConfirm;
  static const baseImages = 'assets/images/feature_discovery/ve_service';
  @override
  Widget build(BuildContext context) {
    final _size = MediaQuery.of(context).size;

    return Material(
      color: Colors.transparent,
      child: Container(
          width: _size.width,
          height: _size.height,
          color: Colors.black.withOpacity(.75),
          child: Column(
            children: [
              ShowUp(
                delay: 250,
                child: Padding(
                  padding: const EdgeInsets.only(top: 100.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: Text(
                            'Ao finalizar de adicionar seus documentos, para enviar a solicitação, clique aqui',
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      Image.asset(
                        '$baseImages/angle_to_left_down_arrow.png',
                        // width: _size.width * 0.35,
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: ShowUp(
                    delay: 400,
                    duration: 750,
                    child: Container(
                        alignment: Alignment.center,
                        margin: const EdgeInsets.all(16.0),
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(color: UnimedColors.orange),
                        ),
                        child: Text(
                            'Seus documentos serão listadas nessa área, você poderá editar, ampliar e excluir. Caso sejam multiplos anexos, arraste para o lado para visualizar todos.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                            ))),
                  ),
                ),
              ),
              Container(
                alignment: Alignment.center,
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                child: ElevatedButton(
                  child: Text('Ok, entendi'),
                  onPressed: onConfirm,
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: ShowUp(
                      delay: 150,
                      child: Row(
                        children: [
                          Image.asset(
                            '$baseImages/angle_to_right_arrow.png',
                            // width: _size.width * 0.35,
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 70.0),
                              child: Text(
                                'Caso queira fotografar o documento, clique aqui',
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: ShowUp(
                            delay: 50,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 70.0),
                              child: Text(
                                'Caso queira anexar um documento, clique aqui',
                                textAlign: TextAlign.end,
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Image.asset(
                          '$baseImages/angle_to_left_arrow.png',
                          // width: _size.width * 0.35,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          )),
    );
  }
}
