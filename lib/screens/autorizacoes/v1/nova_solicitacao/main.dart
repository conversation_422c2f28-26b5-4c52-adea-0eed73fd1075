import 'dart:io';

import 'package:badges/badges.dart' as badge;
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation__event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_state.dart';
import 'package:cliente_minha_unimed/bloc/contrato/contrato_bloc.dart';
import 'package:cliente_minha_unimed/bloc/contrato/contrato_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/profile-data/profile_data_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/solicitations-feature-discovery.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-data.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/tab-files.dart';
import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/sensitive-data-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert_confirm.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NovaSolicitacaoScreen extends StatefulWidget {
  final bool fromAlert;
  final EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao;

  NovaSolicitacaoScreen(
      {Key? key, required this.fromAlert, this.envioSolicitacaoAutorizacao})
      : super(key: key);

  _NovaSolicitacaoScreenState createState() => _NovaSolicitacaoScreenState();
}

class _NovaSolicitacaoScreenState extends State<NovaSolicitacaoScreen>
    with TickerProviderStateMixin {
  bool dialogShowUp = false;
  bool _showTutorial = false;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    debugPrint("============== ${widget.fromAlert}");
    BlocProvider.of<ProviderSolicitationBloc>(context).add(
        ListAllProviderSolicitationEvent(
            perfil: BlocProvider.of<PerfilBloc>(context).perfil));

    if (widget.fromAlert)
      BlocProvider.of<VerifySolicBloc>(context).add(
          VerifyStatus(perfil: BlocProvider.of<PerfilBloc>(context).perfil));

    _isFirstAccess();
    // BlocProvider.of<VerifySolicBloc>(context).add(VerifyRequest());

    BlocProvider.of<ContratoBloc>(context).add(
        GetBeneficiarioDataEvent(BlocProvider.of<PerfilBloc>(context).perfil));

    BlocProvider.of<ProfileDataBloc>(context).add(GetProfileContactData(
        profile: BlocProvider.of<PerfilBloc>(context).perfil));
    BlocProvider.of<AutorizacoesEnvioBloc>(context).add(Inicio());

    AnalyticsService().addLogScreenView(
      screenName: 'NovaSolicitacaoScreen',
      screenClass: 'NovaSolicitacaoScreen',
    );

    super.initState();
  }

  void _isFirstAccess() async {
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      final isFirstAccess =
          await BlocProvider.of<VerifySolicBloc>(context).isFirstAccess();
      setState(() => _showTutorial = isFirstAccess);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) => _checkExit,
      child: Stack(
        children: [
          _home(),
          if (_showTutorial)
            NewSolicitationFeatureDiscovery(
              onConfirm: () {
                setState(() => _showTutorial = false);
                BlocProvider.of<VerifySolicBloc>(context).setFirstAccess();
              },
            ),
        ],
      ),
    );
  }

  Widget _home() {
    final _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: 0,
    );

    return Scaffold(
        appBar: AppBarUnimed(
          title: Text('Nova Solicitação'),
          backgroundColor: UnimedColors.green,
          actions: [
            SensitibeDataButton(),
            IconButton(
              onPressed: () => setState(() => _showTutorial = true),
              icon: Icon(Icons.help_outline_outlined),
            )
          ],
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            indicatorWeight: 5,
            indicatorPadding: EdgeInsets.only(bottom: 2),
            tabs: [
              Tab(
                icon: badge.Badge(
                  badgeStyle: badge.BadgeStyle(badgeColor: Colors.white),
                  badgeContent: BlocBuilder<AutorizacaoAttachmentsBloc,
                      AutorizacaoAttachmentsState>(builder: (context, state) {
                    return Text(
                        '${BlocProvider.of<AutorizacaoAttachmentsBloc>(context).attachments!.length}');
                  }),
                  child: Icon(Icons.file_upload, size: 40),
                ),
              ),
              Tab(icon: Icon(Icons.playlist_add_check, size: 40)),
            ],
          ),
        ),
        body: _verifySolic(_tabController));
  }

  Widget _verifySolic(TabController _tabController) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.envioSolicitacaoAutorizacao != null) if (!widget.fromAlert &&
          !dialogShowUp) _showDialog(widget.envioSolicitacaoAutorizacao);
      // else
      //   BlocProvider.of<AcceptedSolicBloc>(context).add(AcceptRequest(
      //       envioSolicitacaoAutorizacao: widget.envioSolicitacaoAutorizacao));
    });

    return BlocListener<VerifySolicBloc, VerifySolicState>(
        listener: (context, state) {
          if (state is FailRequest)
            BlocProvider.of<AcceptedSolicBloc>(context).add(AcceptRequest(
                envioSolicitacaoAutorizacao:
                    state.envioSolicitacaoAutorizacao));

          return;
        },
        child: TabBarView(
          controller: _tabController,
          physics: NeverScrollableScrollPhysics(),
          children: [
            NovaSolicitacaoTabFiles(),
            // TODO: remove this if when finish ios background service
            if (Platform.isAndroid)
              NovaSolicitacaoTabData()
            else
              NovaSolicitacaoTabData()
          ],
        ));
  }

  Future<bool> _checkExit() async {
    // final _countAttachments =
    //     BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
    //         .attachments!
    //         .length;

    BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
        .add(RemoveAllAttachedFiles());

    return true;
  }

  void _showDialog(EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao) {
    dialogShowUp = true;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => UnimedConfirmDialog(
        textWidget: Text("Atenção!"),
        textDescription: Text(
            "Ocorreu um erro na última tentativa de envio da solicitação. Gostaria de tentar novamente a partir dos dados existentes?"),
        textButton: "Confirmar",
        colorButton: UnimedColors.green,
        onClose: () {
          Navigator.of(context).pop();
          BlocProvider.of<AcceptedSolicBloc>(context).add(CleanRequest());
          BlocProvider.of<ProviderSolicitationBloc>(context)
              .add(SelectProviderSolicitationEvent(providerSolicitation: null));
          BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
              .add(RemoveAllAttachedFiles());
        },
        onPressed: () {
          Navigator.of(context).pop();
          BlocProvider.of<AcceptedSolicBloc>(context).add(AcceptRequest(
              envioSolicitacaoAutorizacao: envioSolicitacaoAutorizacao));
        },
      ),
    );
  }
}
