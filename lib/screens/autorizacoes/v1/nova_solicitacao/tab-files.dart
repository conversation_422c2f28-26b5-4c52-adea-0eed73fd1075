import 'dart:async';
import 'dart:io';

import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/envio_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/accepted/accepted_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/file_preview.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/handle_camera.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

final logger = UnimedLogger(className: 'NovaSolicitacaoTabFiles');
const double MAX_HEIGHT = 1080;

class NovaSolicitacaoTabFiles extends StatefulWidget {
  final picker = ImagePicker();

  NovaSolicitacaoTabFiles({Key? key}) : super(key: key);

  @override
  _NovaSolicitacaoTabFilesState createState() =>
      _NovaSolicitacaoTabFilesState();
}

class _NovaSolicitacaoTabFilesState extends State<NovaSolicitacaoTabFiles>
    with WidgetsBindingObserver {
  bool enableAddFilesButtons = true;
  bool _allowMultiple = false;
  bool _requestingPermission = false;

  bool get _maxLimit {
    return BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
            .attachments!
            .length >=
        BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
            .maxFilesAttachments;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (_requestingPermission) {
        _requestingPermission = false;
        _onPressFile();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: UnimedColors.grayLight,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Expanded(child: _listFiles(context)),
          specialCharsPanel(context),
        ],
      ),
    );
  }

  Widget specialCharsPanel(BuildContext context) {
    return Container(
      color: unimedGreen,
      child: SafeArea(
        child: Material(
          borderRadius: BorderRadius.all(Radius.circular(6.0)),
          color: unimedGreen,
          child: Row(
            children: <Widget>[
              Expanded(
                flex: 5,
                child: ElevatedButton(
                  key: Key('btnCamera'),
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                  ),
                  onPressed:
                      BlocProvider.of<AutorizacoesEnvioBloc>(context).state ==
                              AutorizacoesEnvioLoadingState()
                          ? null
                          : !enableAddFilesButtons
                              ? null
                              : _onPressCamera,
                  child: Icon(Icons.camera_alt),
                ),
              ),
              Expanded(
                flex: 5,
                child: ElevatedButton(
                  key: Key('btnFile'),
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                  ),
                  onPressed:
                      BlocProvider.of<AutorizacoesEnvioBloc>(context).state ==
                              AutorizacoesEnvioLoadingState()
                          ? null
                          : !enableAddFilesButtons
                              ? null
                              : () async => _onPressFile(),
                  child: Icon(Icons.file_copy),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _close(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  _onPressCamera() async {
    if (enableAddFilesButtons)
      try {
        enableAddFilesButtons = false;
        if (_maxLimit) {
          _openMaxLimitDialog();

          return;
        }
        AnalyticsService()
            .logButtonClick({'type': 'button', 'value': 'camera'});

        final cameraStatus = await Permission.camera.status;
        logger.i('Permissão da Câmera => $cameraStatus');

        if (cameraStatus == PermissionStatus.granted) {
          HandleCamera.openCameraScreen(context, _onTakePhoto);
        } else {
          HandleCamera.requestCameraPermission(context);
        }
      } catch (e) {
        logger.e('Error on onPressCamera on Solicitations $e');
      } finally {
        enableAddFilesButtons = true;
      }
  }

  _onTakePhoto(File? file, BuildContext context) async {
    if (file == null) {
      BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
          .add(StopAttachedFile());
    } else {
      final compressedFile = await FileUtils.getCompressedFile(file);

      BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
          .add(AttachFile(file: file, compressedFile: compressedFile));
    }
  }

  void _openMaxLimitDialog() {
    final int __maxFilesAttachments =
        BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
            .maxFilesAttachments;
    showDialog(
        context: context,
        builder: (context) {
          return UnimedAlertDialog(
              textWidget: Text(
                'Você pode anexar no máximo $__maxFilesAttachments imagens por solicitação.',
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                _close(context);
                BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
                    .add(StopAttachedFile());
              });
        });
  }

  _onPressFile() async {
    try {
      if (_maxLimit) {
        _openMaxLimitDialog();

        return;
      }

      AnalyticsService().logButtonClick({'type': 'button', 'value': 'file'});

      final infoJson =
          await Locator.instance.get<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

      ///TODO - Foi removido a permissão de acesso aos arquivos para o Android 13, mesmo assim o FilePicker está funcionando. Verificar se é necessário solicitar a permissão
      final dataAccesStatus = Platform.isAndroid
          ? androidVersion < 13
              ? await Permission.storage.request()
              : null
          : await Permission.photos.status;

      logger.i('Permissão da acesso arquivos => $dataAccesStatus');

      setState(() => enableAddFilesButtons = false);

      if (dataAccesStatus == PermissionStatus.granted ||
          dataAccesStatus == PermissionStatus.limited ||
          androidVersion >= 13) {
        final FilePickerResult? filePickerResult =
            await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowMultiple: _allowMultiple,
          allowedExtensions:
              BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
                  .formatFilesAllowed,
        );

        if (filePickerResult?.files.first == null ||
            filePickerResult!.files.isEmpty) {
          BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
              .add(StopAttachedFile());
        } else {
          final PlatformFile filePicked = filePickerResult.files.first;
          final File file =
              await FileUtils.copyFileOnDocuments(File(filePicked.path!));

          BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
              .add(AttachFile(file: file));
        }
      } else {
        _alertPermissionDataAcces(dataAccesStatus);
      }
    } catch (e) {
      logger.e('pickFiles error $e');
    } finally {
      setState(() => enableAddFilesButtons = true);
    }
  }

  _alertPermissionDataAcces(dataAccesStatus) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Acesso aos dados',
      text: 'Precisamos acessar seus arquivos para anexar o documento.',
      textButtonClose: 'Fechar',
      callbackClose: () {
        setState(() => enableAddFilesButtons = true);
      },
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.orange,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          child: Text('Autorizar'),
          onPressed: () async {
            if (canClick) {
              setState(() {
                canClick = false;
              });

              if (dataAccesStatus == PermissionStatus.denied) {
                await _requestPermission();
              } else {
                Navigator.of(context).pop();
                _requestingPermission = true;
                openAppSettings();
              }

              setState(() => enableAddFilesButtons = true);
            }
          },
        ),
      ],
    );
  }

  Future<void> _requestPermission() async {
    try {
      final infoJson =
          await Locator.instance.get<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

      Platform.isAndroid
          ? androidVersion < 13
              ? Permission.storage.request().then((PermissionStatus status) {
                  debugPrint('Permissão storage $androidVersion $status');
                  _handlePermissionStatusResponse(status);
                })
              : null
          : Permission.photos.request().then((PermissionStatus status) {
              debugPrint('Permissão photos $status');
              Navigator.of(context).pop();
            });
    } catch (e) {
      debugPrint('erro: $e');
    }
  }

  void _handlePermissionStatusResponse(PermissionStatus status) {
    if (status == PermissionStatus.granted ||
        status == PermissionStatus.limited) {
      Navigator.of(context).pop();
      _onPressFile();
    } else {
      Navigator.of(context).pop();
      _alertPermissionDenied(status);
    }
  }

  _alertPermissionDenied(dataAccesStatus) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Permissão não concedida',
      text:
          'Não será possível acessar seus arquivos para anexar o documento. Tente novamente para permitir o acesso.',
      textButtonClose: 'Fechar',
      callbackClose: () {
        setState(() => enableAddFilesButtons = true);
      },
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.orange,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          child: Text('Tentar novamente'),
          onPressed: () async {
            if (canClick) {
              setState(() {
                canClick = false;
              });

              if (dataAccesStatus == PermissionStatus.denied) {
                await _requestPermission();
              } else {
                Navigator.of(context).pop();
                openAppSettings();
              }

              setState(() => enableAddFilesButtons = true);
            }
          },
        ),
      ],
    );
  }

  Widget _listFiles(BuildContext context) {
    return BlocBuilder<AcceptedSolicBloc, AcceptedState>(
      buildWhen: (oldState, state) {
        if (state is RequestAccepted) {
          BlocProvider.of<AutorizacaoAttachmentsBloc>(context).add(
              AttachFiles(files: state.envioSolicitacaoAutorizacao!.anexos));
        }

        return true;
      },
      builder: (context, state) {
        return BlocBuilder<AutorizacaoAttachmentsBloc,
            AutorizacaoAttachmentsState>(
          builder: (context, state) {
            if (state is InitialState) {
              return _attachDocumentsInfo();
            } else if (state is LoadingState) {
              return Column(children: <Widget>[
                Text('Carregando documentos na lista...'),
                LinearProgressIndicator()
              ]);
            } else if (state is DoneState) {
              if (state.attachments!.isEmpty) return _attachDocumentsInfo();

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: PageView.builder(
                  itemCount: state.attachments!.length,
                  controller: PageController(viewportFraction: 1 / 1.25),
                  itemBuilder: (context, index) {
                    return _cardFile(state.attachments![index]);
                  },
                ),
              );
            } else if (state is AutorizacaoAttachmentErrorState) {
              AnalyticsService().sendEvent(
                name: "Limite de arquivos excedido",
                parameters: {
                  "info": "Usuário escedeu o limite de anexos",
                  "limite":
                      "${BlocProvider.of<AutorizacaoAttachmentsBloc>(context).maxFilesAttachments}",
                },
              );

              return UnimedAlertDialog(
                textWidget: Text(
                  state.message,
                  textAlign: TextAlign.center,
                ),
                onPressed: () =>
                    BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
                        .add(StopAttachedFile()),
              );
            } else {
              return Container();
            }
          },
        );
      },
    );
  }

  Widget _attachDocumentsInfo() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Card(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(18.0),
          child: Text(
            'Anexe documentos a sua solicitação',
            style: TextStyle(color: UnimedColors.green),
          ),
        ),
      ),
    );
  }

  Widget _cardFile(FileAttach file) {
    final fileParts = file.file.path.split('.');
    final fileType = fileParts.last;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 16, horizontal: 8.0),
      child: Card(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Expanded(
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PreviewFileScreen(
                        file: file.file,
                        fileName: file.name,
                        fileType: fileType,
                      ),
                    ),
                  );
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: _viewFile(file, fileType),
                ),
              ),
            ),
            BlocProvider.of<AutorizacoesEnvioBloc>(context).state ==
                    AutorizacoesEnvioLoadingState()
                ? Container()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      IconButton(
                        icon: Icon(Icons.delete),
                        color: Colors.red,
                        onPressed: () {
                          AnalyticsService().logButtonClick({
                            'type': 'button',
                            'value': 'Solicitação: remove_photo'
                          });
                          BlocProvider.of<AutorizacaoAttachmentsBloc>(context)
                              .add(RemoveAttachedFile(file: file));
                        },
                      ),
                      fileType != 'pdf'
                          ? IconButton(
                              icon: Icon(Icons.edit),
                              color: UnimedColors.grayDark,
                              onPressed: () async {
                                AnalyticsService().logButtonClick({
                                  'type': 'button',
                                  'value': 'Solicitação: edit_photo',
                                });
                                File newFile = await _editImage(file.file);
                                BlocProvider.of<AutorizacaoAttachmentsBloc>(
                                        context)
                                    .add(
                                  EditAttachedFile(
                                    file: file.file,
                                    newFile: newFile,
                                  ),
                                );
                              },
                            )
                          : Container(),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Widget _viewFile(FileAttach file, String fileType) {
    return fileType == 'pdf'
        ? Column(children: [
            Expanded(child: Icon(Icons.picture_as_pdf_outlined, size: 56)),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(file.name))
          ])
        : Stack(
            fit: StackFit.expand,
            children: [
              Image.file(file.thumbnail ?? file.file,
                  fit: BoxFit.cover, filterQuality: FilterQuality.low),
              Container(
                color: Theme.of(context).hintColor.withOpacity(.55),
                child: Column(
                  children: [
                    Expanded(child: SizedBox()),
                    Expanded(
                      child: Icon(Icons.image,
                          size: 56, color: UnimedColors.white),
                    ),
                    Expanded(
                      child: Center(
                        child: Text(
                          'Clique para visualizar',
                          style: TextStyle(color: UnimedColors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          );
  }

  Future<File> _editImage(imageFile) async {
    final editedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatioPresets: Platform.isAndroid
          ? [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ]
          : [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio5x3,
              CropAspectRatioPreset.ratio5x4,
              CropAspectRatioPreset.ratio7x5,
              CropAspectRatioPreset.ratio16x9
            ],
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Edição',
          toolbarColor: UnimedColors.green,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
      ],
    );
    if (editedFile != null) {
      imageFile = File(editedFile.path);
    }

    return imageFile;
  }

  blockCameraButton() {
    setState(() {
      enableAddFilesButtons = true;
    });
  }
}
