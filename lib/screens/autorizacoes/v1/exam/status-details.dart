import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/electronic-guide.vo.dart';
import 'package:flutter/material.dart';

class StatusDetails extends StatefulWidget {
  final DetalheGuiaModel? electronicGuide;
  StatusDetails({required this.electronicGuide});
  @override
  _StatusDetailsState createState() => _StatusDetailsState();
}

class _StatusDetailsState extends State<StatusDetails> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.0),
      child: Card(
        color: UnimedColors.yellowLight,
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: <
                  Widget>[
                Text(
                  "Solicitada",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                ),
                Text(
                  "Em análise",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                ),
                Text(
                  "Autorizada",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                ),
                Text(
                  "Finalizada",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
                )
              ]),
              Container(
                padding: EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    Flexible(
                      child: Container(),
                    ),
                    Container(
                      height: 30,
                      width: 30,
                      decoration: decorationCircle("Solicitada"),
                      child: Center(
                          child: Text(
                        '1',
                        style: TextStyle(color: verifyStatus("Solicitada")),
                      )),
                    ),
                    Flexible(
                      child: Divider(
                        thickness: 2,
                      ),
                    ),
                    Container(
                      height: 30,
                      width: 30,
                      decoration: decorationCircle("Em Análise"),
                      child: Center(
                          child: Text(
                        '2',
                        style: TextStyle(color: verifyStatus("Em Análise")),
                      )),
                    ),
                    Flexible(
                      child: Divider(
                        thickness: 2,
                      ),
                    ),
                    Container(
                      height: 30,
                      width: 30,
                      decoration: decorationCircle("Autorizada"),
                      child: Center(
                          child: Text(
                        '3',
                        style: TextStyle(color: verifyStatus("Autorizada")),
                      )),
                    ),
                    Flexible(
                      child: Divider(
                        thickness: 2,
                      ),
                    ),
                    Container(
                      height: 30,
                      width: 30,
                      decoration: decorationCircle("Finalizada"),
                      child: Center(
                          child: Text(
                        '4',
                        style: TextStyle(color: verifyStatus("Finalizada")),
                      )),
                    ),
                    Flexible(
                      child: Container(),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Color verifyStatus(String label) {
    ///////// TROCAR

    return ElectronicGuideStatus.setCircle(
                widget.electronicGuide!.situacao!.toLowerCase()) ==
            label
        ? UnimedColors.orange
        : UnimedColors.grayLight;
  }

  Decoration decorationCircle(String status) {
    return BoxDecoration(
      color: Colors.white,
      border: Border.all(color: verifyStatus(status), width: 3),
      borderRadius: BorderRadius.all(Radius.elliptical(30, 30)),
    );
  }
}
