import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/pdf_exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/electronic-guide.vo.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/pdf-view/pdf-view-screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class TableExam extends StatefulWidget {
  final DetalheGuiaModel guia;

  TableExam({required this.guia});

  @override
  _TableExamState createState() => _TableExamState();
}

class _TableExamState extends BaseState<TableExam> {
  //Inserido logger por motivos de redirecionamento de url
  final logger = UnimedLogger(className: 'ElectronicGuideDetails');
  final baseTranslate = "autorizations";

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[Text("Serviço"), Text("Qtde")],
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: PageScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.0),
            itemCount: widget.guia.listaServicos?.length,
            itemBuilder: (BuildContext context, int index) {
              final _itemProc = widget.guia.listaServicos?.elementAt(index);

              return Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              alignment: WrapAlignment.start,
                              crossAxisAlignment: WrapCrossAlignment.start,
                              direction: Axis.vertical,
                              children: <Widget>[
                                SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.65,
                                  child: Text(
                                    _itemProc!.descricao!,
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                // _arrayDetails(_itemProc.!)
                              ],
                            ),
                            Text(
                              _itemProc.situacaoServico!,
                              style: TextStyle(
                                color: ElectronicGuideStatus.setColorStatus(
                                  _itemProc.situacaoServico!.toLowerCase(),
                                ),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Text(_itemProc.quantidade.toString()),
                      ],
                    ),
                  ),
                  Divider(),
                ],
              );
            },
          ),
          if (_checkDisplayButton())
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [_button()],
              ),
            ),
        ],
      ),
    );
  }

  bool _checkDisplayButton() {
    if (widget.guia.codPreSolic == null) {
      return false;
    } else if (widget.guia.situacao == 'Em Analise') {
      return false;
    } else if (widget.guia.situacao == 'Pendente') {
      return false;
    }

    return true;
  }

  // // Widget _documentosPendentes(ListaServicos item) {
  // //   List<Widget> documentosPendente = [];

  // //   item.documentosPendentes.forEach(
  // //     (element) => documentosPendente.add(Row(
  // //       children: [
  // //         Icon(
  // //           Icons.circle,
  // //           color: UnimedColors.grayDark,
  // //           size: 10,
  // //         ),
  // //         Padding(
  // //           padding: const EdgeInsets.only(left: 4.0),
  // //           child: Text(
  // //             element,
  // //             style: TextStyle(color: Colors.black),
  // //           ),
  // //         ),
  // //       ],
  // //     )),
  // //   );

  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       Expanded(
  //         child: Padding(
  //           padding: const EdgeInsets.only(left: 8),
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: documentosPendente,
  //           ),
  //         ),
  //       ),
  //       Expanded(
  //         child: FittedBox(child: _buttonSendDocument(item)),
  //       ),
  //     ],
  //   );
  // }

  // void _launchUrl(ResponseCheckBtnRobertaVO response) async {
  //   if (widget.guia.numProtocoloANS != null) {
  //     try {
  //       final String carteira = BlocProvider.of<PerfilBloc>(context)
  //           .perfil
  //           .carteira!
  //           .carteiraNumero;
  //       final message = Uri.parse(response.handleDataMessage(
  //         widget.guia.numProtocoloANS ?? "0",
  //         carteira,
  //       ));

  //       final iOSURL = "https://wa.me/${response.phone}?text=$message";

  //       final androidURL =
  //           "whatsapp://send?phone=${response.phone}&text=$message";

  //       if (Platform.isIOS) {
  //         logger.d("_launchUrl iOSURL => $iOSURL");
  //         // for iOS phone only
  //         if (await canLaunchUrl(Uri.parse(iOSURL))) {
  //           await launchUrl(Uri.parse(iOSURL));
  //         } else {
  //           final message = "WhatsApp não instalado.";
  //           logger.e('_launchUrl Error $iOSURL => $message');

  //           throw message;
  //         }
  //       } else {
  //         // android , web
  //         logger.d("_launchUrl androidURL => $androidURL");
  //         if (await canLaunchUrl(Uri.parse(androidURL))) {
  //           await launchUrl(Uri.parse(androidURL));
  //         } else {
  //           final message = "WhatsApp não instalado.";
  //           logger.e('_launchUrl Error $androidURL => $message');
  //           throw message;
  //         }
  //       }
  //     } catch (e) {
  //       logger.e("launchUrl error $e");
  //     }
  //   }
  // }

  // Widget _btnWhatsApp() {
  //   return widget.guia.numProtocoloANS != null
  //       ? BlocConsumer<RobertaWppBloc, RobertaWppState>(
  //           listener: (context, state) {
  //           if (state is DoneState) {
  //             _launchUrl(state.response);
  //           }
  //         }, buildWhen: (previousState, state) {
  //           if (state is ErrorState) {
  //             showDialog(
  //                 context: context,
  //                 builder: (context) {
  //                   return UnimedAlertDialog(
  //                     title: Text("Aviso"),
  //                     textWidget: Text(
  //                       state.message,
  //                       textAlign: TextAlign.center,
  //                     ),
  //                     onPressed: () {
  //                       Navigator.pop(context);
  //                     },
  //                   );
  //                 });
  //           }

  //           return true;
  //         }, builder: (context, state) {
  //           return state is LoadingState
  //               ? SpinKitThreeBounce(color: UnimedColors.green, size: 20)
  //               : ElevatedButton(
  //                   style: ElevatedButton.styleFrom(
  //                       backgroundColor: UnimedColors.green),
  //                   onPressed: () async {
  //                     BlocProvider.of<RobertaWppBloc>(context).add(
  //                         CheckTalkRoberta(
  //                             perfil:
  //                                 BlocProvider.of<PerfilBloc>(context).perfil));
  //                   },
  //                   child: Text("Falar com Atendente"));
  //         })
  //       : SizedBox();
  // }

  Widget _button() {
    // Foi combinado com diel e diogo que botão PDF só irá aparecer quando tiver
    // na guia o codPreSolic, pois é o que precisa no endpoint para gerar o PDF
    // no servico

    return widget.guia.codPreSolic != null
        ? BlocBuilder<PdfExamBloc, ExamState>(
            buildWhen: (previousState, state) {
              if (previousState is LoadingPdf && state is DonePdf) {
                if (state.codSolicitacao == widget.guia.codPreSolic)
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PDFViewScreen(
                        state.path,
                        title: 'Detalhes da guia eletrônica',
                        isPath: true,
                      ),
                    ),
                  );
              } else if (previousState is LoadingPdf && state is ErrorPdf) {
                Alert.open(
                  context,
                  title: 'Ocorreu um erro',
                  text: state.message,
                );
              }

              return true;
            },
            builder: (context, state) {
              return state is LoadingPdf
                  ? SpinKitThreeBounce(color: UnimedColors.green)
                  : ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: UnimedColors.orange,
                      ),
                      onPressed: () {
                        BlocProvider.of<PdfExamBloc>(context).add(GeneratePdf(
                          cod: widget.guia.codPreSolic,
                          perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                        ));
                      },
                      child: Text("VER AUTORIZAÇÃO"),
                    );
            },
          )
        : Container();
  }
}
