import 'dart:math';

import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/detalhe-guia-timeline/detalhe_guia_bloc.dart';
import 'package:flutter/material.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/item-timeline.dart';

import 'package:cliente_minha_unimed/shared/widgets/show-up-animation/show-up-animation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class TimelineAttendance extends StatefulWidget {
  final GuiaModel guia;

  TimelineAttendance({required this.guia});

  @override
  _TimelineAttendanceState createState() => _TimelineAttendanceState();
}

class _TimelineAttendanceState extends State<TimelineAttendance>
    with SingleTickerProviderStateMixin {
  late AnimationController _arrowAnimationController;
  late Animation _arrowAnimation;
  late ScrollController scrollController;
  double _animatedHeight = 0.0;
  bool _expandedMode = false;

  final double expandedHight = 150.0;
  double get top {
    double res = expandedHight;
    if (scrollController.hasClients) {
      double offset = scrollController.offset;
      res = offset < (res - kToolbarHeight) ? offset : kToolbarHeight;
    }

    return res;
  }

  @override
  void initState() {
    _arrowAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 300));
    _arrowAnimation =
        Tween(begin: 0.0, end: pi).animate(_arrowAnimationController);
    scrollController = new ScrollController();
    // ignore: no-empty-block
    scrollController.addListener(() => setState(() {}));

    BlocProvider.of<DetalheTimelineGuiaBloc>(context)
        .add(BuscarDetalheTimelineEvent(guia: widget.guia));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BlocBuilder<DetalheTimelineGuiaBloc, DetalheGuiaState>(
        builder: (context, state) {
          if (state is ErrorBuscarDetalheState) {
            return Text(state.message);
          } else if (state is LoadingBuscarDetalheState) {
            return SpinKitThreeBounce(color: UnimedColors.green);
          } else if (state is SuccessBuscarDetalheState) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: _showList(state.timeline),
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }

  List<Widget> _showList(List<DetalheTimeline> timeline) {
    return [
      Divider(),
      Center(
        child: Text(
          "LINHA DO TEMPO",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: UnimedColors.grayDark2,
            fontSize: 18,
          ),
        ),
      ),
      GestureDetector(
        onTap: () => setState(() {
          _arrowAnimationController.isCompleted
              ? _arrowAnimationController.reverse()
              : _arrowAnimationController.forward();
          _animatedHeight != 0.0
              ? _animatedHeight = 0.0
              : _animatedHeight = 150;
          _expandedMode = !_expandedMode;
        }),
        child: AnimatedBuilder(
          animation: _arrowAnimationController,
          builder: (context, child) => Transform.rotate(
            angle: _arrowAnimation.value,
            child: Icon(
              Icons.expand_more,
              size: 40,
              color: UnimedColors.green,
            ),
          ),
        ),
      ),
      if (_expandedMode) ...list(timeline),
    ];
  }

  List<Widget> list(List<DetalheTimeline> list) {
    return [
      Container(
        padding: EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[Text("Observações"), Text("Data/Horário")],
        ),
      ),
      ShowUp(
        delay: 350,
        duration: 500,
        child: ListView.builder(
          shrinkWrap: true,
          physics: PageScrollPhysics(),
          padding: const EdgeInsets.all(8),
          itemCount: list.length,
          itemBuilder: (BuildContext context, int index) {
            final _itemAttend = list.elementAt(index);

            return ItemTimeline(
              detalheTimeline: _itemAttend,
            );
          },
        ),
      ),
    ];
  }
}
