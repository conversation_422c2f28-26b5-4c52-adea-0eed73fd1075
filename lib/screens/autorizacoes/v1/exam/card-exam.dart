import 'package:flutter/material.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/electronic-guide.vo.dart';

class CardExam extends StatefulWidget {
  final ElectronicGuide electronicGuide;
  CardExam({required this.electronicGuide});
  @override
  _CardExamState createState() => _CardExamState();
}

class _CardExamState extends State<CardExam> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // onTap: () {
      //   // Navigator.push(
      //   //   context,
      //   //   ScaleRoute(
      //   //     page: DetailsExamScreen(
      //   //       electronicGuide: widget.electronicGuide,
      //   //     ),
      //   //   ),
      //   // );
      // },
      child: Card(
        margin: EdgeInsets.all(8),
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Container(
                alignment: Alignment.center,
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10)),
                  color: ElectronicGuideStatus.setColorStatus(
                      widget.electronicGuide.descStatus?.toLowerCase() ??
                          "Status não recebido"),
                ),
                child: Text(
                  widget.electronicGuide.descStatus
                      .toString(), // verificar com dantas quais os nomes de acordo com o numero
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8.0, left: 8.0),
                child: Text(
                  "Solicitação",
                  style: TextStyle(fontSize: 12),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  widget.electronicGuide.codPreSolic.toString(),
                  style: TextStyle(color: UnimedColors.grayDark2, fontSize: 18),
                ),
              ),
              Container(
                padding: EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          "Data da solicitação",
                          style: TextStyle(fontSize: 12),
                        ),
                        Row(
                          children: <Widget>[
                            Icon(
                              Icons.calendar_today,
                              size: 14,
                              color: UnimedColors.grayLight2,
                            ),
                            Text(widget.electronicGuide.dataHoraFormat,
                                style: TextStyle(
                                    fontSize: 16,
                                    color: UnimedColors.grayDark2))
                          ],
                        ),
                      ],
                    ),
                    Icon(
                      Icons.info_outline,
                      color: UnimedColors.green,
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
