import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_state.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/file-viewer.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';

import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/journey/journey-footer.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva.dart';
import 'package:flutter/material.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ExamDocuments extends StatefulWidget {
  final ListaServicos servico;
  final String protocolo;

  ExamDocuments({
    required this.servico,
    required this.protocolo,
  });

  @override
  _ExamDocumentState createState() => _ExamDocumentState();
}

class _ExamDocumentState extends BaseState<ExamDocuments> {
  bool loading = false;
  static const baseTranslate = 'autorizations.details';

  @override
  void initState() {
    BlocProvider.of<AuthorizationDocumentsBloc>(context).add(LoadAttachedFiles(
      attachment: widget.servico.documentosPendentes,
      cdAgeCir: widget.servico.codPreSolicServ.toString(),
      card: BlocProvider.of<PerfilBloc>(context)
          .perfil
          .contratoBeneficiario
          .carteira!
          .carteiraNumero,
    ));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUnimed(
        title: Text("Documentos"),
      ),
      body:
          BlocBuilder<AuthorizationDocumentsBloc, AuthorizationDocumentsState>(
        builder: (context, state) {
          if (state is LoadingState) {
            return Center(
              child: EvaLoading(
                color: UnimedColors.green,
              ),
            );
          } else if (state is DoneState) {
            return Container(
              color: UnimedColors.grayLight,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  header(),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: PageView.builder(
                        itemCount: state.attachments.length,
                        controller: PageController(viewportFraction: 1 / 1.25),
                        itemBuilder: (context, index) {
                          return FileViewer(
                            fileAttach: state.attachments[index],
                            index: index,
                          );
                        },
                      ),
                    ),
                  ),
                  _footer(),
                ],
              ),
            );
          } else
            return Center(
              child: EvaTriste(
                message: Text("Erro ao carregar documentos"),
              ),
            );
        },
      ),
    );
  }

  Widget header() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          Text(
            translate('$baseTranslate.header.span1'),
            style: TextStyle(
              color: UnimedColors.conectaViolet,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '${translate('$baseTranslate.header.span2')}:',
            style: TextStyle(
              color: UnimedColors.conectaViolet,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _footer() {
    return JourneyFooter(
      textButton: 'Continuar',
      onClickActionButton: () {
        BlocProvider.of<AuthorizationDocumentsBloc>(context).add(
          SendAttachedFiles(
            servico: widget.servico,
            protocolo: widget.protocolo,
            perfil: BlocProvider.of<PerfilBloc>(context).perfil,
          ),
        );
      },
      loading: loading,
      onClickBackButton: () {
        Navigator.of(context).pop();
      },
      active: !loading && true,
    );
  }

  // Widget _cardDocument(DocumentRequired fileAttach) {
  //   return Card(

  //       // Padding(
  //       //     padding: EdgeInsets.symmetric(vertical: 8),
  //       //     child: Text(
  //       //       fileName,
  //       //       style: TextStyle(fontSize: 16),
  //       //     )),
  //       child: Column(
  //     children: [
  //       Expanded(
  //         child: Container(
  //             alignment: Alignment.center,
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Text(
  //                   fileAttach.name ?? "",
  //                   style: TextStyle(fontSize: 16),
  //                 ),
  //                 Text(
  //                   fileAttach.required ?? false
  //                       ? "(Obrigatorio)"
  //                       : "(Opcional)",
  //                   style: TextStyle(
  //                       fontSize: 12,
  //                       color: fileAttach.required ?? false
  //                           ? UnimedColors.redCancel
  //                           : UnimedColors.grayDark2),
  //                 ),
  //               ],
  //             )),
  //       ),
  //       InkWell(
  //           onTap: () {
  //             //if (enableAddFilesButtons) _openImagesModal(context);
  //           },
  //           child: Container(
  //               color: UnimedColors.green,
  //               child: Padding(
  //                 padding: EdgeInsets.symmetric(vertical: 10),
  //                 child: Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     Icon(
  //                       Icons.note_add,
  //                       color: Colors.white,
  //                       size: 24,
  //                     ),
  //                     SizedBox(
  //                       width: 20,
  //                     ),
  //                     Text(
  //                       "ANEXAR",
  //                       style: TextStyle(
  //                         color: Colors.white,
  //                       ),
  //                     )
  //                   ],
  //                 ),
  //               )))
  //     ],
  //   ));
  // }

  // Widget _viewFile(FileAttach? file, String fileType) {
  //   return Stack(fit: StackFit.expand, children: [
  //     Image.file(
  //       file?.thumbnail ?? file!.file,
  //       fit: BoxFit.cover,
  //       filterQuality: FilterQuality.low,
  //     ),
  //     Container(
  //       color: Theme.of(context).hintColor.withOpacity(.55),
  //       child: Column(
  //         children: [
  //           Expanded(child: SizedBox()),
  //           loading
  //               ? SpinKitThreeBounce(
  //                   color: UnimedColors.white,
  //                   size: 20,
  //                 )
  //               : Expanded(
  //                   child: Icon(
  //                     Icons.image,
  //                     size: 56,
  //                     color: UnimedColors.white,
  //                   ),
  //                 ),
  //           Expanded(
  //             child: Center(
  //               child: Text(
  //                 loading ? "message" ?? "" : 'Clique para visualizar',
  //                 style: TextStyle(color: UnimedColors.white),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     )
  //   ]);
  // }
}
