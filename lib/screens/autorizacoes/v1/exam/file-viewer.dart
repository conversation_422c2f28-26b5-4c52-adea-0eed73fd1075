import 'dart:io';

import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/documents/exams_documents_state.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/document-required.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/nova_solicitacao/file_preview.dart';
import 'package:cliente_minha_unimed/screens/checkin-unity/checkin-surgery/utils.dart';
import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/handle_camera.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class FileViewer extends StatefulWidget {
  final DocumentRequired fileAttach;
  final int index;

  @override
  _FileViewerState createState() => _FileViewerState();

  FileViewer({required this.fileAttach, required this.index});
}

class _FileViewerState extends State<FileViewer> {
  bool loading = false;
  bool? sended;
  String? message;

  bool enableAddFilesButtons = true;
  late String filetype;
  late List<String> fileParts;
  late DocumentRequired fileAttach;

  @override
  void initState() {
    sended = widget.fileAttach.attachment?.sended;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    fileAttach = widget.fileAttach;
    fileParts = (widget.fileAttach.attachment?.file.path ?? "").split('.');
    filetype = fileParts.last;

    return BlocListener<AuthorizationDocumentsBloc,
            AuthorizationDocumentsState>(
        listener: (context, state) {
          if (state is LoadingDocumentState) {
            setState(() {
              sended = widget.index == state.index ? null : sended;
              loading = state.index == widget.index;
              message = state.message;
            });
          } else if (state is FileSendedState && sended != true) {
            setState(() {
              sended = widget.index == state.index ? true : sended;
              loading = false;
            });
          } else if (state is ErrorSendedState) {
            setState(() {
              sended = widget.index == state.index ? false : sended;
              loading = false;
            });
          } else if (state is DoneState) {
            setState(() {
              if (state.attachments.length > widget.index)
                sended = state.attachments[widget.index].attachment?.sended;

              loading = false;
            });
          } else {
            setState(() {
              loading = false;
            });
          }
        },
        child: Container(
            margin: EdgeInsets.symmetric(vertical: 16, horizontal: 8.0),
            child: Card(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: fileAttach.attachment != null
                        ? <Widget>[
                            if (fileAttach.name != null)
                              Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: Text(
                                    fileAttach.name ?? "",
                                    style: TextStyle(fontSize: 16),
                                  )),
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  if (!loading)
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              PreviewFileScreen(
                                            title: I18nHelper.translate(
                                              context,
                                              "checkinSurgery.title",
                                            ),
                                            file: fileAttach.attachment!.file,
                                            fileName:
                                                fileAttach.attachment!.name,
                                            fileType: filetype,
                                          ),
                                        ));
                                },
                                child: Padding(
                                  padding: EdgeInsets.all(5),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.0),
                                    child: _viewFile(
                                        fileAttach.attachment, filetype),
                                  ),
                                ),
                              ),
                            ),
                            if (!loading) _footer(filetype)
                          ]
                        : <Widget>[
                            Expanded(
                                child: loading
                                    ? Container(
                                        color: Theme.of(context)
                                            .hintColor
                                            .withOpacity(.55),
                                        child: Column(
                                          children: [
                                            Expanded(child: SizedBox()),
                                            SpinKitThreeBounce(
                                              color: UnimedColors.white,
                                              size: 20,
                                            ),
                                            Expanded(
                                              child: Center(
                                                  child: Text(
                                                message ?? '',
                                                style: TextStyle(
                                                    color: UnimedColors.white),
                                              )),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Expanded(
                                            child: Container(
                                                alignment: Alignment.center,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      fileAttach.name ?? "",
                                                      style: TextStyle(
                                                          fontSize: 16),
                                                    ),
                                                    Text(
                                                      fileAttach.required ??
                                                              false
                                                          ? "(Obrigatorio)"
                                                          : "(Opcional)",
                                                      style: TextStyle(
                                                          fontSize: 12,
                                                          color: widget
                                                                      .fileAttach
                                                                      .required ??
                                                                  false
                                                              ? UnimedColors
                                                                  .redCancel
                                                              : UnimedColors
                                                                  .grayDark2),
                                                    ),
                                                  ],
                                                )),
                                          ),
                                          InkWell(
                                              onTap: () {
                                                if (enableAddFilesButtons)
                                                  _openImagesModal(context);
                                              },
                                              child: Container(
                                                  color: UnimedColors.green,
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.note_add,
                                                          color: Colors.white,
                                                          size: 24,
                                                        ),
                                                        SizedBox(
                                                          width: 20,
                                                        ),
                                                        Text(
                                                          "ANEXAR",
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                  )))
                                        ],
                                      ))
                          ]))));
  }

  Widget _viewFile(FileAttach? file, String fileType) {
    if (fileType == 'pdf') {
      return Stack(fit: StackFit.expand, children: [
        Column(children: [
          Expanded(child: Icon(Icons.picture_as_pdf_outlined, size: 56)),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(file?.name ?? "Arquivo"))
        ]),
        if (loading)
          Container(
              color: Theme.of(context).hintColor.withOpacity(.55),
              child: Column(children: [
                Expanded(child: SizedBox()),
                SpinKitThreeBounce(color: UnimedColors.white, size: 20),
                Expanded(
                    child: Center(
                        child: Text(message ?? '',
                            style: TextStyle(color: UnimedColors.white))))
              ])),
        if (sended != null)
          Padding(
              padding: EdgeInsets.all(5),
              child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8))),
                        child: Text(
                            getStatusLabel(
                                sended!, fileAttach.attachment?.status),
                            style: TextStyle(
                                color: getStatusColor(
                                    sended!, fileAttach.attachment?.status)))),
                    Container(
                        padding: EdgeInsets.only(right: 5, top: 5, bottom: 5),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8))),
                        child: Icon(
                            getStatusBoolean(
                                    sended!, fileAttach.attachment?.status)
                                ? Icons.check_circle
                                : Icons.close,
                            color: getStatusColor(
                                sended!, fileAttach.attachment?.status),
                            size: 18))
                  ]))
      ]);
    } else {
      return Stack(fit: StackFit.expand, children: [
        Image.file(file?.thumbnail ?? file!.file,
            fit: BoxFit.cover, filterQuality: FilterQuality.low),
        Container(
            color: Theme.of(context).hintColor.withOpacity(.55),
            child: Column(children: [
              Expanded(child: SizedBox()),
              loading
                  ? SpinKitThreeBounce(color: UnimedColors.white, size: 20)
                  : Expanded(
                      child: Icon(Icons.image,
                          size: 56, color: UnimedColors.white)),
              Expanded(
                  child: Center(
                      child: Text(
                          loading ? message ?? "" : 'Clique para visualizar',
                          style: TextStyle(color: UnimedColors.white))))
            ])),
        if (sended != null)
          Padding(
              padding: EdgeInsets.all(5),
              child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        padding: EdgeInsets.only(left: 5, top: 5, bottom: 5),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8))),
                        child: Text(
                            getStatusLabel(
                                sended!, fileAttach.attachment?.status),
                            style: TextStyle(
                                color: getStatusColor(
                                    sended!, fileAttach.attachment?.status)))),
                    Container(
                        padding: EdgeInsets.only(right: 5, top: 5, bottom: 5),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8))),
                        child: Icon(
                            getStatusBoolean(
                                    sended!, fileAttach.attachment?.status)
                                ? Icons.check_circle
                                : Icons.close,
                            color: getStatusColor(
                                sended!, fileAttach.attachment?.status),
                            size: 18))
                  ]))
      ]);
    }
  }

  Future<File> _editImage(imageFile) async {
    final editedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatioPresets: Platform.isAndroid
          ? [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ]
          : [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio3x2,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio5x3,
              CropAspectRatioPreset.ratio5x4,
              CropAspectRatioPreset.ratio7x5,
              CropAspectRatioPreset.ratio16x9
            ],
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Edição',
          toolbarColor: UnimedColors.green,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
      ],
    );
    if (editedFile != null) {
      imageFile = File(editedFile.path);
    }

    return imageFile;
  }

  showRemoveDocumentAlert() {
    Alert.open(
      context,
      text:
          I18nHelper.translate(context, 'checkinSurgery.documents.remove.text'),
      title: I18nHelper.translate(
          context, 'checkinSurgery.documents.remove.title'),
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: unimedGreen,
          ),
          child: Text(I18nHelper.translate(
              context, 'checkinSurgery.documents.remove.confirm')),
          onPressed: () => {
            BlocProvider.of<AuthorizationDocumentsBloc>(context).add(
                RemoveAttachedFile(
                    index: widget.index, chave: fileAttach.attachment!.chave)),
            Navigator.pop(context)
          },
        )
      ],
    );
  }

  _showAlert(
      String title, String text, String confirmText, Function onConfirm) {
    Alert.open(
      context,
      text: text,
      title: title,
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: unimedGreen,
          ),
          child: Text(confirmText),
          onPressed: () => {onConfirm()},
        )
      ],
    );
  }

  _openImagesModal(
    BuildContext context,
  ) {
    showModalBottomSheet<void>(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
              child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 24.0, vertical: 16.0),
                  color: UnimedColors.white,
                  child:
                      Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
                    InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          _onPressCamera();
                        },
                        child: Padding(
                          padding: EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.camera_alt,
                                color: UnimedColors.green,
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                "Camera",
                                style: TextStyle(
                                  color: UnimedColors.green,
                                ),
                              )
                            ],
                          ),
                        )),
                    Divider(
                      height: 5,
                    ),
                    InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          _onPressFile();
                        },
                        child: Padding(
                          padding: EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.file_copy,
                                color: UnimedColors.green,
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                "Arquivo",
                                style: TextStyle(
                                  color: UnimedColors.green,
                                ),
                              )
                            ],
                          ),
                        )),
                  ])));
        });
  }

  _onPressCamera() async {
    if (enableAddFilesButtons)
      try {
        enableAddFilesButtons = false;

        final cameraStatus = await Permission.camera.status;

        if (cameraStatus == PermissionStatus.granted) {
          HandleCamera.openCameraScreen(context, _onTakePhoto);
        } else {
          HandleCamera.requestCameraPermission(context);
        }
      } catch (e) {
        logger.e('Error on onPressCamera on Solicitations $e');
      } finally {
        enableAddFilesButtons = true;
      }
  }

  _onTakePhoto(File? file, BuildContext context) async {
    if (file == null) {
      BlocProvider.of<AuthorizationDocumentsBloc>(context)
          .add(StopAttachedFile());
    } else {
      final compressedFile = await FileUtils.getCompressedFile(file);

      BlocProvider.of<AuthorizationDocumentsBloc>(context).add(AttachFile(
          card: BlocProvider.of<PerfilBloc>(context)
              .perfil
              .contratoBeneficiario
              .carteira!
              .carteiraNumero,
          file: file,
          type: fileAttach.name,
          id: fileAttach.id!,
          compressedFile: compressedFile,
          index: widget.index));
    }
  }

  _onPressFile() async {
    try {
      final infoJson =
          await Locator.instance.get<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

      final dataAccesStatus = Platform.isAndroid
          ? androidVersion < 13
              ? await Permission.storage.status
              : null
          : await Permission.photos.status;

      setState(() => enableAddFilesButtons = false);

      if (dataAccesStatus == PermissionStatus.granted ||
          dataAccesStatus == PermissionStatus.limited ||
          androidVersion >= 13) {
        final FilePickerResult? filePickerResult =
            await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowMultiple: false,
          allowedExtensions:
              BlocProvider.of<AuthorizationDocumentsBloc>(context)
                  .formatFilesAllowed,
        );

        if (filePickerResult?.files.first == null ||
            filePickerResult!.files.isEmpty) {
          BlocProvider.of<AuthorizationDocumentsBloc>(context)
              .add(StopAttachedFile());
        } else {
          final PlatformFile filePicked = filePickerResult.files.first;
          final File file =
              await FileUtils.copyFileOnDocuments(File(filePicked.path!));

          BlocProvider.of<AuthorizationDocumentsBloc>(context).add(
            AttachFile(
                card: BlocProvider.of<PerfilBloc>(context)
                    .perfil
                    .contratoBeneficiario
                    .carteira!
                    .carteiraNumero,
                file: file,
                type: fileAttach.name,
                id: fileAttach.id!,
                index: widget.index),
          );
        }
      } else {
        _alertPermissionDataAcces(dataAccesStatus);
      }
    } catch (e) {
    } finally {
      setState(() => enableAddFilesButtons = true);
    }
  }

  _alertPermissionDataAcces(dataAccesStatus) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Acesso aos dados',
      text: 'Precisamos acessar seus arquivos para anexar o documento',
      textButtonClose: 'Fechar',
      callbackClose: () {
        setState(() => enableAddFilesButtons = true);
      },
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.orange,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
          child: Text('Autorizar'),
          onPressed: () async {
            if (canClick) {
              setState(() {
                canClick = false;
              });

              if (dataAccesStatus == PermissionStatus.denied) {
                final infoJson = await Locator.instance
                    .get<RemoteLog>()
                    .deviceInfo
                    ?.toJson();
                final androidVersion =
                    int.parse(infoJson?['version.release'] ?? '0');

                Platform.isAndroid
                    ? androidVersion < 13
                        ? Permission.storage.request().then((_) {
                            Navigator.of(context).pop();
                          })
                        : Navigator.of(context).pop()
                    : Permission.photos.request().then((_) {
                        Navigator.of(context).pop();
                      });
              } else {
                Navigator.of(context).pop();
                openAppSettings();
              }
              setState(() => enableAddFilesButtons = true);
            }
          },
        ),
      ],
    );
  }

  Widget _footer(fileType) {
    return !(fileAttach.attachment?.sended ?? false) ||
            fileAttach.attachment?.status == "N"
        ? Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              IconButton(
                icon: Icon(Icons.delete),
                color: Colors.red,
                onPressed: () {
                  if (sended == true) {
                    _showAlert(
                        I18nHelper.translate(
                            context, 'checkinSurgery.documents.remove.title'),
                        I18nHelper.translate(
                            context, 'checkinSurgery.documents.remove.text'),
                        I18nHelper.translate(
                            context, 'checkinSurgery.documents.remove.confirm'),
                        () => {
                              BlocProvider.of<AuthorizationDocumentsBloc>(
                                      context)
                                  .add(RemoveAttachedFile(
                                      index: widget.index,
                                      chave:
                                          widget.fileAttach.attachment!.chave)),
                              Navigator.pop(context)
                            });
                    //showRemoveDocumentAlert();
                  } else {
                    BlocProvider.of<AuthorizationDocumentsBloc>(context).add(
                        RemoveAttachedFile(
                            index: widget.index,
                            chave: fileAttach.attachment!.chave));
                  }
                },
              ),
              if (fileType != 'pdf')
                SizedBox(
                  width: MediaQuery.of(context).size.width / 10,
                ),
              fileType != 'pdf'
                  ? IconButton(
                      icon: Icon(Icons.edit),
                      color: UnimedColors.grayDark,
                      onPressed: () async {
                        if (sended == true) {
                          _showAlert(
                              I18nHelper.translate(context,
                                  'checkinSurgery.documents.edit.title'),
                              I18nHelper.translate(context,
                                  'checkinSurgery.documents.edit.text'),
                              I18nHelper.translate(context,
                                  'checkinSurgery.documents.edit.confirm'),
                              () async {
                            Navigator.pop(context);
                            File newFile =
                                await _editImage(fileAttach.attachment!.file);
                            BlocProvider.of<AuthorizationDocumentsBloc>(context)
                                .add(EditAttachedFile(
                              index: widget.index,
                              chave: fileAttach.attachment!.chave,
                              file: fileAttach.attachment!.file,
                              newFile: newFile,
                              type: fileAttach.name,
                              id: fileAttach.id!,
                            ));
                          });
                        } else {
                          File newFile =
                              await _editImage(fileAttach.attachment!.file);
                          BlocProvider.of<AuthorizationDocumentsBloc>(context)
                              .add(EditAttachedFile(
                            index: widget.index,
                            chave: fileAttach.attachment!.chave,
                            file: fileAttach.attachment!.file,
                            newFile: newFile,
                            type: fileAttach.name,
                            id: fileAttach.id!,
                          ));
                        }
                      },
                    )
                  : Container(),
            ],
          )
        : Container();
  }
}
