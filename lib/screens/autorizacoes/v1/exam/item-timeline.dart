import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:flutter/material.dart';

class ItemTimeline extends StatefulWidget {
  final DetalheTimeline detalheTimeline;
  ItemTimeline({required this.detalheTimeline});
  @override
  _ItemTimelineState createState() => _ItemTimelineState();
}

class _ItemTimelineState extends State<ItemTimeline> {
  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.all(8.0),
        child: Column(
          children: <Widget>[
            Row(
              children: <Widget>[
                Flexible(
                  child: Divider(
                    color: UnimedColors.orange,
                    thickness: 1,
                  ),
                ),
                Icon(
                  Icons.adjust,
                  color: UnimedColors.orange,
                ),
                Flexible(
                  child: Divider(
                    color: UnimedColors.orange,
                    thickness: 1,
                  ),
                ),
              ],
            ),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // Text(
                      //   widget.listaDeHistStatus.atendente,
                      //   style:
                      //       TextStyle(color: UnimedColors.green, fontSize: 16),
                      // ),
                      Container(
                        width: 100,
                        child: Text(
                          widget.detalheTimeline.descStatus!,
                          style: TextStyle(color: UnimedColors.orange),
                        ),
                      ),
                    ],
                  ),
                  Text(widget.detalheTimeline.dataReferencia!)
                ]),
          ],
        ));
  }
}
