import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/electronic-guide.vo.dart';
import 'package:flutter/material.dart';

class HeaderDetails extends StatefulWidget {
  final DetalheGuiaModel guia;

  HeaderDetails({required this.guia});

  @override
  State<HeaderDetails> createState() => _HeaderDetailsState();
}

class _HeaderDetailsState extends State<HeaderDetails> {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: <Widget>[
            Text(
              widget.guia.situacao ?? "",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: ElectronicGuideStatus.setColorStatus(
                  widget.guia.situacao?.toLowerCase() ?? "Status não recebido",
                ),
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            Divider(),
            if (widget.guia.numProtocoloANS != null)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  _protocolo(),
                ],
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[_titulo(), _tituloData()],
            ),
          ],
        ),
      ),
    );
  }

  Widget _titulo() {
    if (widget.guia.codPreSolic != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Solicitação"),
          Text(
            "${widget.guia.codPreSolic!}",
            style: TextStyle(
              fontSize: 18,
              color: UnimedColors.grayDark2,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    } else if (widget.guia.numAtendimento != null) {
      return Column(
        children: [
          Text("Nº atendimento"),
          Text(
            '${widget.guia.numAtendimento}',
            style: TextStyle(
              fontSize: 18,
              color: UnimedColors.grayDark2,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  Widget _protocolo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Protocolo"),
        Text(
          "${widget.guia.numProtocoloANS ?? ""}",
          style: TextStyle(
            fontSize: 18,
            color: UnimedColors.grayDark2,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _tituloData() {
    // A data validade so aparece quando a autorização possui uma pre senha
    // por isso o tratamento pelo campo
    return widget.guia.dataValidade != null
        ? Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text("Validade"),
            Text(
              widget.guia.dataValidadeFormatted,
              style: TextStyle(
                fontSize: 18,
                color: UnimedColors.grayDark2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ])
        : Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text("Referência"),
            Text(
              widget.guia.dataReferenciaFormatted,
              style: TextStyle(
                fontSize: 18,
                color: UnimedColors.grayDark2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ]);
  }
}
