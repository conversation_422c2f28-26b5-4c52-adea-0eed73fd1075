import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_state.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/card-exam.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ExamListScreen extends StatefulWidget {
  @override
  _ExamListScreenState createState() => _ExamListScreenState();
}

class _ExamListScreenState extends State<ExamListScreen> {
  @override
  void initState() {
    BlocProvider.of<ExamBloc>(context).add(
        GetListExamsEvent(perfil: BlocProvider.of<PerfilBloc>(context).perfil));
    AnalyticsService().addLogScreenView(
      screenName: 'ExamListScreen',
      screenClass: 'ExamListScreen',
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PerfilBloc, PerfilState>(listener: (context, state) {
      if (state is LoadedProfileState) {
        BlocProvider.of<ExamBloc>(context).add(GetListExamsEvent(
            perfil: BlocProvider.of<PerfilBloc>(context).perfil));
      }
    }, builder: (context, state) {
      if (state is LoadingPerfilState) {
        return EvaLoading();
      }

      return RefreshIndicator(
        onRefresh: () async {
          BlocProvider.of<ExamBloc>(context).add(GetListExamsEvent(
              perfil: BlocProvider.of<PerfilBloc>(context).perfil));
        },
        child: BlocBuilder<ExamBloc, ExamState>(
          builder: (context, state) {
            if (state is LoadingListExamState) {
              return EvaLoading();
            } else if (state is DoneListExamState) {
              return ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: state.exams!.length,
                  itemBuilder: (BuildContext context, int index) {
                    final _item = state.exams!.elementAt(index);

                    return CardExam(
                      electronicGuide: _item,
                    );
                  });
            } else if (state is ErrorListExamState) {
              return ListView(children: [
                EvaTriste(
                  message: Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                )
              ]);
            } else if (state is NoDataExamState) {
              return ListView(children: [
                EvaTriste(
                    message: Text("Não possui registros",
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16)))
              ]);
            } else {
              return Container();
            }
          },
        ),
      );
    });
  }
}
