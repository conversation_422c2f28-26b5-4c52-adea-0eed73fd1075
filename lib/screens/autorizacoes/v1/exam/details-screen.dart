import 'package:cliente_minha_unimed/bloc/autorizacoes/guias/detalhe-guia/detalhe_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/header-details.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/status-details.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/table-exam.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/exam/timeline-attendance.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/eva-wpp-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DetailsExamScreen extends StatefulWidget {
  final GuiaModel guia;

  DetailsExamScreen({
    required this.guia,
  });

  @override
  _DetailsExamScreenState createState() => _DetailsExamScreenState();
}

class _DetailsExamScreenState extends BaseState<DetailsExamScreen> {
  final baseTranslate = "autorizations";
  final logger = UnimedLogger(className: 'DetailsExamScreen');

  @override
  void initState() {
    BlocProvider.of<DetalheBloc>(context).add(CarregarDetalheEvent(
      perfil: BlocProvider.of<PerfilBloc>(context).perfil,
      guia: widget.guia,
    ));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUnimed(
        title: Text("Detalhes da autorização"),
      ),
      body: Container(
        padding: EdgeInsets.all(8.0),
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.all(8.0),
            child: _listProcedures(),
          ),
        ),
      ),
    );
  }

  Widget _listProcedures() {
    return BlocBuilder<DetalheBloc, DetalheState>(
      builder: (context, state) {
        if (state is SuccessCarregarDetalheState) {
          return Column(
            children: <Widget>[
              // _header(state.guia),
              HeaderDetails(guia: state.guia),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Column(
                    children: <Widget>[
                      StatusDetails(
                        electronicGuide: state.guia,
                      ),
                      Text(
                        'Acompanhe os detalhes na Linha do Tempo',
                        // translate("$baseTranslate.accompanySolicitation"),
                        style:
                            TextStyle(fontSize: 12, color: UnimedColors.green),
                      ),
                      TableExam(
                        guia: state.guia,
                        // protocoloAns: widget.electronicGuide.protocoloAns,
                      ),
                      _listAttendances(state.guia),
                    ],
                  ),
                ),
              ),
              if (_checkDisplayWhatsButton(state.guia)) _btnWhatsApp(state.guia)
            ],
          );
        } else if (state is LoadingCarregarDetalheState) {
          return SpinKitThreeBounce(
            color: UnimedColors.green,
          );
        } else if (state is ErrorCarregarDetalheState) {
          return EvaTriste(
            message: Text(
              state.message,
              textAlign: TextAlign.center,
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _listAttendances(DetalheGuiaModel guia) {
    return TimelineAttendance(guia: widget.guia);
  }

  bool _checkDisplayWhatsButton(DetalheGuiaModel guia) {
    if (guia.codPreSolic == null) {
      return false;
    }

    return true;
  }

  Widget _btnWhatsApp(DetalheGuiaModel guia) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: guia.numProtocoloANS != null
          ? EvaWppButton(protocolo: guia.numProtocoloANS ?? "")
          : SizedBox(),
    );
  }
}
