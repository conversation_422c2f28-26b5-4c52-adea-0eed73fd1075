import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/eva-wpp-button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DetalhePedidoScreen extends StatefulWidget {
  const DetalhePedidoScreen({
    super.key,
    required this.pedido,
    required this.tipoPedido,
    required this.numProtocolo,
  });
  final Pedido? pedido;
  final TipoPedido tipoPedido;
  final String numProtocolo;

  @override
  State<DetalhePedidoScreen> createState() => _DetalhePedidoScreenState();
}

class _DetalhePedidoScreenState extends State<DetalhePedidoScreen> {
  @override
  void initState() {
    super.initState();
    AnalyticsService().addLogScreenView(
      screenName: 'DetalhePedidoScreen',
      screenClass: 'DetalhePedidoScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UnimedColors.greyBackground,
      appBar: AppBarUnimed(
        title: FittedBox(
          child: Column(
            children: [
              Text(
                'Pedido',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AutoSizeText(
                BlocProvider.of<PerfilBloc>(context).perfil.nomeBeneficiario ??
                    '',
                minFontSize: 10,
                maxLines: 1,
                style: TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
        backgroundColor: UnimedColors.green,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _cardStatus(),
            _cardServices(),
          ],
        ),
      ),
    );
  }

  Widget _cardStatus() {
    return Card(
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _statusNameWidget(),
            Divider(
              height: 30,
              thickness: 1,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Protocolo',
                        style: TextStyle(
                          color: UnimedColors.greyCard,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.start,
                      ),
                      SelectableText(
                        '${widget.numProtocolo}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                ),
                if (widget.pedido?.numSolicitacao != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Solicitação',
                        style: TextStyle(
                          color: UnimedColors.greyCard,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.start,
                      ),
                      SelectableText(
                        '${widget.pedido?.numSolicitacao ?? ''}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _cardServices() {
    return Card(
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.pedido?.segmentacao ?? '',
                    style: TextStyle(
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
              ],
            ),
            Divider(
              thickness: 1,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _itemView(
                  title: 'Data da solicitação',
                  description: '${widget.pedido?.dataReferenciaFormatted}',
                ),
                _itemView(
                  title: 'Prazo',
                  description:
                      '${widget.pedido?.prazo} ${widget.pedido!.prazo! < 2 ? 'dia útil' : 'dias úteis'}',
                  isAlignEnd: true,
                ),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              'Serviço',
              style: TextStyle(
                color: UnimedColors.greyCard,
              ),
            ),
            Divider(
              thickness: 1,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: (widget.pedido?.servicos ?? []).map((e) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: Text((e.descricao ?? '').toUpperCase()),
                );
              }).toList(),
            ),
            EvaWppButton(protocolo: widget.numProtocolo),
            // if (widget.tipoPedido == TipoPedido.authorized)
            //   _showAuthorization(),
          ],
        ),
      ),
    );
  }

  // Widget _showAuthorization() {
  //   return widget.pedido?.numSolicitacao != null
  //       ? Padding(
  //           padding: const EdgeInsets.only(top: 20),
  //           child: Center(
  //             child: BlocBuilder<PdfExamBloc, ExamState>(
  //               buildWhen: (previousState, state) {
  //                 if (previousState is LoadingPdf && state is DonePdf) {
  //                   if (state.codSolicitacao == widget.pedido?.numSolicitacao)
  //                     Navigator.push(
  //                       context,
  //                       MaterialPageRoute(
  //                         builder: (context) => PDFViewScreen(
  //                           state.path,
  //                           title: 'Detalhes da guia eletrônica',
  //                           isPath: true,
  //                         ),
  //                       ),
  //                     );
  //                 } else if (previousState is LoadingPdf && state is ErrorPdf) {
  //                   Alert.open(
  //                     context,
  //                     title: 'Ocorreu um erro',
  //                     text: state.message,
  //                   );
  //                 }

  //                 return true;
  //               },
  //               builder: (context, state) {
  //                 return state is LoadingPdf
  //                     ? SpinKitThreeBounce(color: UnimedColors.green)
  //                     : ElevatedButton(
  //                         style: ElevatedButton.styleFrom(
  //                           backgroundColor: UnimedColors.orange,
  //                         ),
  //                         onPressed: () {
  //                           BlocProvider.of<PdfExamBloc>(context)
  //                               .add(GeneratePdf(
  //                             cod: widget.pedido?.numSolicitacao,
  //                             perfil:
  //                                 BlocProvider.of<PerfilBloc>(context).perfil,
  //                           ));
  //                         },
  //                         child: Text("VER AUTORIZAÇÃO"),
  //                       );
  //               },
  //             ),
  //           ),
  //         )
  //       : Container();
  // }

  Widget _statusNameWidget() {
    switch (widget.tipoPedido) {
      case TipoPedido.authorized:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: UnimedColors.green,
            ),
            const SizedBox(
              width: 5,
            ),
            Text(
              'Autorizado',
              style: TextStyle(
                color: UnimedColors.green,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );
      case TipoPedido.rejected:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              color: UnimedColors.redStatus,
            ),
            const SizedBox(
              width: 5,
            ),
            Text(
              'Não Autorizado',
              style: TextStyle(
                color: UnimedColors.redStatus,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );
      default:
        return const Center();
    }
  }

  Widget _itemView({
    required String title,
    required String description,
    bool isAlignEnd = false,
    bool isSelectableText = false,
  }) {
    return Column(
      crossAxisAlignment:
          isAlignEnd ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: UnimedColors.greyCard,
            fontSize: 12,
          ),
          textAlign: TextAlign.start,
        ),
        isSelectableText
            ? SelectableText(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              )
            : Text(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              ),
      ],
    );
  }
}
