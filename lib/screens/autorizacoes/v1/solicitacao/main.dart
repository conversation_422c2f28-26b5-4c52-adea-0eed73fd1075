import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_state.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/solicitacao.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/detalhe_pedido/main.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/sensitive-data-button.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/eva-wpp-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/pdf-view/pdf-view-screen.dart';
import 'package:cliente_minha_unimed/shared/widgets/timeline.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SolicitacaoScreen extends StatefulWidget {
  const SolicitacaoScreen({
    super.key,
    required this.solicitacao,
  });
  final RetornoSolicitacaoModel solicitacao;

  @override
  State<SolicitacaoScreen> createState() => _SolicitacaoScreenState();
}

class _SolicitacaoScreenState extends State<SolicitacaoScreen> {
  String _statusSelecionado = SolicitacaoStatusFilter.EM_ANDAMENTO;
  List<String> _listFilters = ['Em andamento', 'Concluídos'];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    setState(() {
      if (((widget.solicitacao.pedidosAutorizados ?? []).isNotEmpty ||
              (widget.solicitacao.pedidosIndeferidos ?? []).isNotEmpty) &&
          (widget.solicitacao.pedidosEmAndamento ?? []).isEmpty) {
        _listFilters = ['Concluídos'];
        _statusSelecionado = SolicitacaoStatusFilter.FINALIZADO;
      } else if (((widget.solicitacao.pedidosAutorizados ?? []).isNotEmpty ||
              (widget.solicitacao.pedidosIndeferidos ?? []).isNotEmpty) &&
          (widget.solicitacao.pedidosEmAndamento ?? []).isNotEmpty) {
        _listFilters = ['Em andamento', 'Concluídos'];
        _statusSelecionado = SolicitacaoStatusFilter.FINALIZADO;
      } else if ((widget.solicitacao.pedidosAutorizados ?? []).isNotEmpty ||
          (widget.solicitacao.pedidosEmAndamento ?? []).isNotEmpty ||
          (widget.solicitacao.pedidosIndeferidos ?? []).isNotEmpty) {
        _listFilters = ['Em andamento', 'Concluídos'];
      } else {
        _listFilters = ['Recebido'];
        _statusSelecionado = SolicitacaoStatusFilter.RECEBIDO;
      }
    });

    AnalyticsService().addLogScreenView(
      screenName: 'SolicitacaoScreen',
      screenClass: 'SolicitacaoScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UnimedColors.greyBackground,
      appBar: AppBarUnimed(
        title: FittedBox(
          child: Column(
            children: [
              Text(
                'Solicitação',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return AutoSizeText(
                    state.isSensitiveDataVisible
                        ? StringUtils.formahideDatatMessage(
                            BlocProvider.of<PerfilBloc>(context)
                                    .perfil
                                    .nomeBeneficiario ??
                                '')
                        : BlocProvider.of<PerfilBloc>(context)
                                .perfil
                                .nomeBeneficiario ??
                            '',
                    minFontSize: 10,
                    maxLines: 1,
                    style: TextStyle(fontSize: 10),
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          SensitibeDataButton(),
        ],
        backgroundColor: UnimedColors.green,
      ),
      body: SingleChildScrollView(
        physics: ClampingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
        child: Column(
          children: [
            _filters(),
            Card(
              margin: EdgeInsets.all(8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                      builder: (context, state) {
                        return _itemView(
                          title: 'Protocolo',
                          description: state.isSensitiveDataVisible
                              ? StringUtils.formahideDatatMessage(
                                  '${widget.solicitacao.numProtocolo}')
                              : '${widget.solicitacao.numProtocolo}',
                          isSelectableText: true,
                        );
                      },
                    ),
                    Divider(
                      height: 30,
                      thickness: 1,
                    ),
                    if (_statusSelecionado ==
                            SolicitacaoStatusFilter.EM_ANDAMENTO ||
                        _statusSelecionado == SolicitacaoStatusFilter.RECEBIDO)
                      _pedidosEmAndamento(widget.solicitacao)
                    else if (_statusSelecionado ==
                        SolicitacaoStatusFilter.FINALIZADO)
                      _pedidosConcluidos(widget.solicitacao),
                    if (_isLoading)
                      SpinKitThreeBounce(color: UnimedColors.green)
                    else
                      EvaWppButton(
                        protocolo: widget.solicitacao.numProtocolo ?? "",
                      ),
                  ],
                ),
              ),
            ),
            if (_statusSelecionado == SolicitacaoStatusFilter.FINALIZADO) ...[
              BlocProvider<GuideSolicitationBloc>(
                create: (context) => GuideSolicitationBloc(),
                child:
                    BlocConsumer<GuideSolicitationBloc, GuideSolicitationState>(
                  listener: (context, state) {
                    setState(() {
                      _isLoading = state is LoadingGuideSolicitationState;
                    });
                    if (state is EmptyGuidesPdfState) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          duration: Duration(seconds: 3),
                          backgroundColor: UnimedColors.redStatus,
                          content: Text(
                            'Nenhuma guia disponível',
                          ),
                        ),
                      );
                    } else if (state is ErrorGuideSolicitationState) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          duration: Duration(seconds: 3),
                          backgroundColor: UnimedColors.redStatus,
                          content: Text(
                            state.message,
                          ),
                        ),
                      );
                    } else if (state is DoneGetGuidesPdfState) {
                      if (state.guidesPath.length == 1) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PDFViewScreen(
                              state.guidesPath.first,
                              title: 'Detalhes da guia',
                              isPath: true,
                            ),
                          ),
                        );
                      } else {
                        _showGuidesModal(guidesPath: state.guidesPath);
                      }
                    }
                  },
                  builder: (context, state) {
                    if (state is LoadingGuideSolicitationState) {
                      return SpinKitThreeBounce(color: UnimedColors.green);
                    }

                    return ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: UnimedColors.orange,
                      ),
                      onPressed: () {
                        BlocProvider.of<GuideSolicitationBloc>(context).add(
                          GetGuidesPdf(
                            numProtocolo: widget.solicitacao.numProtocolo ?? '',
                            numAtend: widget.solicitacao.numAtend ?? 0,
                          ),
                        );
                      },
                      child: Text("Imprimir guias"),
                    );
                  },
                ),
              ),
              const SizedBox(height: 10),
            ],
          ],
        ),
      ),
    );
  }

  Widget _pedidosEmAndamento(RetornoSolicitacaoModel solicitacao) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _pedidosWidget(
          solicitacao.pedidosEmAndamento ?? [],
          TipoPedido.inProgress,
        ),
        if ((solicitacao.etapas ?? []).isNotEmpty) ...[
          const SizedBox(height: 20),
          Text(
            'Etapas',
            style: TextStyle(
              color: UnimedColors.greenDark,
            ),
          ),
          Divider(
            height: 30,
            thickness: 1,
            color: UnimedColors.greenDark,
          ),
          _timeLine(solicitacao.etapas ?? []),
        ],
      ],
    );
  }

  Widget _pedidosConcluidos(RetornoSolicitacaoModel solicitacao) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if ((solicitacao.pedidosAutorizados ?? []).isNotEmpty) ...[
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: UnimedColors.green,
              ),
              const SizedBox(
                width: 5,
              ),
              Text(
                'Autorizados',
                style: TextStyle(
                  color: UnimedColors.green,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          _pedidosWidget(
              solicitacao.pedidosAutorizados ?? [], TipoPedido.authorized),
        ],
        if ((solicitacao.pedidosIndeferidos ?? []).isNotEmpty) ...[
          const SizedBox(
            height: 20,
          ),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: UnimedColors.redStatus,
              ),
              const SizedBox(
                width: 5,
              ),
              Text(
                'Não Autorizados',
                style: TextStyle(
                  color: UnimedColors.redStatus,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          _pedidosWidget(
              solicitacao.pedidosIndeferidos ?? [], TipoPedido.rejected),
        ],
      ],
    );
  }

  Widget _pedidosWidget(List<Pedido> pedidos, TipoPedido tipoPedido) {
    return Column(
      children: pedidos.map((e) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: InkWell(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DetalhePedidoScreen(
                  pedido: e,
                  tipoPedido: tipoPedido,
                  numProtocolo: widget.solicitacao.numProtocolo ?? '',
                ),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        e.segmentacao ?? '',
                        style: TextStyle(
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Icon(
                      Icons.keyboard_arrow_right_outlined,
                      color: UnimedColors.green,
                    )
                  ],
                ),
                const SizedBox(
                  height: 5,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _itemView(
                      title: 'Data da solicitação',
                      description: '${e.dataReferenciaFormatted}',
                    ),
                    if (e.prazo != null && tipoPedido != TipoPedido.rejected)
                      _itemView(
                        title: 'Prazo',
                        description:
                            '${e.prazo} ${e.prazo! < 2 ? 'dia útil' : 'dias úteis'}',
                        isAlignEnd: true,
                      ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Divider(
                  thickness: 1,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _timeLine(List<Etapas> etapas) {
    return Timeline(
      lineColor: UnimedColors.green,
      indicatorSize: 10,
      gutterSpacing: 10,
      itemGap: 20,
      children: etapas.map((e) {
        return Text(
          e.etapa?.mensagemReceptiva ?? '',
        );
      }).toList(),
      childrenLeft: etapas.map((e) {
        return Container(
          width: 80,
          child: Text(
            e.dataAcaoFormatted,
            textAlign: TextAlign.right,
            style: TextStyle(
              color: UnimedColors.greyCard,
            ),
          ),
        );
      }).toList(),
      indicators: etapas.map((e) {
        return Container(
          height: 1,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: UnimedColors.green,
          ),
        );
      }).toList(),
    );
  }

  void _showGuidesModal({required List<String> guidesPath}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.fromLTRB(24.0, 16.0, 24.0, 32.0),
          color: UnimedColors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                  IconButton(
                    icon: Icon(Icons.close, color: UnimedColors.green),
                    onPressed: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      }
                    },
                  )
                ]),
                const SizedBox(
                  height: 10,
                ),
                for (var index = 0; index < guidesPath.length; index++)
                  _cardFile(pathFile: guidesPath[index], index: index),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _cardFile({required String pathFile, required int index}) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFViewScreen(
              pathFile,
              title: 'Detalhes da guia',
              isPath: true,
            ),
          ),
        );
      },
      child: Container(
        // height: 100,
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16.0),
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey,
              offset: Offset(0.0, 1.0), //(x,y)
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.picture_as_pdf_outlined, size: 45),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Text('Arquivo ${index + 1}'),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right_rounded, size: 30)
          ],
        ),
      ),
    );
  }

  Widget _filters() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        height: 40,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: _listFilters.length,
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            String _key = _listFilters.elementAt(index);

            return _chipFilter(_key);
          },
        ),
      ),
    );
  }

  Widget _chipFilter(String status) {
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: InkWell(
        onTap: () {
          if (_statusSelecionado != status) {
            setState(() {
              _statusSelecionado = status;
            });
          }
        },
        child: Chip(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              color: UnimedColors.greenDark,
            ),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10),
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
            ),
          ),
          label: Text(
            status,
            style: TextStyle(
              color: status == _statusSelecionado
                  ? Colors.white
                  : UnimedColors.greenDark,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: _statusSelecionado == status
              ? UnimedColors.greenDark
              : UnimedColors.grayLight,
        ),
      ),
    );
  }

  Widget _itemView({
    required String title,
    required String description,
    bool isAlignEnd = false,
    bool isSelectableText = false,
  }) {
    return Column(
      crossAxisAlignment:
          isAlignEnd ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: UnimedColors.greyCard,
            fontSize: 12,
          ),
          textAlign: TextAlign.start,
        ),
        isSelectableText
            ? SelectableText(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              )
            : Text(
                description,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                textAlign: TextAlign.start,
              ),
      ],
    );
  }
}
