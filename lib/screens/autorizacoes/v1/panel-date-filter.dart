import 'package:flutter/material.dart';

import '../../../colors.dart';

class PanelDateFilter extends StatelessWidget {
  const PanelDateFilter({
    Key? key,
    required this.dateStartFilter,
    required this.dateEndFilter,
  }) : super(key: key);
  final DateTime dateStartFilter;
  final DateTime dateEndFilter;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 8,
        bottom: 8,
      ),
      child: Container(
        alignment: Alignment.topCenter,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8)),
          boxShadow: [
            BoxShadow(
              color: UnimedColors.grayLight2,
              blurRadius: 3.0,
              spreadRadius: 2.0,
              offset: Offset(0.0, 3.0),
            ),
          ],
        ),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 4.0, horizontal: 15),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        color: UnimedColors.green,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Período",
                                style: TextStyle(
                                    fontSize: 12,
                                    color: UnimedColors.grayLight3)),
                            Text(
                                "${dateStartFilter.day.toString().padLeft(2, '0')}/${dateStartFilter.month.toString().padLeft(2, '0')}/${dateStartFilter.year.toString().padLeft(2, '0')}",
                                style: TextStyle(color: UnimedColors.grayDark))
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      color: UnimedColors.green,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Hoje",
                            style: TextStyle(
                                fontSize: 12, color: UnimedColors.grayLight3),
                          ),
                          Text(
                              "${dateEndFilter.day.toString().padLeft(2, '0')}/${dateEndFilter.month.toString().padLeft(2, '0')}/${dateEndFilter.year.toString().padLeft(2, '0')}",
                              style: TextStyle(color: UnimedColors.grayDark)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
