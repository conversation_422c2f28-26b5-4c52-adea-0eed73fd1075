import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/pdf_exam_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/solicitacao_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AuthorizationCardGuide extends StatefulWidget {
  final GuiaModel guiaModel;
  final bool isLoading;

  AuthorizationCardGuide({
    required this.guiaModel,
    required this.isLoading,
  });

  @override
  State<AuthorizationCardGuide> createState() => _AuthorizationCardGuideState();
}

class _AuthorizationCardGuideState extends State<AuthorizationCardGuide> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      child: ClipPath(
        child: Container(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _subtitle(widget.guiaModel),
              Divider(
                height: 15,
                thickness: 1,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Icon(
                        Icons.calendar_today,
                        color: UnimedColors.grayDark,
                        size: 18,
                      ),
                      Text(
                        widget.guiaModel.dataFormatted,
                        style: TextStyle(color: UnimedColors.grayDark),
                      ),
                    ],
                  ),
                  if (widget.isLoading)
                    SpinKitThreeBounce(
                      color: UnimedColors.green,
                      size: 30,
                    )
                  else
                    _actionButton()
                ],
              ),
            ],
          ),
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(color: UnimedColors.greenDark, width: 8),
            ),
          ),
        ),
        clipper: ShapeBorderClipper(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  Widget _actionButton() {
    if (widget.guiaModel.guiaEletronica == null) {
      return widget.guiaModel.codProtocoloANS == null
          ? _printGuide(widget.guiaModel)
          : _seeDetailsButton();
    } else
      return widget.guiaModel.guiaEletronica == "S"
          ? _printGuide(widget.guiaModel)
          : _seeDetailsButton();
  }

  Widget _seeDetailsButton() {
    return InkWell(
      onTap: () {
        if (widget.guiaModel.codProtocoloANS != null) {
          BlocProvider.of<SolicitacaoBloc>(context).add(GetSolicitacao(
              numProtocolo: widget.guiaModel.codProtocoloANS ?? ''));
        } else {
          showDialog(
            context: context,
            builder: (context) {
              return UnimedAlertDialog(
                title: Text("Aviso"),
                textWidget: Text(
                  "Detalhes não disponíveis no momento.",
                  textAlign: TextAlign.center,
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
              );
            },
          );
        }
      },
      child: Row(
        children: [
          Text(
            'Ver detalhes',
            style: TextStyle(
              color: UnimedColors.green,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
          Icon(
            Icons.keyboard_arrow_right_outlined,
            color: UnimedColors.green,
          ),
        ],
      ),
    );
  }

  Widget _printGuide(GuiaModel guiaModel) {
    return guiaModel.codPreSolic != null
        ? Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Center(
              child: widget.isLoading
                  ? SpinKitThreeBounce(color: UnimedColors.green)
                  : ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: UnimedColors.orange,
                      ),
                      onPressed: () {
                        BlocProvider.of<PdfExamBloc>(context).add(
                          GeneratePdf(
                            cod: widget.guiaModel.codPreSolic,
                            perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                          ),
                        );
                      },
                      child: Text("Imprimir guia"),
                    ),
            ),
          )
        : Container();
  }
}

Widget _subtitle(GuiaModel guia) {
  if (guia.codProtocoloANS != null) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text("Protocolo", textAlign: TextAlign.start),
      BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, state) {
          return Text(
              state.isSensitiveDataVisible
                  ? StringUtils.formahideDatatMessage(guia.codProtocoloANS!)
                  : guia.codProtocoloANS!,
              textAlign: TextAlign.start);
        },
      )
    ]);
  } else if (guia.nomePrestador != null) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text("Prestador", textAlign: TextAlign.start),
      BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, state) {
          return Text(
              state.isSensitiveDataVisible
                  ? StringUtils.formahideDatatMessage(guia.nomePrestador!)
                  : guia.nomePrestador!,
              textAlign: TextAlign.start);
        },
      )
    ]);
  } else {
    return Container();
  }
}

Color statusColor(String cor) {
  return cor.trim() == "Finalizada" || cor.trim() == "Autorizada"
      ? UnimedColors.green
      : UnimedColors.orange;
}
