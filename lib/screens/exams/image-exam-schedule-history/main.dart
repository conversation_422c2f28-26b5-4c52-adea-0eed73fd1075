import 'package:cliente_minha_unimed/bloc/exams/schedule-exams/image-exam-schedule-history/image_exam_schedule_history_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/exams/image_exam_schedule.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v1/panel-date-filter.dart';
import 'package:cliente_minha_unimed/screens/exams/image-exam-schedule-history/card_image_exam.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/loading.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste.dart';
import 'package:cliente_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ImageExamScheduleHistoryScreen extends StatefulWidget {
  const ImageExamScheduleHistoryScreen({super.key});

  @override
  State<ImageExamScheduleHistoryScreen> createState() =>
      _ImageExamScheduleHistoryScreenState();
}

class _ImageExamScheduleHistoryScreenState
    extends State<ImageExamScheduleHistoryScreen> {
  DateTimeRange? _dateRangeToFilter;
  final _lastMonthsToFilter = [3, 6];
  bool _isLoading = false;
  int? _filterMonthSelected;

  @override
  void initState() {
    super.initState();
    _loadImageExamScheduleHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Agendamentos de exames de imagem'),
      ),
      body: Column(
        children: [
          _buildFilters(),
          AnimatedContainer(
            height: _dateRangeToFilter != null ? 70 : 0,
            curve: Curves.easeIn,
            duration: Duration(milliseconds: 300),
            child: _dateRangeToFilter != null
                ? PanelDateFilter(
                    dateStartFilter:
                        _dateRangeToFilter?.start ?? DateTime.now(),
                    dateEndFilter: _dateRangeToFilter?.end ?? DateTime.now(),
                  )
                : const Center(),
          ),
          Expanded(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: BlocBuilder<ImageExamScheduleHistoryBloc,
                    ImageExamScheduleHistoryState>(
                  builder: (context, state) {
                    if (state is ErrorImageExamScheduleHistory) {
                      _buildErrorWidget(errorMessage: state.message);
                    } else if (state is LoadingListImageScheduleExams) {
                      return EvaLoading();
                    } else if (state is NoDataListImageExamScheduleHistory) {
                      return _buildErrorWidget(
                          errorMessage: state.message ??
                              'Nenhum agendamento encontrado.');
                    } else if (state is LoadedImageExamScheduleHistoryExams) {
                      if (state.schedules.isEmpty) {
                        return _buildErrorWidget(
                            errorMessage: 'Nenhum agendamento encontrado.');
                      }
                      return _buildListImageScheduleExams(
                          schedules: state.schedules);
                    }
                    return const Center();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget({required String errorMessage}) {
    return RefreshIndicator(
      onRefresh: () async => _loadImageExamScheduleHistory(),
      child: ListView(
        padding: const EdgeInsets.only(top: 8.0),
        physics: ClampingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        children: [
          EvaTriste(
            message: Text(
              errorMessage,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListImageScheduleExams(
      {required List<ImageExamScheduleModel> schedules}) {
    return RefreshIndicator(
      onRefresh: () async => _loadImageExamScheduleHistory(),
      child: ListView.builder(
        itemCount: schedules.length,
        itemBuilder: (context, index) {
          final schedule = schedules[index];
          return CardImageExam(
            imageExamScheduleModel: schedule,
          );
        },
      ),
    );
  }

  Widget _buildFilters() {
    if (_isLoading) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 30),
        child: SpinKitThreeBounce(
          color: UnimedColors.green,
          size: 20,
        ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 40,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _lastMonthsToFilter.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    int _key = _lastMonthsToFilter.elementAt(index);

                    return _chipFilter(_key);
                  },
                ),
              ),
              Semantics(
                label: _dateRangeToFilter == null
                    ? 'Filtrar por data'
                    : 'Limpar filtro de data',
                child: IconButton(
                  icon: Icon(
                    _dateRangeToFilter == null
                        ? Icons.calendar_month_outlined
                        : Icons.clear_outlined,
                  ),
                  color: UnimedColors.greenDark,
                  onPressed: _dateRangeToFilter != null
                      ? () {
                          setState(() {
                            _dateRangeToFilter = null;
                            _filterMonthSelected = null;
                          });
                          _loadImageExamScheduleHistory();
                        }
                      : () async {
                          DateTime? dateStart;
                          DateTime? dateEnd;
                          dateStart = await _selectDateToFilter(
                            context,
                            'Selecione a data inicial',
                          );
                          if (dateStart != null) {
                            dateEnd = await _selectDateToFilter(
                              context,
                              'Selecione a data final',
                              firstDate: dateStart,
                            );
                          }
                          if (dateStart != null && dateEnd != null) {
                            setState(() {
                              _filterMonthSelected = 3;
                              _dateRangeToFilter = DateTimeRange(
                                start: dateStart!,
                                end: dateEnd!,
                              );
                            });
                            if (dateStart.isBefore(dateEnd)) {
                              _loadImageExamScheduleHistory();
                            } else {
                              _showMessage(
                                  'A data inicial deve ser anterior a data final');
                            }
                          }
                        },
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _chipFilter(int filterSelected) {
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: InkWell(
        onTap: () {
          if (_filterMonthSelected != filterSelected) {
            setState(() {
              _filterMonthSelected = filterSelected;
              _dateRangeToFilter = null;
            });
            final dateResult = DateTime(DateTime.now().year,
                (DateTime.now().month - filterSelected), DateTime.now().day);
            BlocProvider.of<ImageExamScheduleHistoryBloc>(context).add(
              GetImageExamHistoricSchedule(
                perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                startDate: dateResult,
                endDate: DateTime.now(),
              ),
            );
          } else {
            setState(() {
              _filterMonthSelected = null;
              _dateRangeToFilter = null;
            });
            _loadImageExamScheduleHistory();
          }
        },
        child: Chip(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              color: UnimedColors.greenDark,
            ),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10),
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
            ),
          ),
          label: Text(
            'Últimos $filterSelected meses',
            style: TextStyle(
              color: filterSelected == _filterMonthSelected &&
                      _dateRangeToFilter == null
                  ? UnimedColors.grayLight
                  : UnimedColors.greenDark,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: _filterMonthSelected == filterSelected &&
                  _dateRangeToFilter == null
              ? UnimedColors.greenDark
              : UnimedColors.grayLight,
        ),
      ),
    );
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
        context: context,
        helpText: helpText,
        initialDate: DateTime.now(),
        firstDate: firstDate ?? DateTime.now().subtract(Duration(days: 365)),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              textTheme: const TextTheme(
                labelSmall: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                ),
                headlineMedium: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            child: child!,
          );
        });
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        Snack.success(
          message,
          duration: Duration(seconds: 3),
        ),
      );
    });
  }

  void _loadImageExamScheduleHistory() {
    final startDate = _dateRangeToFilter?.start ??
        DateTime.now().subtract(const Duration(days: 3));
    final endDate = _dateRangeToFilter?.end ??
        DateTime(
          DateTime.now().year,
          (DateTime.now().month + 3),
          DateTime.now().day,
        );

    BlocProvider.of<ImageExamScheduleHistoryBloc>(context).add(
      GetImageExamHistoricSchedule(
        perfil: BlocProvider.of<PerfilBloc>(context).perfil,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }
}
