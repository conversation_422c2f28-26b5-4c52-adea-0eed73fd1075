import 'package:cliente_minha_unimed/models/conecta-saude/fha-schedule.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FHAProviderModel Tests', () {
    test('Should return "Médico" when tipoPrestador is 8', () {
      final provider = FHAProviderModel(tipoPrestador: 8);
      expect(provider.providerType, 'Médico');
    });

    test('Should return "Enfermeiro" when tipoPrestador is 4', () {
      final provider = FHAProviderModel(tipoPrestador: 4);
      expect(provider.providerType, 'Enfermeiro');
    });

    test('Should return "Tipo de prestador não definido" when tipoPrestador is null', () {
      final provider = FHAProviderModel(tipoPrestador: null);
      expect(provider.providerType, 'Tipo de prestador não definido');
    });

    test('Should return "Tipo de prestador não definido" when tipoPrestador is invalid', () {
      final provider = FHAProviderModel(tipoPrestador: 99);
      expect(provider.providerType, 'Tipo de prestador não definido');
    });

    test('Should copy FHAProviderModel with new values', () {
      final provider = FHAProviderModel(
        crm: '12345',
        name: 'Dr. João',
        tipoPrestador: 8,
        phone: '123456789',
      );

      final updatedProvider = provider.copyWith(
        name: 'Dr. Maria',
        tipoPrestador: 4,
      );

      expect(updatedProvider.name, 'Dr. Maria');
      expect(updatedProvider.tipoPrestador, 4);
      expect(updatedProvider.crm, '12345'); 
    });

    test('Should return empty specialties when schedule is null', () {
      final provider = FHAProviderModel(schedule: null);
      expect(provider.availableSpecialties, isEmpty);
    });

    test('Should return unique specialties from schedule', () {
      final schedule = [
        FHAScheduleModel(code: 1, name: 'Cardiologia'),
        FHAScheduleModel(code: 2, name: 'Pediatria'),
        FHAScheduleModel(code: 1, name: 'Cardiologia'), 
      ];

      final provider = FHAProviderModel(schedule: schedule);
      final specialties = provider.availableSpecialties;

      expect(specialties.length, 2);
      expect(specialties[0].name, 'Cardiologia');
      expect(specialties[1].name, 'Pediatria');
    });

    test('Should return empty scheduleBySpecialty when specialty is null', () {
      final provider = FHAProviderModel(schedule: []);
      final result = provider.scheduleBySpecialty(null);
      expect(result, isEmpty);
    });

    test('Should return schedule filtered by specialty', () {
      final specialty = FHASpecialtyModel(1, 'Cardiologia');
      final schedule = [
        FHAScheduleModel(code: 1, name: 'Cardiologia'),
        FHAScheduleModel(code: 2, name: 'Pediatria'),
      ];

      final provider = FHAProviderModel(schedule: schedule);
      final result = provider.scheduleBySpecialty(specialty);

      expect(result.length, 1);
      expect(result[0].name, 'Cardiologia');
    });

    test('Should serialize and deserialize FHAProviderModel  correctly doctors', () {
      final provider = FHAProviderModel(
        crm: '12345',
        name: 'Dr. João',
        tipoPrestador: 8,
        phone: '123456789',
      );

      final json = provider.toJson();
      final deserializedProvider = FHAProviderModel.fromJson(json);

      expect(deserializedProvider.crm, provider.crm);
      expect(deserializedProvider.name, provider.name);
      expect(deserializedProvider.tipoPrestador, provider.tipoPrestador);
      expect(deserializedProvider.phone, provider.phone);
    });

    test('Should serialize and deserialize FHAProviderModel correctly nurse', () {
      final provider = FHAProviderModel(
        crm: '12345',
        name: 'Enfermeira Maria',
        tipoPrestador: 4,
        phone: '123456789',
      );

      final json = provider.toJson();
      final deserializedProvider = FHAProviderModel.fromJson(json);

      expect(deserializedProvider.crm, provider.crm);
      expect(deserializedProvider.name, provider.name);
      expect(deserializedProvider.tipoPrestador, provider.tipoPrestador);
      expect(deserializedProvider.phone, provider.phone);
    });
  });
}