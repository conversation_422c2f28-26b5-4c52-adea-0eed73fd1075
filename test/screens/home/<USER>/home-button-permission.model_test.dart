import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/shared/services/permission.service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>/home-button-permission.model.dart';

void main() {
  group('HomeButtonPermission', () {
    final ProfileRoles2ScreenModel screenPermission = ProfileRoles2ScreenModel.fromJson({
      'screen': 'home',
      'roles': [
        {'key': 'financial', 'value': true},
        {'key': 'appointment', 'value': true},
        {'key': 'authorizations', 'value': true},
        {'key': 'plan', 'value': true},
        {'key': 'virtualCard', 'value': true},
        {'key': 'optional', 'value': true},
        {'key': 'evaluation', 'value': true},
        {'key': 'virtualEmergencyService', 'value': true},
        {'key': 'roberta', 'value': true},
        {'key': 'conecta-saude', 'value': true},
        {'key': 'checkin-unity', 'value': true},
        {'key': 'list-exams', 'value': true},
        {'key': 'token-authorization', 'value': true},
        {'key': 'checkin-emergencia', 'value': true},
        {'key': 'scheduling-appointment', 'value': true},
        {'key': 'biometric-registration', 'value': true},
        {'key': 'consultas-virtuais', 'value': true},
        {'key': 'notificacao-digital', 'value': true},
        {'key': 'update-contacts', 'value': false},
        {'key': 'in-person-service', 'value': true},
        {'key': 'medical-guide-portal', 'value': true},
        {'key': 'res', 'value': true},
        {'key': 'club-mais-vantagens', 'value': true}
      ]
    });

    test('canShow retorna true quando forceShow é true', () {
      final model = HomeButtonPermission(
        permission: PermissionProfileWidgetRes(),
        screenPermissions: screenPermission,
        onPressed: () {},
        forceShow: true,
      );

      expect(model.canShow, isTrue);
    });

    test('canShow retorna false quando disabled é true', () {
      final model = HomeButtonPermission(
        permission: PermissionProfileWidgetRes(),
        screenPermissions: screenPermission,
        onPressed: () {},
        disabled: true,
      );

      expect(model.canShow, isFalse);
    });

    test(
        'canShow retorna true quando forceShow é false, disabled é false, e a lógica de permissão permite',
        () {
      final model = HomeButtonPermission(
        permission: PermissionProfileWidgetRes(),
        screenPermissions: screenPermission,
        onPressed: () {},
        disabled: false,
        forceShow: false,
      );

      // Simula a lógica de permissão permitindo que o widget seja exibido
      expect(model.canShow, isTrue);
    });

    test('erro de asserção quando label e widgetLabel são nulos', () {
      expect(
        () => HomeButtonPermission(
          permission: PermissionProfileWidgetRes(),
          screenPermissions: screenPermission,
          onPressed: () {},
          label: null,
          widgetLabel: null,
        ),
        throwsAssertionError,
      );
    });

    test('valores padrão são definidos corretamente', () {
      final model = HomeButtonPermission(
        permission: PermissionProfileWidgetRes(),
        screenPermissions: screenPermission,
        onPressed: () {},
      );

      expect(model.label, equals("SEM LABEL"));
      expect(model.disabled, isFalse);
      expect(model.forceShow, isFalse);
      expect(model.key, isNull);
      expect(model.order, isNull);
      expect(model.description, isNull);
    });
  });
}
