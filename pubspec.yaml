name: cliente_minha_unimed
description: <PERSON><PERSON> <PERSON><PERSON> Unimed Cliente.
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 6.2.2+622002

environment:
  sdk: ">=3.0.5 <4.0.0"
  flutter: "3.24.3"

dependency_overrides:
  geolocator_android: 4.6.1 # problemas com o geolocator_android 4.6.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.4
  device_info_plus: ^10.1.2
  shared_preferences: ^2.2.1
  encrypt: 5.0.0 #Deve ser fixado nessa versão, pois a mais recente dá erro
  path: ^1.8.3
  package_info_plus: ^8.0.0
  http: ^1.1.0
  crypto: ^3.0.1
  intl: ^0.19.0
  local_auth: ^2.1.7
  flutter_html: ^3.0.0-beta.2
  google_maps_flutter: ^2.5.0
  location_permissions: ^4.0.1 # DESCONTINUADO
  geolocator: ^13.0.3
  mask_text_input_formatter: ^2.5.0
  extended_masked_text: ^2.3.1
  image_picker: ^1.0.7
  archive: ^4.0.2
  path_provider: ^2.1.1
  uuid: ^4.5.1
  logger: ^2.0.2+1
  flutter_markdown: ^0.6.17+3
  flutter_spinkit: ^5.2.0
  money2: ^4.0.0
  flutter_pdfview: ^1.3.1
  numberpicker: ^2.1.2
  image_cropper: ^5.0.1
  webview_flutter: ^3.0.4
  badges: ^3.1.2
  flutter_parsed_text: ^2.2.1
  permission_handler: ^11.0.1
  camera: ^0.10.5+4
  sqflite: ^2.3.0
  table_calendar: ^3.0.3
  cached_network_image: ^3.2.1
  dbcrypt: ^2.0.0
  flutter_i18n: ^0.35.0
  auto_size_text: ^3.0.0 # null-safety
  flutter_image_compress: ^2.0.0
  share: ^2.0.4 # DESCONTINUADO
  collection: ^1.17.2
  vibration: ^1.8.2
  screenshot: ^2.1.0
  fl_chart: ^0.63.0
  graphql: ^5.1.3

  # bloc packages
  flutter_bloc: ^7.3.3
  equatable: ^2.0.3
  hydrated_bloc: ^7.1.0

  qr_flutter: 4.0.0 # Dependencia exata

  # firebase_core: ^1.10.0
  firebase_core: ^3.10.0
  firebase_messaging: ^15.2.0
  cloud_firestore: ^5.5.0
  firebase_analytics: ^11.4.6
  firebase_database: ^11.3.2
  #firebase_crashlytics: ^3.0.11

  flare_flutter: ^3.0.2 # null-safety

  file_picker: ^6.1.1
  json_annotation: ^4.4.0

  qr_code_scanner:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: qr_code_scanner
      ref: qr_code_scanner-v1.0.0

  google_map_polyline:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: google_map_polyline
      ref: google_map_polyline-v0.3.0

  header_login:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: header_login
      ref: header_login-v1.0.6

  evaluation:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: evaluation
      ref: evalution-v5.0.0

  unimed_select:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: unimed_select
      ref: unimed_select-v1.1.1

  websocket_service:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: websocket_service
      ref: websocket_service-v1.0.0

  speech_bubble:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: speech_bubble
      ref: speech_bubble-v1.0.1

  splash_unimed:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: splash_unimed
      ref: splash_unimed-v2.0.0

  pa_virtual:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: pa_virtual
      ref: pa_virtual-v7.1.4

  http_client:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: http_client
      ref: http_client-v2.0.0

  password_validation:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: password_validation
      ref: password_validation-v1.0.0

  url_launcher:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: url_launcher
  flutter_svg: ^2.0.10+1

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  json_serializable: ^6.0.1 # null-safety
  build_runner: ^2.1.5 # null-safety
  mockito: ^5.0.17

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path_android: "assets/icon/1024x1024_android.png"
  image_path_ios: "assets/icon/1024x1024_ios.png"

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/pin_maps/
    - assets/i18n/
    - assets/images/feature_discovery/ve_service/
    - assets/animations/
    - assets/svg/

  fonts:
    - family: UnimedSans
      fonts:
        - asset: assets/fonts/UnimedSans-Regular.otf
        - asset: assets/fonts/UnimedSans-RegularItalic.otf
          style: italic
    - family: UnimedSlab
      fonts:
        - asset: assets/fonts/unimed-slab/UnimedSlab-Regular.otf
    - family: UnimedIcons
      fonts:
        - asset: assets/fonts/UnimedIcons.ttf
    - family: uicons
      fonts:
        - asset: assets/icomoon/fonts/uicons.ttf
    - family: ConectaSaudeIcons
      fonts:
        - asset: assets/fonts/conecta-saude/ConectaSaudeIcons.ttf
    - family: UnimedSansBold
      fonts:
        - asset: assets/fonts/UnimedSans2020-Bd.otf
